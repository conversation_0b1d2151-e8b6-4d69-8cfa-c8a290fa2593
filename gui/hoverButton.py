from PySide6.QtCore import QSize
from PySide6.QtWidgets import QPushButton


class HoverButton(QPushButton):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.default_size = QSize(35, 35)
        self.hover_size = QSize(40, 40)  # Bigger size for hover state
        self.setIconSize(self.default_size)

    def enterEvent(self, event):
        self.setIconSize(self.hover_size)
        current_size = self.size()
        self.setFixedSize(current_size.width() + 2, current_size.height() + 1)
        super().enterEvent(event)

    def leaveEvent(self, event):
        self.setIconSize(self.default_size)
        current_size = self.size()
        self.setFixedSize(current_size.width() - 2, current_size.height() - 1)
        super().leaveEvent(event)