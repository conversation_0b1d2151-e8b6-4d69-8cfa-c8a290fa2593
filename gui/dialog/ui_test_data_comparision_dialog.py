# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'test_data_comparision_dialogLragub.ui'
##
## Created by: Qt User Interface Compiler version 6.8.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QDialog, QFrame,
    QHBoxLayout, QHeaderView, QLineEdit, QSizePolicy,
    QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget)

class Ui_testComparisonDialog(object):
    def setupUi(self, testComparisonDialog):
        if not testComparisonDialog.objectName():
            testComparisonDialog.setObjectName(u"testComparisonDialog")
        testComparisonDialog.resize(750, 800)
        testComparisonDialog.setMinimumSize(QSize(750, 800))
        testComparisonDialog.setMaximumSize(QSize(750, 1000))
        self.verticalLayout = QVBoxLayout(testComparisonDialog)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.frame_80 = QFrame(testComparisonDialog)
        self.frame_80.setObjectName(u"frame_80")
        self.frame_80.setStyleSheet(u"QFrame #frame_80{\n"
"background-color: #18181b;\n"
"border-radius: 10px;\n"
"border: 1px solid #000000;}")
        self.frame_80.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_80.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_69 = QHBoxLayout(self.frame_80)
        self.horizontalLayout_69.setObjectName(u"horizontalLayout_69")
        self.tableFrame_2 = QFrame(self.frame_80)
        self.tableFrame_2.setObjectName(u"tableFrame_2")
        self.tableFrame_2.setStyleSheet(u"background-color:#18181b;\n"
"border-radius: 10px;")
        self.tableFrame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.tableFrame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_63 = QHBoxLayout(self.tableFrame_2)
        self.horizontalLayout_63.setObjectName(u"horizontalLayout_63")
        self.results_table_compare = QTableWidget(self.tableFrame_2)
        if (self.results_table_compare.columnCount() < 6):
            self.results_table_compare.setColumnCount(6)
        __qtablewidgetitem = QTableWidgetItem()
        self.results_table_compare.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.results_table_compare.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.results_table_compare.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.results_table_compare.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.results_table_compare.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.results_table_compare.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        self.results_table_compare.setObjectName(u"results_table_compare")
        self.results_table_compare.setMinimumSize(QSize(550, 0))
        self.results_table_compare.setStyleSheet(u"QTableWidget {\n"
"                    background-color: #18181b;\n"
"                    color: white;\n"
"                    gridline-color: #446699;\n"
"                   border-radius: 10px;\n"
"					border:none;\n"
"                }\n"
"                QHeaderView::section {\n"
"                    background-color: #1e1e1e;\n"
"                    color: white;\n"
"                    padding: 5px;\n"
"                    border: 2px solid #0c9486;\n"
"					 border-radius:5px;\n"
"					\n"
"                }\n"
"                QTableWidget::item {\n"
"                    padding: 5px;\n"
"                }\n"
"                QTableWidget::item:selected {\n"
"                    background-color: #F2613F;\n"
"                }")
        self.results_table_compare.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.results_table_compare.setDragDropOverwriteMode(False)
        self.results_table_compare.setDragDropMode(QAbstractItemView.DragDropMode.NoDragDrop)
        self.results_table_compare.setAlternatingRowColors(False)
        self.results_table_compare.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.results_table_compare.setSortingEnabled(False)
        self.results_table_compare.setWordWrap(True)
        self.results_table_compare.setColumnCount(6)

        self.horizontalLayout_63.addWidget(self.results_table_compare)


        self.horizontalLayout_69.addWidget(self.tableFrame_2)


        self.verticalLayout.addWidget(self.frame_80)

        self.widget_5 = QWidget(testComparisonDialog)
        self.widget_5.setObjectName(u"widget_5")
        self.widget_5.setMinimumSize(QSize(0, 50))
        self.widget_5.setMaximumSize(QSize(16777215, 16777215))
        self.widget_5.setStyleSheet(u"background-color: transparent;")
        self.horizontalLayout_64 = QHBoxLayout(self.widget_5)
        self.horizontalLayout_64.setObjectName(u"horizontalLayout_64")
        self.search_tab = QLineEdit(self.widget_5)
        self.search_tab.setObjectName(u"search_tab")
        self.search_tab.setMinimumSize(QSize(170, 35))
        self.search_tab.setMaximumSize(QSize(270, 16777215))
        self.search_tab.setStyleSheet(u"border-radius:16px;\n"
"padding:1px 10px;\n"
"font: 14px;\n"
"background-color:#Fffdd0;\n"
"font-size:14px;\n"
"color: black;\n"
"font-weight: bold;")
        self.search_tab.setFrame(False)
        self.search_tab.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.search_tab.setClearButtonEnabled(False)

        self.horizontalLayout_64.addWidget(self.search_tab)


        self.verticalLayout.addWidget(self.widget_5)


        self.retranslateUi(testComparisonDialog)

        QMetaObject.connectSlotsByName(testComparisonDialog)
    # setupUi

    def retranslateUi(self, testComparisonDialog):
        testComparisonDialog.setWindowTitle(QCoreApplication.translate("testComparisonDialog", u"Dialog", None))
        ___qtablewidgetitem = self.results_table_compare.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("testComparisonDialog", u"Test Number", None));
        ___qtablewidgetitem1 = self.results_table_compare.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("testComparisonDialog", u"Test Date", None));
        ___qtablewidgetitem2 = self.results_table_compare.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("testComparisonDialog", u"Catalyst", None));
        ___qtablewidgetitem3 = self.results_table_compare.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("testComparisonDialog", u"Propellant Conc.", None));
        ___qtablewidgetitem4 = self.results_table_compare.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("testComparisonDialog", u"Database", None));
        ___qtablewidgetitem5 = self.results_table_compare.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("testComparisonDialog", u"Cut-off Temp", None));
        self.search_tab.setInputMask("")
        self.search_tab.setText("")
    # retranslateUi

