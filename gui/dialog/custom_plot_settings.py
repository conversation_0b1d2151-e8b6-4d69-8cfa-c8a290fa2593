import os
import logging
from typing import Dict, Any, Optional, List, <PERSON><PERSON>

import numpy as np
from PIL import ImageFont
from PySide6 import QtGui, QtWidgets
from PySide6.QtGui import QIcon, QFont, QFontMetricsF
from matplotlib.backends.backend_qt5agg import NavigationToolbar2Q<PERSON> as Navigation<PERSON>oolbar
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QDialog, QPushButton, QColorDialog, QFileDialog
from PySide6.QtCore import Qt, QSize
from matplotlib.font_manager import FontProperties
from scipy.stats import linregress

from .ui_plot_settings import Ui_Plot_settings_Dialog
from src.utils import get_resource_path
from .. import MultiStateToggleSwitch

# Set up logging
logger = logging.getLogger(__name__)


class PlotSettingsManager:
    """Centralized manager for plot settings and data frame operations"""

    def __init__(self):
        self.settings: Dict[str, Any] = {}
        self.data_frame_1: Dict = {}
        self.data_frame_2: Dict = {}

    def initialize_data_frames(self, data_frame: Any) -> Tuple[Dict, Dict]:
        """Safely initialize data frames from input parameter"""
        if not isinstance(data_frame, dict):
            return data_frame or {}, {}

        # Extract data_frame_1
        df1 = self._extract_data_frame(data_frame, 'data_frame_1') or data_frame
        # Extract data_frame_2
        df2 = self._extract_data_frame(data_frame, 'data_frame_2')

        self.data_frame_1 = df1 or {}
        self.data_frame_2 = df2 or {}

        return self.data_frame_1, self.data_frame_2

    def _extract_data_frame(self, data_frame: Dict, key: str) -> Optional[Dict]:
        """Extract specific data frame from nested structure"""
        if key in data_frame:
            return data_frame[key]

        # Look in nested dictionaries
        for value in data_frame.values():
            if isinstance(value, dict) and key in value:
                return value[key]

        return None

    def safe_convert(self, value: Any, target_type: type, default: Any) -> Any:
        """Safely convert value to target type with fallback"""
        try:
            if value is None:
                return default
            if target_type == int:
                return int(float(value))  # Handle string numbers
            return target_type(value)
        except (ValueError, TypeError):
            return default

    def extract_plot_settings(self, ax, ax_2=None) -> Dict[str, Any]:
        """Extract current plot settings into structured dictionary"""
        settings = {}

        try:
            # Title settings
            title = ax.title
            settings.update({
                'title_font': title.get_fontname() or 'Arial',
                'title_size': self.safe_convert(title.get_fontsize(), int, 12),
                'title_color': title.get_color() or '#000000'
            })

            # Label settings
            xlabel = ax.xaxis.get_label()
            settings.update({
                'label_font': xlabel.get_fontname() or 'Arial',
                'label_size': self.safe_convert(xlabel.get_fontsize(), int, 10),
                'label_color': xlabel.get_color() or '#000000'
            })

            # Legend settings
            if ax.legend_ is not None:
                legend_texts = ax.legend_.get_texts()
                if legend_texts:
                    text = legend_texts[0]
                    settings.update({
                        'legend_font': text.get_fontname() or 'Arial',
                        'legend_size': self.safe_convert(text.get_fontsize(), int, 10),
                        'legend_color': text.get_color() or '#000000'
                    })

            # Axis limits
            x_min, x_max = ax.get_xlim()
            y_min, y_max = ax.get_ylim()
            settings.update({
                'x_axis_min': x_min,
                'x_axis_max': x_max,
                'y_axis_min': y_min,
                'y_axis_max': y_max
            })

            if ax_2:
                y2_min, y2_max = ax_2.get_ylim()
                settings.update({
                    'y2_axis_min': y2_min,
                    'y2_axis_max': y2_max
                })

            # Data series settings
            self._extract_series_settings(ax, settings, self.data_frame_1)
            if ax_2:
                self._extract_series_settings(ax_2, settings, self.data_frame_2)

            # Grid settings
            settings.update({
                'major_grid_visible': self._check_grid_visibility(ax, 'major'),
                'minor_grid_visible': self._check_grid_visibility(ax, 'minor'),
                'major_grid_color': self._get_grid_color(ax, 'major'),
                'minor_grid_color': self._get_grid_color(ax, 'minor')
            })

            print(f"The minor grid visibility is: {settings['minor_grid_visible']}")

        except Exception as e:
            logger.error(f"Error extracting plot settings: {e}")

        self.settings = settings
        return settings

    def _extract_series_settings(self, ax, settings: Dict, data_frame: Dict):
        """Extract settings for data series from axis"""
        for line in ax.get_lines():
            series_name = line.get_label()
            if series_name and series_name in data_frame:
                settings[f'data_series_color_{series_name}'] = line.get_color() or '#008000'
                if f'legend_name_{series_name}' not in settings:
                    settings[f'legend_name_{series_name}'] = series_name

    def _check_grid_visibility(self, ax, which: str = 'major') -> bool:
        """Check if grid is visible for major or minor ticks."""
        try:
            if which == 'major':
                xticks = ax.xaxis.get_major_ticks()
                yticks = ax.yaxis.get_major_ticks()
            else:
                xticks = ax.xaxis.get_minor_ticks()
                yticks = ax.yaxis.get_minor_ticks()

            # Gather gridlines from tick objects
            lines = []
            for tick in xticks + yticks:
                line_x = tick.gridline
                line_y = getattr(tick, '_gridline2', None)  # depends on backend
                for line in (line_x, line_y):
                    if line and line.get_visible():
                        lines.append(line)

            return len(lines) > 0
        except Exception as e:
            logger.warning(f"Error checking {which} grid visibility: {e}")
            return False

    # def _get_grid_color(self, ax, which: str = 'major') -> str:
    #     """Get grid color"""
    #     try:
    #         gridlines = ax.get_xgridlines() + ax.get_ygridlines()
    #         if which == 'major':
    #             lines = [line for line in gridlines if line.get_visible() and
    #                      line.get_linestyle() in ['-', 'solid']]
    #         else:  # minor
    #             lines = [line for line in gridlines if line.get_visible() and
    #                      line.get_linestyle() in ['--', ':', 'dashed', 'dotted']]
    #
    #         if lines:
    #             return lines[0].get_color()
    #     except Exception as e:
    #         logger.warning(f"Error getting {which} grid color: {e}")
    #     return '#008000'

    def _get_grid_color(self, ax, which: str = 'major') -> str:
        """Get gridline color for major or minor grid."""
        try:
            # Force rendering to make sure all gridlines exist
            ax.figure.canvas.draw()

            # Select the correct ticks
            if which == 'major':
                xticks = ax.xaxis.get_major_ticks()
                yticks = ax.yaxis.get_major_ticks()
            else:
                xticks = ax.xaxis.get_minor_ticks()
                yticks = ax.yaxis.get_minor_ticks()

            for tick in xticks + yticks:
                # Try both gridlines (some versions have _gridline2)
                for line in (getattr(tick, 'gridline', None), getattr(tick, '_gridline2', None)):
                    if line and line.get_visible():
                        return line.get_color()
        except Exception as e:
            logger.warning(f"Error getting {which} grid color: {e}")

        # Default fallback
        return '#008000'


class StylesheetManager:
    """Centralized stylesheet management"""

    @staticmethod
    def get_base_paths() -> Dict[str, str]:
        """Get all required asset paths"""
        return {
            'down_arrow': get_resource_path("assets/down-arrow.png").replace('\\', '/'),
            'up_arrow': get_resource_path("assets/up-arrow.png").replace('\\', '/'),
            'checkbox_checked': get_resource_path("assets/checkbox_checked.png").replace('\\', '/'),
            'checkbox_unchecked': get_resource_path("assets/checkbox_unchecked.png").replace('\\', '/'),
        }

    @classmethod
    def get_toolbar_stylesheet(cls) -> str:
        """Generate stylesheet for toolbar"""
        paths = cls.get_base_paths()
        return f"""
            QDialog {{
                background-color: #171717;
            }}
            QLabel {{
                color: black;
                padding: 1px 50px;
                font-family: Arial;
                font-size: 14px;
            }}
            QLineEdit {{
                background-color: #1C1C1C;
                border: 1px solid #303030;
                border-radius: 10px;
                color: #EEEEEE;
                font-size: 16px;
                padding: 4px 15px;
            }}
            {cls._get_combobox_style(paths['down_arrow'])}
            {cls._get_checkbox_style(paths['checkbox_checked'], paths['checkbox_unchecked'])}
            {cls._get_tab_style()}
        """

    @classmethod
    def get_dialog_stylesheet(cls) -> str:
        """Generate stylesheet for dialogs"""
        paths = cls.get_base_paths()
        return f"""
            QDialog {{
                background-color: #171717;
            }}
            QWidget {{
                background-color: #171717;
                color: #EEEEEE;
            }}
            QPushButton {{
                background-color: #1C1C1C;
                color: white;
                font-family: inter;
                font-size: 14px;
                border-radius: 5px;
            }}
            QPushButton:hover {{
                background-color: #252525;
            }}
            QLineEdit {{
                background-color: #1C1C1C;
                border: 1px solid #303030;
                border-radius: 10px;
                color: #EEEEEE;
                font-size: 16px;
                padding: 4px 15px;
            }}
            {cls._get_combobox_style(paths['down_arrow'])}
            {cls._get_spinbox_style(paths['up_arrow'], paths['down_arrow'])}
            {cls._get_checkbox_style(paths['checkbox_checked'], paths['checkbox_unchecked'])}
            QLabel {{
                color: #AAAAAA;
                font-size: 16px;
                font-family: inter;
            }}
        """

    @staticmethod
    def _get_combobox_style(down_arrow_path: str) -> str:
        """Get combobox specific styles"""
        return f"""
            QComboBox {{
                border: 1px solid #303030;
                border-radius: 10px;
                padding: 4px 15px;
                background: #1C1C1C;
                color: #EEEEEE;
                font-size: 16px;
            }}
            QComboBox::drop-down {{
                border: none;
                background: transparent;
                width: 20px;
                margin-right: 8px;
            }}
            QComboBox::down-arrow {{
                image: url("{down_arrow_path}");
                width: 24px;
                height: 24px;
            }}
        """

    @staticmethod
    def _get_spinbox_style(up_arrow_path: str, down_arrow_path: str) -> str:
        """Get spinbox specific styles"""
        return f"""
            QDoubleSpinBox {{
                border: 1px solid #303030;
                border-radius: 10px;
                padding: 4px 15px;
                background: #1C1C1C;
                color: #EEEEEE;
                font-size: 16px;
            }}
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {{
                border: none;
                background: transparent;
                width: 20px;
                margin-right: 8px;
            }}
            QDoubleSpinBox::up-arrow {{
                image: url("{up_arrow_path}");
                width: 24px;
                height: 24px;
            }}
            QDoubleSpinBox::down-arrow {{
                image: url("{down_arrow_path}");
                width: 24px;
                height: 24px;
            }}
        """

    @staticmethod
    def _get_checkbox_style(checked_path: str, unchecked_path: str) -> str:
        """Get checkbox specific styles"""
        return f"""
            QCheckBox {{
                color: #444444;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:checked {{
                image: url("{checked_path}");
            }}
            QCheckBox::indicator:unchecked {{
                image: url("{unchecked_path}");
            }}
        """

    @staticmethod
    def _get_tab_style() -> str:
        """Get tab widget specific styles"""
        return """
            QTabWidget {
                background-color: transparent;
                border: none;
                border-radius: 10px;
            }
            QWidget {
                background-color: transparent;
            }
            QTabWidget::tab-bar {
                alignment: center;
            }
            QTabBar::tab {
                border-radius: 5px;
                width: 120px;
                height: 20px;
                color: black;
                font-size: 16px;
                font-family: inter;
                padding: 2px;
                background-color: #ffffff;
            }
            QTabBar::tab:selected {
                background: black;
                color: white;
            }
            QTabBar::tab:hover {
                background: #787878;
            }
            QTabWidget::pane {
                border: none;
                background-color: transparent;
                border-radius: 10px;
            }
        """


class PlotRenderer:
    """Handles plot rendering and visual updates"""

    def __init__(self, figure, ax, ax_2=None):
        self.figure = figure
        self.ax = ax
        self.ax_2 = ax_2

    def apply_settings(self, settings: Dict[str, Any], data_frame_1: Dict, data_frame_2: Dict):
        """Apply all settings to the plot"""
        try:
            self._apply_title_settings(settings)
            self._apply_label_settings(settings)
            self._apply_legend_settings(settings)
            self._apply_series_settings(settings, data_frame_1, data_frame_2)
            self._apply_grid_settings(settings)
            self._apply_axis_limits(settings)

            self.figure.tight_layout()
            self.figure.canvas.draw()

        except Exception as e:
            logger.error(f"Error applying settings: {e}")

    def _apply_title_settings(self, settings: Dict[str, Any]):
        """Apply title formatting settings"""
        self.ax.title.set_fontname(settings.get('title_font', 'Arial'))
        self.ax.title.set_fontsize(settings.get('title_size', 12))
        self.ax.title.set_color(settings.get('title_color', '#000000'))

    def _apply_label_settings(self, settings: Dict[str, Any]):
        """Apply label formatting settings"""
        label_font = settings.get('label_font', 'Arial')
        label_size = settings.get('label_size', 10)
        label_color = settings.get('label_color', '#000000')

        self.ax.set_xlabel(self.ax.get_xlabel(), fontname=label_font,
                           fontsize=label_size, color=label_color)
        self.ax.set_ylabel(self.ax.get_ylabel(), fontname=label_font,
                           fontsize=label_size, color=label_color)

        if self.ax_2:
            self.ax_2.set_ylabel(self.ax_2.get_ylabel(), fontname=label_font,
                                 fontsize=label_size, color=label_color)

    def _apply_legend_settings(self, settings: Dict[str, Any]):
        """Apply legend formatting settings"""
        legend_font = settings.get('legend_font', 'Arial')
        legend_size = settings.get('legend_size', 10)
        legend_color = settings.get('legend_color', '#000000')

        font_props = FontProperties(family=legend_font, size=legend_size)

        for ax in [self.ax, self.ax_2]:
            if ax and ax.legend_:
                handles, labels = ax.get_legend_handles_labels()
                ax.legend_.remove()

                if handles and labels:
                    if ax == self.ax:
                        legend = ax.legend(handles, labels,
                                           bbox_to_anchor=(0.5, -0.13),
                                           loc='upper center',
                                           ncol=min(5, len(labels)),
                                           columnspacing=1,
                                           handletextpad=0.5,
                                           borderaxespad=0,
                                           facecolor='none',
                                           edgecolor='#446699',
                                           prop=font_props,
                                           bbox_transform=ax.transAxes)
                    else:
                        legend = ax.legend(bbox_to_anchor=(0.5, -0.2),
                                           loc='upper center',
                                           ncol=5,
                                           columnspacing=1,
                                           handletextpad=0.5,
                                           borderaxespad=0,
                                           facecolor='none',
                                           edgecolor='#446699',
                                           prop=font_props,
                                           bbox_transform=ax.transAxes)

                    for text in legend.get_texts():
                        text.set_color(legend_color)

    def _apply_series_settings(self, settings: Dict[str, Any], data_frame_1: Dict, data_frame_2: Dict):
        """Apply data series color and label settings"""
        # Apply to primary axis
        for line in self.ax.get_lines():
            series_name = line.get_label()
            if series_name and series_name in data_frame_1:
                color = settings.get(f'data_series_color_{series_name}', '#008000')
                legend_name = settings.get(f'legend_name_{series_name}', series_name)
                line.set_color(color)
                line.set_label(legend_name)

        # Apply to secondary axis
        if self.ax_2:
            for line in self.ax_2.get_lines():
                series_name = line.get_label()
                if series_name and series_name in data_frame_2:
                    color = settings.get(f'data_series_color_{series_name}', '#008000')
                    legend_name = settings.get(f'legend_name_{series_name}', series_name)
                    line.set_color(color)
                    line.set_label(legend_name)

    def _apply_grid_settings(self, settings: Dict[str, Any]):
        """Apply grid settings"""
        major_visible = settings.get('major_grid_visible', False)
        minor_visible = settings.get('minor_grid_visible', False)
        major_color = settings.get('major_grid_color', '#008000')
        minor_color = settings.get('minor_grid_color', '#008000')

        if major_visible:
            self.ax.grid(which='major', visible=True, color=major_color, linestyle='-', alpha=0.7)
        else:
            self.ax.grid(which='major', visible=False)

        if minor_visible:
            self.ax.minorticks_on()
            self.ax.grid(which='minor', visible=True, color=minor_color, linestyle=':', alpha=0.5)
        else:
            self.ax.grid(which='minor', visible=False)
            self.ax.minorticks_off()

    def _apply_axis_limits(self, settings: Dict[str, Any]):
        """Apply axis limits"""
        if 'x_axis_min' in settings and 'x_axis_max' in settings:
            self.ax.set_xlim(settings['x_axis_min'], settings['x_axis_max'])

        if 'y_axis_min' in settings and 'y_axis_max' in settings:
            self.ax.set_ylim(settings['y_axis_min'], settings['y_axis_max'])

        if self.ax_2 and 'y2_axis_min' in settings and 'y2_axis_max' in settings:
            self.ax_2.set_ylim(settings['y2_axis_min'], settings['y2_axis_max'])


class CustomNavigationToolbar(NavigationToolbar):
    """Custom navigation toolbar with integrated plot settings"""

    def __init__(self, canvas, parent=None, data_frame=None, data_series_1=None, data_series_2=None):
        super().__init__(canvas, parent)

        self.canvas = canvas
        self.parent = parent
        self.data_series_1 = data_series_1
        self.data_series_2 = data_series_2

        # Initialize managers
        self.settings_manager = PlotSettingsManager()
        self.data_frame_1, self.data_frame_2 = self.settings_manager.initialize_data_frames(data_frame)

        # Setup UI
        self.setIconSize(QSize(24, 24))
        self.setup_custom_icons()
        self._add_settings_button()
        self.setStyleSheet(StylesheetManager.get_toolbar_stylesheet())

    def _add_settings_button(self):
        """Add custom settings button to toolbar"""
        self.addSeparator()
        settings_btn = QPushButton()
        settings_icon = QIcon()
        settings_icon_path = get_resource_path("assets/advanced_settings.png")
        settings_icon.addFile(settings_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        settings_btn.setIcon(settings_icon)
        settings_btn.setIconSize(QSize(25, 25))
        settings_btn.setToolTip('Plot Settings')
        settings_btn.setCursor(Qt.PointingHandCursor)
        settings_btn.clicked.connect(self.open_plot_settings)
        self.addWidget(settings_btn)

    def setup_custom_icons(self):
        """Replace default matplotlib toolbar icons with custom ones"""
        icon_mapping = {
            'home': 'home.png',
            'back': 'back.png',
            'forward': 'forward.png',
            'pan': 'pan.png',
            'zoom': 'zoom.png',
            'save': 'save.png',
            'subplots': 'subplots.png'
        }

        for action in self.actions():
            try:
                action_name = action.text().lower() if action.text() else ""
                if not action_name or action_name == "separator":
                    continue

                for key, icon_name in icon_mapping.items():
                    if key in action_name:
                        icon_path = get_resource_path(f"assets/{icon_name}")
                        if os.path.exists(icon_path):
                            action.setIcon(QIcon(icon_path))
                        break
            except Exception as e:
                logger.warning(f"Error setting custom icon for action {action_name}: {e}")

    def edit_parameters(self):
        """Override edit_parameters with custom styling"""
        super().edit_parameters()
        dialog = self._get_active_dialog()
        if dialog:
            dialog.setStyleSheet(StylesheetManager.get_dialog_stylesheet())

    def configure_subplots(self):
        """Override configure_subplots with custom styling"""
        super().configure_subplots()
        dialog = self._get_active_dialog()
        if dialog:
            dialog.setStyleSheet(StylesheetManager.get_dialog_stylesheet())

    def _get_active_dialog(self):
        """Get the currently active dialog window"""
        try:
            for widget in QtWidgets.QApplication.topLevelWidgets():
                if isinstance(widget, QtWidgets.QDialog) and widget.isVisible():
                    return widget
        except Exception as e:
            logger.warning(f"Error finding active dialog: {e}")
        return None

    def open_plot_settings(self):
        """Show the plot settings dialog"""
        try:
            self.settings_dialog = PlotSettingsDialog(
                self.parent,
                self.canvas.figure,
                {'data_frame_1': self.data_frame_1, 'data_frame_2': self.data_frame_2},
                self.settings_manager.settings,
                data_series_1=self.data_series_1,
                data_series_2=self.data_series_2
            )
            self.settings_dialog.show()
        except Exception as e:
            logger.error(f"Error opening plot settings dialog: {e}")


class PlotSettingsDialog(QDialog, Ui_Plot_settings_Dialog):
    """Plot settings dialog with centralized management"""

    def __init__(self, parent, figure, data_frame, settings, data_series_1=None, data_series_2=None):
        super().__init__(parent)
        self.setupUi(self)

        self.figure = figure
        self.data_series_1 = data_series_1
        self.data_series_2 = data_series_2

        # Initialize managers
        self.settings_manager = PlotSettingsManager()
        self.data_frame_1, self.data_frame_2 = self.settings_manager.initialize_data_frames(data_frame)

        # Initialize axes
        self.ax = None
        self.ax_2 = None
        self._initialize_axes()

        # Initialize renderer
        self.renderer = PlotRenderer(self.figure, self.ax, self.ax_2)

        # Initialize settings
        self.settings_manager.settings = settings or {}

        # Setup UI
        self._setup_ui()
        self._connect_signals()
        self._initialize_values()
        self.setup_icons()

        """Initialize data frames from input parameter"""
        exponential_icon = QIcon(get_resource_path("assets/exponential.png"))
        self.pushButton.setIcon(exponential_icon)
        self.pushButton.setIconSize(QSize(58, 58))

        linear_icon = QIcon(get_resource_path("assets/linear.png"))
        self.pushButton_2.setIcon(linear_icon)
        self.pushButton_2.setIconSize(QSize(58, 58))

        logarithmic_icon = QIcon(get_resource_path("assets/logarithmic.png"))
        self.pushButton_3.setIcon(logarithmic_icon)
        self.pushButton_3.setIconSize(QSize(58, 58))

        polynomial_icon = QIcon(get_resource_path("assets/polynomial.png"))
        self.pushButton_4.setIcon(polynomial_icon)
        self.pushButton_4.setIconSize(QSize(58, 58))

    def _initialize_axes(self):
        """Safely initialize axes from figure"""
        try:
            if self.figure.axes:
                self.ax = self.figure.axes[0]
                if len(self.figure.axes) > 1:
                    self.ax_2 = self.figure.axes[1]
        except (IndexError, AttributeError) as e:
            logger.warning(f"Error accessing figure axes: {e}")
            self.ax = self.figure.gca()

    def _setup_ui(self):
        """Setup UI components"""
        # Font size options
        font_sizes = [str(i) for i in range(8, 25)]
        for combo in [self.comboBoxTitleFontSize, self.comboBoxLabelFontSize, self.comboBoxLegendFontSize]:
            combo.addItems(font_sizes)

        # Set cursor for color buttons
        color_buttons = [
            self.btnLabelFontColorPallet, self.btnTitleFontColorPallet,
            self.btnLegendFontColorPallet, self.btnDataSelectionColorPallet,
            self.btnMajorGridColorPallet, self.btnMinorGridColorPallet
        ]
        for button in color_buttons:
            button.setCursor(Qt.PointingHandCursor)

    def _connect_signals(self):
        """Connect all UI signals"""
        # Dialog buttons
        self.pushButton_8.clicked.connect(self.accept_changes)
        self.pushButton_7.clicked.connect(self.reject)

        # Color picker buttons
        color_connections = [
            (self.btnTitleFontColorPallet, 'title_color', self.lnEdtTitleFontColorHex),
            (self.btnLabelFontColorPallet, 'label_color', self.lnEdtLabelFontColorHex),
            (self.btnLegendFontColorPallet, 'legend_color', self.lnEdtLegendFontColorHex),
            (self.btnDataSelectionColorPallet, 'data_series_color', self.lnEdtDataSelectionColorHex),
            (self.btnMajorGridColorPallet, 'major_grid_color', self.lnEdtMajorGridColorHex),
            (self.btnMinorGridColorPallet, 'minor_grid_color', self.lnEdtMinorGridColorHex),
        ]

        for button, setting_key, line_edit in color_connections:
            button.clicked.connect(lambda checked, b=button, k=setting_key, le=line_edit:
                                   self._pick_color(b, k, le))

        # Other connections
        self.lnEdtTitleFontColorHex.editingFinished.connect(
            lambda: self._dynamic_color_change(self.lnEdtTitleFontColorHex.text()))
        self.lnEdtLegendName.editingFinished.connect(self._update_legend_name)
        self.comboBoxDataSelection.currentTextChanged.connect(self._update_data_series_ui)
        self.comboBoxDataSelectionForPlotRange.currentTextChanged.connect(self._update_range_spinboxes)
        self.customDoubleSpinBoxRangeMin.valueChanged.connect(self._update_plot_range)
        self.customDoubleSpinBoxRangeMax.valueChanged.connect(self._update_plot_range)
        self.radioButton.toggled.connect(lambda: self.add_trendline('exponential'))
        self.radioButton_2.toggled.connect(lambda: self.add_trendline('linear'))
        self.radioButton_5.toggled.connect(lambda: self.add_trendline('logarithmic'))
        self.radioButton_6.toggled.connect(lambda: self.add_trendline('polynomial'))
        self.radioButton_8.toggled.connect(lambda: self.add_trendline('moving_average'))


    def _initialize_values(self):
        """Initialize dialog with current plot values"""
        if not self.ax:
            logger.error("No axes available for initialization")
            return

        # Extract current settings from plot
        current_settings = self.settings_manager.extract_plot_settings(self.ax, self.ax_2)
        self.settings_manager.settings.update(current_settings)

        print(f"The current settings are: {self.settings_manager.settings}")

        # Initialize UI elements
        self._populate_data_selection_combos()
        self._set_font_settings()
        self._set_color_settings()
        self._set_grid_settings()
        self._set_range_settings()
        self._setup_dual_axis_toggle()

    def _populate_data_selection_combos(self):
        """Populate data selection combo boxes"""
        self.comboBoxDataSelection.clear()
        self.comboBoxDataSelectionForPlotRange.clear()

        # Add data series
        if self.data_frame_1:
            for series in self.data_frame_1.keys():
                legend_name = self.settings_manager.settings.get(f'legend_name_{series}', series)
                self.comboBoxDataSelection.addItem(legend_name)

        # Add axis names for range selection
        self.comboBoxDataSelectionForPlotRange.addItem("X-Axis")
        self.comboBoxDataSelectionForPlotRange.addItem("Y-Axis")
        if self.ax_2:
            self.comboBoxDataSelectionForPlotRange.addItem("Y-Axis 2")

    def _set_font_settings(self):
        """Set font-related UI elements"""
        settings = self.settings_manager.settings

        # Title font
        font = QtGui.QFont()
        font.setFamily(settings.get('title_font', 'Arial'))
        self.comboBoxTitleFont.setCurrentFont(font)
        self.comboBoxTitleFontSize.setCurrentText(str(settings.get('title_size', 12)))

        # Label font
        font.setFamily(settings.get('label_font', 'Arial'))
        self.comboBoxLabelFont.setCurrentFont(font)
        self.comboBoxLabelFontSize.setCurrentText(str(settings.get('label_size', 10)))

        # Legend font
        font.setFamily(settings.get('legend_font', 'Arial'))
        self.comboBoxLegendFont.setCurrentFont(font)
        self.comboBoxLegendFontSize.setCurrentText(str(settings.get('legend_size', 10)))

    def _set_color_settings(self):
        """Set color-related UI elements"""
        settings = self.settings_manager.settings

        # Title color
        title_color = settings.get('title_color', '#000000')
        self.lnEdtTitleFontColorHex.setText(title_color)
        self.btnTitleFontColorPallet.setStyleSheet(f"background-color: {title_color};")

        # Label color
        label_color = settings.get('label_color', '#000000')
        self.lnEdtLabelFontColorHex.setText(label_color)
        self.btnLabelFontColorPallet.setStyleSheet(f"background-color: {label_color};")

        # Legend color
        legend_color = settings.get('legend_color', '#000000')
        self.lnEdtLegendFontColorHex.setText(legend_color)
        self.btnLegendFontColorPallet.setStyleSheet(f"background-color: {legend_color};")

        # Data series color (first series)
        if self.data_frame_1:
            first_series = list(self.data_frame_1.keys())[0]
            series_color = settings.get(f'data_series_color_{first_series}', '#008000')
            legend_name = settings.get(f'legend_name_{first_series}', first_series)
            self.lnEdtDataSelectionColorHex.setText(series_color)
            self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
            self.lnEdtLegendName.setText(legend_name)

        # Grid colors
        major_grid_color = settings.get('major_grid_color', '#008000')
        minor_grid_color = settings.get('minor_grid_color', '#008000')
        self.lnEdtMajorGridColorHex.setText(major_grid_color)
        self.lnEdtMinorGridColorHex.setText(minor_grid_color)
        self.btnMajorGridColorPallet.setStyleSheet(f"background-color: {major_grid_color};")
        self.btnMinorGridColorPallet.setStyleSheet(f"background-color: {minor_grid_color};")

    def _set_grid_settings(self):
        """Set grid-related UI elements"""
        settings = self.settings_manager.settings
        print(f"The Current settings at grid are: {settings}")
        self.checkBoxMinorGrid.setChecked(settings['minor_grid_visible'])
        print(f"The minor grid visibility is: {settings['minor_grid_visible']}")
        print(f"The Current settings at major grid are: {settings}")
        self.checkBoxMajorGrid.setChecked(settings['major_grid_visible'])
        print(f"The major grid visibility is: {settings['major_grid_visible']}")

    def _set_range_settings(self):
        """Set range-related UI elements"""
        settings = self.settings_manager.settings
        if 'x_axis_min' in settings and 'x_axis_max' in settings:
            self.customDoubleSpinBoxRangeMin.setValue(settings['x_axis_min'])
            self.customDoubleSpinBoxRangeMax.setValue(settings['x_axis_max'])

    def _setup_dual_axis_toggle(self):
        """Setup toggle switch for dual-axis plots"""
        if self.ax_2 and self.data_series_1 and self.data_series_2:
            font = QFont("Arial", 12)
            metrics = QFontMetricsF(font)
            width_1 = metrics.horizontalAdvance(self.data_series_1)
            width_2 = metrics.horizontalAdvance(self.data_series_2)
            max_width = int(max(width_1, width_2)) + 20

            self.data_selection_toggle = MultiStateToggleSwitch(
                [self.data_series_1, self.data_series_2],
                width=max_width,
                height=25
            )
            self.dataSettingsHeaderFrame.layout().addWidget(self.data_selection_toggle)
            self.data_selection_toggle.modeChanged.connect(self._handle_data_frame_change)

    def setup_icons(self):
        """Setup icons for UI elements"""
        try:
            paths = StylesheetManager.get_base_paths()

            # Combo box style
            combo_style = f"""
                QComboBox::down-arrow {{
                    image: url("{paths['down_arrow']}");
                    width: 24px;
                    height: 24px;
                }}
                QComboBox::drop-down {{
                    border: none;
                    background: transparent;
                    width: 20px;
                    margin-right: 8px;
                }}
            """

            combo_boxes = [
                self.comboBoxDataSelection, self.comboBoxDataSelectionForPlotRange,
                self.comboBoxTitleFont, self.comboBoxLabelFont, self.comboBoxLegendFont,
                self.comboBoxTitleFontSize, self.comboBoxLabelFontSize, self.comboBoxLegendFontSize
            ]

            for combo_box in combo_boxes:
                if combo_box:
                    current_style = combo_box.styleSheet()
                    combo_box.setStyleSheet(current_style + combo_style)

            # Checkbox style
            checkbox_style = f"""
                QCheckBox::indicator {{
                    width: 18px;
                    height: 18px;
                }}
                QCheckBox::indicator:checked {{
                    image: url("{paths['checkbox_checked']}");
                }}
                QCheckBox::indicator:unchecked {{
                    image: url("{paths['checkbox_unchecked']}");
                }}
            """

            checkboxes = [self.checkBoxMajorGrid, self.checkBoxMinorGrid]
            for checkbox in checkboxes:
                if checkbox:
                    current_style = checkbox.styleSheet()
                    checkbox.setStyleSheet(current_style + checkbox_style)

        except Exception as e:
            logger.warning(f"Error setting up icons: {e}")

    def _get_active_axis(self):
        """Get the currently active axis"""
        if hasattr(self, 'data_selection_toggle'):
            index = self.data_selection_toggle.get_current_index()
            return self.ax if index == 0 else self.ax_2
        return self.ax

    def _get_active_data_frame(self):
        """Get the currently active data frame"""
        if hasattr(self, 'data_selection_toggle'):
            index = self.data_selection_toggle.get_current_index()
            return self.data_frame_1 if index == 0 else self.data_frame_2
        return self.data_frame_1

    def _get_original_series_name(self, legend_name):
        """Map legend name back to original series name"""
        try:
            active_df = self._get_active_data_frame()
            for series in active_df.keys():
                if self.settings_manager.settings.get(f'legend_name_{series}', series) == legend_name:
                    return series
        except Exception as e:
            logger.warning(f"Error getting original series name: {e}")
        return None

    def _handle_data_frame_change(self):
        """Handle changes in data frame selection"""
        try:
            self.comboBoxDataSelection.clear()
            current_data_frame = self._get_active_data_frame()

            if current_data_frame:
                for series in current_data_frame.keys():
                    legend_name = self.settings_manager.settings.get(f'legend_name_{series}', series)
                    self.comboBoxDataSelection.addItem(legend_name)

                # Update UI for first series
                first_series = list(current_data_frame.keys())[0]
                series_color = self.settings_manager.settings.get(f'data_series_color_{first_series}', '#008000')
                legend_name = self.settings_manager.settings.get(f'legend_name_{first_series}', first_series)
                self.lnEdtDataSelectionColorHex.setText(series_color)
                self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
                self.lnEdtLegendName.setText(legend_name)

        except Exception as e:
            logger.error(f"Error handling data frame change: {e}")

    def _dynamic_color_change(self, color):
        """Update color button when hex code is manually changed"""
        try:
            if color and color.startswith('#') and len(color) == 7:
                self.btnTitleFontColorPallet.setStyleSheet(f"background-color: {color};")
                self.settings_manager.settings['title_color'] = color
                self._apply_current_settings()
        except Exception as e:
            logger.warning(f"Error in dynamic color change: {e}")

    def _update_data_series_ui(self):
        """Update UI when different data series is selected"""
        if not self.data_frame_1:
            return

        try:
            selected_series = self._get_original_series_name(self.comboBoxDataSelection.currentText())
            if selected_series:
                series_color = self.settings_manager.settings.get(f'data_series_color_{selected_series}', '#008000')
                legend_name = self.settings_manager.settings.get(f'legend_name_{selected_series}', selected_series)
                self.lnEdtDataSelectionColorHex.setText(series_color)
                self.btnDataSelectionColorPallet.setStyleSheet(f"background-color: {series_color};")
                self.lnEdtLegendName.setText(legend_name)
        except Exception as e:
            logger.error(f"Error updating data series UI: {e}")

    def _update_legend_name(self):
        """Update legend name for current series"""
        try:
            current_legend_name = self.comboBoxDataSelection.currentText()
            series = self._get_original_series_name(current_legend_name)
            if not series:
                return

            new_name = self.lnEdtLegendName.text()
            color = self.lnEdtDataSelectionColorHex.text()

            # Update settings
            self.settings_manager.settings[f'legend_name_{series}'] = new_name
            self.settings_manager.settings[f'data_series_color_{series}'] = color

            # Update combo box
            current_index = self.comboBoxDataSelection.currentIndex()
            if current_index >= 0:
                self.comboBoxDataSelection.setItemText(current_index, new_name)

            self._apply_current_settings()
        except Exception as e:
            logger.error(f"Error updating legend name: {e}")

    def _pick_color(self, button, setting_key, color_line_edit):
        """Handle color picker dialog"""
        try:
            color = QColorDialog.getColor()
            if color.isValid():
                color_name = color.name()
                button.setStyleSheet(f"background-color: {color_name};")
                color_line_edit.setText(color_name)

                # Update settings
                if setting_key == 'data_series_color':
                    series = self._get_original_series_name(self.comboBoxDataSelection.currentText())
                    if series:
                        self.settings_manager.settings[f'data_series_color_{series}'] = color_name
                else:
                    self.settings_manager.settings[setting_key] = color_name

                self._apply_current_settings()
        except Exception as e:
            logger.error(f"Error picking color: {e}")

    def _update_range_spinboxes(self, axis_name):
        """Update range spinboxes when axis selection changes"""
        if not self.ax:
            return

        try:
            # Get current axis limits
            x_min, x_max = self.ax.get_xlim()
            y_min, y_max = self.ax.get_ylim()

            self.settings_manager.settings.update({
                'x_axis_min': x_min, 'x_axis_max': x_max,
                'y_axis_min': y_min, 'y_axis_max': y_max
            })

            if self.ax_2:
                y2_min, y2_max = self.ax_2.get_ylim()
                self.settings_manager.settings.update({
                    'y2_axis_min': y2_min, 'y2_axis_max': y2_max
                })

            # Update spinboxes based on selected axis
            if axis_name == "X-Axis":
                self.customDoubleSpinBoxRangeMin.setValue(x_min)
                self.customDoubleSpinBoxRangeMax.setValue(x_max)
            elif axis_name == "Y-Axis":
                self.customDoubleSpinBoxRangeMin.setValue(y_min)
                self.customDoubleSpinBoxRangeMax.setValue(y_max)
            elif axis_name == "Y-Axis 2" and self.ax_2:
                y2_min, y2_max = self.ax_2.get_ylim()
                self.customDoubleSpinBoxRangeMin.setValue(y2_min)
                self.customDoubleSpinBoxRangeMax.setValue(y2_max)

        except Exception as e:
            logger.error(f"Error updating range spinboxes: {e}")

    def _update_plot_range(self):
        """Update plot range based on spinbox values"""
        if not self.ax:
            return

        try:
            min_val = self.customDoubleSpinBoxRangeMin.value()
            max_val = self.customDoubleSpinBoxRangeMax.value()

            if min_val >= max_val:
                return

            selected_axis = self.comboBoxDataSelectionForPlotRange.currentText()

            if selected_axis == "X-Axis":
                self.settings_manager.settings.update({'x_axis_min': min_val, 'x_axis_max': max_val})
            elif selected_axis == "Y-Axis":
                self.settings_manager.settings.update({'y_axis_min': min_val, 'y_axis_max': max_val})
            elif selected_axis == "Y-Axis 2" and self.ax_2:
                self.settings_manager.settings.update({'y2_axis_min': min_val, 'y2_axis_max': max_val})

            # Apply only axis limits change
            self.renderer._apply_axis_limits(self.settings_manager.settings)
            self.figure.canvas.draw_idle()

        except Exception as e:
            logger.error(f"Error updating plot range: {e}")

    def _collect_current_ui_settings(self):
        """Collect all current UI settings"""
        try:
            # Font settings
            self.settings_manager.settings.update({
                'title_font': self.comboBoxTitleFont.currentFont().family(),
                'title_size': self.settings_manager.safe_convert(self.comboBoxTitleFontSize.currentText(), int, 12),
                'title_color': self.lnEdtTitleFontColorHex.text() or '#000000',

                'label_font': self.comboBoxLabelFont.currentFont().family(),
                'label_size': self.settings_manager.safe_convert(self.comboBoxLabelFontSize.currentText(), int, 10),
                'label_color': self.lnEdtLabelFontColorHex.text() or '#000000',

                'legend_font': self.comboBoxLegendFont.currentFont().family(),
                'legend_size': self.settings_manager.safe_convert(self.comboBoxLegendFontSize.currentText(), int, 10),
                'legend_color': self.lnEdtLegendFontColorHex.text() or '#000000',

                'major_grid_visible': self.checkBoxMajorGrid.isChecked(),
                'minor_grid_visible': self.checkBoxMinorGrid.isChecked(),
                'major_grid_color': self.lnEdtMajorGridColorHex.text() or '#008000',
                'minor_grid_color': self.lnEdtMinorGridColorHex.text() or '#008000'
            })

            # Ensure all data series have settings
            for data_frame in [self.data_frame_1, self.data_frame_2]:
                for series in data_frame.keys():
                    if f'legend_name_{series}' not in self.settings_manager.settings:
                        self.settings_manager.settings[f'legend_name_{series}'] = series
                    if f'data_series_color_{series}' not in self.settings_manager.settings:
                        self.settings_manager.settings[f'data_series_color_{series}'] = '#008000'

        except Exception as e:
            logger.error(f"Error collecting UI settings: {e}")

    def _apply_current_settings(self):
        """Apply current settings to the plot"""
        try:
            self._collect_current_ui_settings()
            self.renderer.apply_settings(
                self.settings_manager.settings,
                self.data_frame_1,
                self.data_frame_2
            )
        except Exception as e:
            logger.error(f"Error applying current settings: {e}")

    def _restore_original_plot(self):
        """Restore original plot state"""
        try:
            for line in self.ax.get_lines():
                if hasattr(line, 'set_data_orig'):
                    line.set_data(*line.set_data_orig)
            self.figure.tight_layout()
            self.figure.canvas.draw()
        except Exception as e:
            logger.error(f"Error restoring original plot: {e}")

    def reject(self):
        """Handle dialog rejection"""
        self._restore_original_plot()
        super().reject()

    def accept_changes(self):
        """Handle dialog acceptance with final settings application"""
        try:
            self._collect_current_ui_settings()
            self.renderer.apply_settings(
                self.settings_manager.settings,
                self.data_frame_1,
                self.data_frame_2
            )
            self.accept()
        except Exception as e:
            logger.error(f"Error accepting changes: {e}")
            self.accept()  # Still accept dialog even if error occurs

    def add_trendline(self, trend_type=None):
        trendline = PlotTrendline(self.ax, self.ax_2)
        if trend_type == 'linear':
            trendline.linear()
        elif trend_type == 'polynomial':
            trendline.polynomial(degree=int(self.lnEdtPolyDegree.text()))
        elif trend_type == 'exponential':
            trendline.exponential()
        elif trend_type == 'logarithmic':
            trendline.logarithmic()
        # elif trend_type == 'power':
        #     trendline.power()
        elif trend_type == 'moving_average':
            trendline.moving_average(window_size=int(self.lnEdtMvAvgWindowSize.text()))


# class PlotTrendline:
#     def __init__(self, ax, ax_2=None):
#         self.ax = ax
#         self.ax_2 = ax_2
#
#     def linear(self):
#         """Apply linear trendline to the current plot"""
#         # Implementation for linear trendline
#         x = self.ax.get_lines()[0].get_xdata()
#         y = self.ax.get_lines()[0].get_ydata()
#         slope, intercept, _, _, _ = linregress(x, y)
#         trendline = slope * x + intercept
#         print(f"The equation is: {slope} * x + {intercept}")
#         self.ax.plot(x, trendline, color='red', linestyle='--', label='Linear Trendline')
#
#     def polynomial(self):
#         """Apply polynomial trendline to the current plot"""
#         # Implementation for polynomial trendline
#         x = self.ax.get_lines()[0].get_xdata()
#         y = self.ax.get_lines()[0].get_ydata()
#         degree = 30  # Example degree, can be made dynamic
#         coefficients = np.polyfit(x, y, degree)
#         polynomial = np.poly1d(coefficients)
#         trendline_eqn = polynomial
#         self.draggable = DraggableText(self.ax.text(0.05, 0.95, f"Trendline: {trendline_eqn}", transform=self.ax.transAxes))
#         trendline = polynomial(x)
#         self.ax.plot(x, trendline, color='blue', linestyle='--', label='Polynomial Trendline')


class DraggableText:
    def __init__(self, text_obj):
        self.text = text_obj
        self.press = None
        self.canvas = self.text.figure.canvas
        self.text.set_picker(True)
        self.connect()

    # ---------- connect / disconnect unchanged ----------
    def connect(self):
        self.cid_press   = self.canvas.mpl_connect('button_press_event',  self.on_press)
        self.cid_release = self.canvas.mpl_connect('button_release_event',self.on_release)
        self.cid_motion  = self.canvas.mpl_connect('motion_notify_event', self.on_motion)

    def disconnect(self):
        for cid in (self.cid_press, self.cid_release, self.cid_motion):
            self.canvas.mpl_disconnect(cid)

    # ----------------------------------------------------
    def on_press(self, event):
        if (event.inaxes != self.text.axes or
            event.button != 1):
            return
        contains, _ = self.text.contains(event)
        if contains:
            # store original position in the SAME coordinate system we use
            self.press = (self.text.get_position(), event.x, event.y)

    def on_motion(self, event):
        if self.press is None or event.inaxes != self.text.axes:
            return
        (x0, y0), xpress, ypress = self.press
        dx = event.x - xpress
        dy = event.y - ypress
        # canvas pixels → axes fraction
        trans = self.text.axes.transAxes.inverted()
        dxa, dya = trans.transform((dx, dy)) - trans.transform((0, 0))
        self.text.set_position((x0 + dxa, y0 + dya))
        self.canvas.draw_idle()

    def on_release(self, event):
        self.press = None
        self.canvas.draw_idle()


class PlotTrendline:
    def __init__(self, ax, ax_2=None):
        self.ax = ax
        self.ax_2 = ax_2

    def linear(self):
        """Apply linear trendline to the current plot"""
        try:
            # Get data from the first line in the plot
            if not self.ax.get_lines():
                print("No data lines found in the plot")
                return

            x = self.ax.get_lines()[0].get_xdata()
            y = self.ax.get_lines()[0].get_ydata()

            # Calculate linear regression
            slope, intercept, r_value, p_value, std_err = linregress(x, y)
            trendline = slope * x + intercept

            # Create equation text
            equation_text = f"y = {slope:.3f}x + {intercept:.3f}\nR² = {r_value ** 2:.3f}"

            # Plot the trendline
            self.ax.plot(x, trendline, color='red', linestyle='--', label='Linear Trendline')

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

        except Exception as e:
            print(f"Error creating linear trendline: {e}")

    def polynomial(self, degree=2):
        """Apply polynomial trendline to the current plot"""
        try:
            # Get data from the first line in the plot
            if not self.ax.get_lines():
                print("No data lines found in the plot")
                return

            x = self.ax.get_lines()[0].get_xdata()
            y = self.ax.get_lines()[0].get_ydata()

            # Fit polynomial (reduced degree for better readability)
            coefficients = np.polyfit(x, y, degree)
            polynomial = np.poly1d(coefficients)

            # Create equation string
            equation_str = self._format_polynomial_equation(coefficients)

            # Calculate R²
            y_pred = polynomial(x)
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            equation_text = f"{equation_str}\nR² = {r_squared:.3f}"

            # Plot the trendline
            trendline = polynomial(x)
            self.ax.plot(x, trendline, color='blue', linestyle='--', label='Polynomial Trendline')

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

        except Exception as e:
            print(f"Error creating polynomial trendline: {e}")

    def _format_polynomial_equation(self, coefficients):
        """Format polynomial coefficients into a readable equation string"""
        degree = len(coefficients) - 1
        terms = []

        for i, coeff in enumerate(coefficients):
            power = degree - i

            if abs(coeff) < 1e-10:  # Skip very small coefficients
                continue

            # Format coefficient
            if power == 0:
                terms.append(f"{coeff:.3f}")
            elif power == 1:
                if coeff == 1:
                    terms.append("x")
                elif coeff == -1:
                    terms.append("-x")
                else:
                    terms.append(f"{coeff:.3f}x")
            else:
                if coeff == 1:
                    terms.append(f"x^{power}")
                elif coeff == -1:
                    terms.append(f"-x^{power}")
                else:
                    terms.append(f"{coeff:.3f}x^{power}")

        if not terms:
            return "y = 0"

        equation = "y = " + terms[0]
        for term in terms[1:]:
            if term.startswith('-'):
                equation += f" - {term[1:]}"
            else:
                equation += f" + {term}"

        return equation

    def exponential(self):
        """Apply exponential trendline to the current plot"""
        try:
            # Get data from the first line in the plot
            if not self.ax.get_lines():
                print("No data lines found in the plot")
                return

            x = self.ax.get_lines()[0].get_xdata()
            y = self.ax.get_lines()[0].get_ydata()

            # Filter out non-positive y values for logarithmic transformation
            mask = y > 0
            if not np.any(mask):
                print("No positive y values found for exponential fit")
                return

            x_filtered = x[mask]
            y_filtered = y[mask]

            # Fit exponential: y = a * exp(b * x)
            # Take log: ln(y) = ln(a) + b * x
            log_y = np.log(y_filtered)
            b, ln_a = np.polyfit(x_filtered, log_y, 1)
            a = np.exp(ln_a)

            # Generate trendline
            x_trend = np.linspace(x.min(), x.max(), 100)
            y_trend = a * np.exp(b * x_trend)

            # Calculate R² for the exponential fit
            y_pred = a * np.exp(b * x_filtered)
            ss_res = np.sum((y_filtered - y_pred) ** 2)
            ss_tot = np.sum((y_filtered - np.mean(y_filtered)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            equation_text = f"y = {a:.3f}e^({b:.3f}x)\nR² = {r_squared:.3f}"

            # Plot the trendline
            self.ax.plot(x_trend, y_trend, color='green', linestyle='--', label='Exponential Trendline')

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

        except Exception as e:
            print(f"Error creating exponential trendline: {e}")

    def logarithmic(self):
        """Apply logarithmic trendline to the current plot"""
        try:
            # Get data from the first line in the plot
            if not self.ax.get_lines():
                print("No data lines found in the plot")
                return

            x = self.ax.get_lines()[0].get_xdata()
            y = self.ax.get_lines()[0].get_ydata()

            # Filter out non-positive x values for logarithmic transformation
            mask = x > 0
            if not np.any(mask):
                print("No positive x values found for logarithmic fit")
                return

            x_filtered = x[mask]
            y_filtered = y[mask]

            # Fit logarithmic: y = a * ln(x) + b
            log_x = np.log(x_filtered)
            a, b = np.polyfit(log_x, y_filtered, 1)

            # Generate trendline
            x_trend = np.linspace(x[mask].min(), x[mask].max(), 100)
            y_trend = a * np.log(x_trend) + b

            # Calculate R²
            y_pred = a * np.log(x_filtered) + b
            ss_res = np.sum((y_filtered - y_pred) ** 2)
            ss_tot = np.sum((y_filtered - np.mean(y_filtered)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            equation_text = f"y = {a:.3f}ln(x) + {b:.3f}\nR² = {r_squared:.3f}"

            # Plot the trendline
            self.ax.plot(x_trend, y_trend, color='purple', linestyle='--', label='Logarithmic Trendline')

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

        except Exception as e:
            print(f"Error creating logarithmic trendline: {e}")

    def power(self):
        """Apply power trendline to the current plot"""
        try:
            # Get data from the first line in the plot
            if not self.ax.get_lines():
                print("No data lines found in the plot")
                return

            x = self.ax.get_lines()[0].get_xdata()
            y = self.ax.get_lines()[0].get_ydata()

            # Fit power: y = a * x^b
            log_x = np.log(x)
            log_y = np.log(y)

            b, log_a = np.polyfit(log_x, log_y, 1)
            a = np.exp(log_a)

            # Generate trendline
            x_trend = np.linspace(x.min(), x.max(), 100)
            y_trend = a * (x_trend ** b)

            # Calculate R²
            y_pred = a * (x ** b)
            ss_res = np.sum((y - y_pred) ** 2)
            ss_tot = np.sum((y - np.mean(y)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0

            equation_text = f"y = {a:.3f}x^{b:.3f}\nR² = {r_squared:.3f}"

            # Plot the trendline
            self.ax.plot(x_trend, y_trend, color='orange', linestyle='--', label='Power Trendline')

            # Add draggable equation text
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

        except Exception as e:
            print(f"Error creating logarithmic trendline: {e}")

    def moving_average(self, window_size=5):
        """Apply moving average trendline to the current plot"""
        try:
            # Get data from the first line in the plot
            if not self.ax.get_lines():
                print("No data lines found in the plot")
                return

            x = self.ax.get_lines()[0].get_xdata()
            y = self.ax.get_lines()[0].get_ydata()

            # Calculate moving average
            moving_avg = np.convolve(y, np.ones(window_size)/window_size, mode='valid')

            # Generate x values for the moving average
            x_trend = x[window_size-1:]

            # Plot the trendline
            self.ax.plot(x_trend, moving_avg, color='cyan', linestyle='--', label='Moving Average Trendline')

            # Add draggable equation text (not applicable for moving average)
            equation_text = "Moving Average Trendline"
            text_obj = self.ax.text(0.05, 0.95, equation_text,
                                    transform=self.ax.transAxes,
                                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                                    fontsize=10)

            # Make the text draggable
            # keep a reference so the object is not garbage-collected
            self.ax.figure.canvas._draggable_text = DraggableText(text_obj)

            # Refresh the plot
            self.ax.figure.canvas.draw()

        except Exception as e:
            print(f"Error creating logarithmic trendline: {e}")

