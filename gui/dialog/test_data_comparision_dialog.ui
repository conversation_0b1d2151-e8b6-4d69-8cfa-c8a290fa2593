<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>testComparisonDialog</class>
 <widget class="QDialog" name="testComparisonDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>750</width>
    <height>800</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>750</width>
    <height>800</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>750</width>
    <height>1000</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="frame_80">
     <property name="styleSheet">
      <string notr="true">QFrame #frame_80{
background-color: #18181b;
border-radius: 10px;
border: 1px solid #000000;}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_69">
      <item>
       <widget class="QFrame" name="tableFrame_2">
        <property name="styleSheet">
         <string notr="true">background-color:#18181b;
border-radius: 10px;</string>
        </property>
        <property name="frameShape">
         <enum>QFrame::Shape::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Shadow::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_63">
         <item>
          <widget class="QTableWidget" name="results_table_compare">
           <property name="minimumSize">
            <size>
             <width>550</width>
             <height>0</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QTableWidget {
                    background-color: #18181b;
                    color: white;
                    gridline-color: #446699;
                   border-radius: 10px;
					border:none;
                }
                QHeaderView::section {
                    background-color: #1e1e1e;
                    color: white;
                    padding: 5px;
                    border: 2px solid #0c9486;
					 border-radius:5px;
					
                }
                QTableWidget::item {
                    padding: 5px;
                }
                QTableWidget::item:selected {
                    background-color: #F2613F;
                }</string>
           </property>
           <property name="editTriggers">
            <set>QAbstractItemView::EditTrigger::NoEditTriggers</set>
           </property>
           <property name="dragDropOverwriteMode">
            <bool>false</bool>
           </property>
           <property name="dragDropMode">
            <enum>QAbstractItemView::DragDropMode::NoDragDrop</enum>
           </property>
           <property name="alternatingRowColors">
            <bool>false</bool>
           </property>
           <property name="selectionBehavior">
            <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
           </property>
           <property name="sortingEnabled">
            <bool>false</bool>
           </property>
           <property name="wordWrap">
            <bool>true</bool>
           </property>
           <property name="columnCount">
            <number>6</number>
           </property>
           <column>
            <property name="text">
             <string>Test Number</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Test Date</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Catalyst</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Propellant Conc.</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Database</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>Cut-off Temp</string>
            </property>
           </column>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_5" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>50</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent;</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_64">
      <item>
       <widget class="QLineEdit" name="search_tab">
        <property name="minimumSize">
         <size>
          <width>170</width>
          <height>35</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>270</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">border-radius:16px;
padding:1px 10px;
font: 14px;
background-color:#Fffdd0;
font-size:14px;
color: black;
font-weight: bold;</string>
        </property>
        <property name="inputMask">
         <string/>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="frame">
         <bool>false</bool>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
        <property name="clearButtonEnabled">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
