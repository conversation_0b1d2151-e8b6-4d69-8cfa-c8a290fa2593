import traceback
from typing import Dict, List, Optional
import time

import numpy as np
from PySide6.QtCore import Qt, <PERSON>S<PERSON>, QObject, QThread, Signal, QTimer, QEvent
from PySide6.QtGui import QIcon, QMovie, QColor
from PySide6.QtWidgets import QVBoxLayout, QScrollArea, QWidget, QPushButton, QTableWidget, QLabel, \
    QTableWidgetItem, QHBoxLayout, QMessageBox, QInputDialog, QDialog, QFrame, QComboBox, QSpacerItem, QSizePolicy, \
    QProgressBar, QApplication
from matplotlib import pyplot as plt
import matplotlib.patches as patches

from src.database import DatabaseHandler, DatabaseConfig, MultiDatabaseHandler
from src.utils import get_resource_path, open_pdf, safe_float
from user_authentication.auth import logger
from .ui_test_data_comparision_dialog import Ui_testComparisonDialog
from .custom_plot_settings import CustomNavigationToolbar
from ..ui_VAPR_iDEX_Thruster_Analysis import Ui_VaprIdexMainWindow
from ..widgets import PlotCanvas
from ..toggle_switch import MultiStateToggleSwitch


class DatabaseWorker(QThread):
    """Worker thread for database operations to prevent UI blocking."""

    # Signals for different types of data
    tests_loaded = Signal(list)  # For test data
    catalysts_loaded = Signal(dict)  # For catalyst data
    error_occurred = Signal(str)  # For error messages
    progress_updated = Signal(int)  # For progress updates

    def __init__(self, multi_db_handler, db_handler, operation_type, **kwargs):
        super().__init__()
        self.multi_db_handler = multi_db_handler
        self.db_handler = db_handler
        self.operation_type = operation_type
        self.kwargs = kwargs

    def run(self):
        """Execute the database operation in background thread."""
        try:
            if self.operation_type == "load_tests":
                self._load_tests()
            elif self.operation_type == "load_catalysts":
                self._load_catalysts()
            elif self.operation_type == "filter_tests":
                self._filter_tests()
        except Exception as e:
            logger.error(f"Database worker error: {str(e)}", exc_info=True)
            self.error_occurred.emit(str(e))

    def _load_tests(self):
        """Load test data based on selected database."""
        database_selection = self.kwargs.get('database_selection', 'EM2')

        self.progress_updated.emit(10)

        if self.multi_db_handler and database_selection == "Both":
            test_data_list = self.multi_db_handler.filter_tests_from_both_databases({})
        elif self.multi_db_handler and database_selection in ["EM1", "EM2"]:
            test_data_list = self.multi_db_handler.filter_tests_from_database({}, database_selection)
        else:
            test_data_list = self.db_handler.filter_tests({})
            # Add database source for regular handler results
            for result in test_data_list:
                if 'database_source' not in result:
                    result['database_source'] = 'EM2'

        self.progress_updated.emit(80)
        self.tests_loaded.emit(test_data_list)
        self.progress_updated.emit(100)

    def _load_catalysts(self):
        """Load catalyst data based on selected database."""
        database_selection = self.kwargs.get('database_selection', 'EM2')

        self.progress_updated.emit(20)

        catalysts_dict = {}

        if self.multi_db_handler and database_selection == "Both":
            catalysts_dict = self.multi_db_handler.get_unique_values_from_both_databases('catalyst_name')
        elif self.multi_db_handler and database_selection in ["EM1", "EM2"]:
            catalysts = self.multi_db_handler.get_unique_values_from_database('catalyst_name', database_selection)
            catalysts_dict[database_selection] = catalysts
        else:
            catalysts = self.db_handler.get_unique_values('catalyst_name')
            catalysts_dict['EM2'] = catalysts

        self.progress_updated.emit(90)
        self.catalysts_loaded.emit(catalysts_dict)
        self.progress_updated.emit(100)

    def _filter_tests(self):
        """Filter tests based on catalyst name."""
        catalyst_name = self.kwargs.get('catalyst_name')
        database_selection = self.kwargs.get('database_selection', 'EM2')

        self.progress_updated.emit(30)

        if self.multi_db_handler and database_selection == "Both":
            filtered_tests = self.multi_db_handler.filter_tests_from_both_databases({'catalystName': catalyst_name})
        elif self.multi_db_handler and database_selection in ["EM1", "EM2"]:
            filtered_tests = self.multi_db_handler.filter_tests_from_database({'catalystName': catalyst_name},
                                                                              database_selection)
        else:
            filtered_tests = self.db_handler.filter_tests({'catalystName': catalyst_name})
            # Add database source for regular handler results
            for result in filtered_tests:
                if 'database_source' not in result:
                    result['database_source'] = 'EM2'

        self.progress_updated.emit(90)
        self.tests_loaded.emit(filtered_tests)
        self.progress_updated.emit(100)


class DataCache:
    """Simple caching mechanism for frequently accessed data."""

    def __init__(self):
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_timeout = 300  # 5 minutes

    def get(self, key):
        """Get cached data if it exists and is not expired."""
        if key in self.cache:
            if time.time() - self.cache_timestamps[key] < self.cache_timeout:
                return self.cache[key]
            else:
                # Remove expired cache
                del self.cache[key]
                del self.cache_timestamps[key]
        return None

    def set(self, key, value):
        """Set cached data with timestamp."""
        self.cache[key] = value
        self.cache_timestamps[key] = time.time()

    def clear(self):
        """Clear all cached data."""
        self.cache.clear()
        self.cache_timestamps.clear()


class DataVisualizationWidget(QWidget):
    def __init__(self, test_no: int, mainwindow, parent=None, database_source="EM2"):
        super().__init__(parent)
        print(f"Creating DataVisualizationWidget for test {test_no} with database source '{database_source}'")
        self.test_no = test_no

        self.database_source = database_source  # Store the database source for this test
        print(f"DataVisualizationWidget initialized for test {test_no} with database_source: '{database_source}'")

        self.db_handler = DatabaseHandler(DatabaseConfig)
        # Initialize multi-database handler for cross-database comparisons
        try:
            self.multi_db_handler = MultiDatabaseHandler()
        except Exception as e:
            print(f"Warning: Could not initialize multi-database handler: {e}")
            self.multi_db_handler = None

        # Initialize caching system
        self.data_cache = DataCache()

        # Initialize worker thread variables
        self.database_worker = None
        self.loading_progress = None

        # Ensure database indexes for performance
        self._ensure_database_indexes()

        self.setup_ui()

        # Initialize comparison dialog components (but don't set up UI yet)
        self.ui_data_comparision_dialog = Ui_testComparisonDialog()
        self.comparison_dialog = None  # Will be created when needed

        # Store references to mainwindow and UI
        self.ui = mainwindow.ui
        self.mainwindow = mainwindow

        # Database selection for comparisons
        self.selected_database = "EM2"  # Default database

        # Multi-test comparison state
        self.selected_tests = []  # List of selected tests for comparison
        self.test_data_cache = {}  # Cache for test data
        self.temperature_data_cache = {}  # Cache for temperature data
        self.pressure_data_cache = {}  # Cache for pressure data

    def setup_ui(self):
        self.setStyleSheet("""
                    QDialog {
                        background-color: #1e1e1e;  /* or any color you want */
                        border-radius: 10px;      /* adjust radius for roundness */
                        border: 2px solid #888;   /* optional border */
                    }

                    QComboBox {
                        background-color: #333333;
                        color: white;
                        padding: 5px;
                        border: 1px solid #446699;
                        min-width: 150px;
                    }
                    QComboBox::drop-down {
                        border: none;
                    }
                    QComboBox::down-arrow {
                        image: none;
                        border-width: 0px;
                    }
                    QScrollArea {
                        background-color: #1e1e1e;
                        border: none;
                    }
                    /* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

QScrollBar:horizontal {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    height: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: none;
}
                    QLabel {
    color: #e0e0e0;
    font-family: 'Roboto', sans-serif;
    font-size: 12px;
}

QPushButton {
    background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #446699, stop:1 #557799);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    min-width: 80px;
}
QPushButton:hover {
    background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #557799, stop:1 #6688AA);
}

QLabel[header="true"] {
    font-size: 16px;
    font-weight: 600;
    color: #00C4B4;
    background-color: #2a3d4a;
    padding: 8px;
    border-radius: 6px;
}
                """)

        # Main layout
        self.layout = QVBoxLayout(self)
        self.layout.setSpacing(10)
        self.layout.setContentsMargins(15, 15, 15, 15)

        # Get test numbers and verify
        test_numbers = self.db_handler.get_all_test_numbers()

        # Scroll area for tables
        scroll = QScrollArea()
        scroll.setContentsMargins(15, 15, 15, 15)
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(scroll_content)
        scroll.setWidget(scroll_content)
        self.layout.addWidget(scroll)
        self.scroll_layout.setSpacing(30)

        # Style for section headers
        self.header_style = """
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #000000;
                background-color: #FFFFFF;
                padding: 5px;
                border-radius: 4px;
            }
        """

        self.scroll_content_style = """

        QWidget {
    background-color: transparent;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

        /* Scrollbar styling */
            QScrollBar:vertical {
                border: none;
                width: 10px;
                margin: 0px;
            	border-radius: 5px;
            }

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}
        """

        # Style for tables
        self.table_style = """
            QTableWidget {
                background-color: transparent;
                color: white;
                gridline-color: #404040;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #404040;
                color: white;
                padding: 4px;
                border: 1px solid #505050;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #404040;
                background-color: #2b2b2b;
            }

            QTableWidget::item:alternate {
                background-color: #333333;
            }

            /* Scrollbar styling */
QScrollBar:vertical {
    border: none;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 3px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}
        """

        self.comboBox = """
            QComboBox{
	            background-color: #22324c;
	            color: white;
	            padding:5px;
	            font-size:15px;
	            border:1px solid #446699;
            }

            QComboBox QAbstractItemView {
                /* Dropdown menu styling */
                background-color: black;
                border: 1px solid #ccc;
                selection-background-color: #0078d7;
                selection-color: white;
            }
        """

        scroll_content.setStyleSheet(self.scroll_content_style)

        # Load test data directly
        self.load_test_data(self.test_no)

    def create_section_header(self, text):
        header = QLabel(text)
        header.setStyleSheet(self.header_style)
        return header

    def create_table_from_dict(self, data_dict):
        table = QTableWidget()
        table.setStyleSheet(self.table_style)
        table.setColumnCount(2)
        table.setRowCount(len(data_dict))
        table.setHorizontalHeaderLabels(["Parameter", "Value"])

        print(f"Creating table from dict: {data_dict}")
        for i, (key, value) in enumerate(data_dict.items()):
            key_item = QTableWidgetItem(str(key).replace('_', ' ').title())
            value_item = QTableWidgetItem(str(value))
            table.setItem(i, 0, key_item)
            table.setItem(i, 1, value_item)

        table.resizeColumnsToContents()
        table.setMinimumHeight(min(400, (table.rowHeight(0) * (len(data_dict) + 1))))

        return table

    def create_table_from_dict_for_comparison(self, data_dict):
        table = QTableWidget()
        table.setStyleSheet(self.table_style)
        table.setColumnCount(3)
        test_names = list(data_dict.keys())
        table.setRowCount(len(data_dict[test_names[0]]))
        table.setHorizontalHeaderLabels(["Parameter", test_names[0], test_names[1]])

        for idx, test in enumerate(test_names[:2]):  # Only handle first two tests
            print(f"Creating table from dict: {data_dict[test]}")

            for i, (key, value) in enumerate(data_dict[test].items()):
                if idx == 0:
                    key_item = QTableWidgetItem(str(key).replace('_', ' ').title())
                    value_item = QTableWidgetItem(str(value))
                    table.setItem(i, 0, key_item)
                    table.setItem(i, 1, value_item)
                elif idx == 1:
                    value_item = QTableWidgetItem(str(value))
                    table.setItem(i, 2, value_item)

        table.resizeColumnsToContents()
        table.setMinimumHeight(min(400, (table.rowHeight(0) * (len(data_dict) + 5))))
        return table

    def create_heater_cycles_table(self, cycles_data):
        table = QTableWidget()
        table.setStyleSheet(self.table_style)
        headers = ["Cycle", "Switch On", "Switch Off", "Max Temp", "Location", "Tank Bottom Temp"]
        table.setColumnCount(len(headers))
        table.setRowCount(len(cycles_data))
        table.setHorizontalHeaderLabels(headers)

        for i, cycle in enumerate(cycles_data):
            table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            table.setItem(i, 1, QTableWidgetItem(str(cycle.get('switch_on', ''))))
            table.setItem(i, 2, QTableWidgetItem(str(cycle.get('switch_off', ''))))
            table.setItem(i, 3, QTableWidgetItem(str(cycle.get('max_temp', ''))))
            table.setItem(i, 4, QTableWidgetItem(str(cycle.get('max_temp_location', ''))))
            table.setItem(i, 5, QTableWidgetItem(str(cycle.get('tank_bottom_temp', ''))))

        table.resizeColumnsToContents()
        return table

    def get_section_wise_data(self, test_data):
        sections = [
            ("Basic Information", test_data.get('basic_info', {})),
            ("System Specifications", test_data.get('system_specs', {})),
            ("Propellant Specifications", test_data.get('propellant_specs', {})),
            ("Catalyst Specifications", test_data.get('catalyst_specs', {})),
            ("Component Details", test_data.get('component_details', {})),
            ("Test Details", test_data.get('test_details', {})),
            ("Heater Information", test_data.get('heater_info', {})),
            ("Post Test Observations", test_data.get('post_test_observations', {})),
            ("Catalyst Post Analysis", test_data.get('catalyst_post_analysis', {})),
            ("Propellant Post Analysis", test_data.get('propellant_post_analysis', {})),
            ("System Performance", test_data.get('performance_data', {}))
        ]
        return sections

    def show_current_test_number(self, test_no):
        # Test selection
        test_selection_layout = QHBoxLayout()
        test_label = QLabel("Current Test Number:")
        self.test_number_label = QLabel(test_no)

        test_selection_layout.addWidget(test_label)
        test_selection_layout.addWidget(self.test_number_label)
        test_selection_layout.addStretch()
        self.layout.addLayout(test_selection_layout)

    def show_comparison_dialog(self):
        """Show the test comparison dialog with optimized loading."""
        # Create a new dialog instance
        self.comparison_dialog = QDialog(self)
        self.comparison_dialog.setWindowTitle(f"Compare with Test {self.test_no}")
        self.comparison_dialog.setMinimumSize(900, 600)

        # Set up the UI using the existing UI class
        self.ui_data_comparision_dialog.setupUi(self.comparison_dialog)
        self.ui_data_comparision_dialog.search_tab.setPlaceholderText("Filter tests...")

        # Add loading progress bar
        self._add_loading_progress_bar()

        # Add database selection dropdown if multi-database handler is available
        if self.multi_db_handler:
            self.add_database_selection_widget()

        # Add catalyst filter cards (will be populated asynchronously)
        self.add_catalyst_filter_cards()

        # Connect search functionality
        self.ui_data_comparision_dialog.search_tab.textChanged.connect(self.filter_comparison_table)

        # Connect double-click on table row to select test for comparison
        self.ui_data_comparision_dialog.results_table_compare.doubleClicked.connect(
            lambda: self.add_test_to_comparison())

        # Add selected tests display area
        self._add_selected_tests_display()

        # Add action buttons
        self._add_comparison_action_buttons()

        # Start asynchronous data loading
        self._start_async_data_loading()

        # Connect dialog close event to cleanup
        self.comparison_dialog.finished.connect(self._cleanup_comparison_dialog)

        # Show the dialog
        self.comparison_dialog.exec()

    def _cleanup_comparison_dialog(self):
        """Clean up resources when comparison dialog is closed."""
        # Stop any running database worker
        if self.database_worker and self.database_worker.isRunning():
            self.database_worker.quit()
            self.database_worker.wait()
            self.database_worker = None

        # Clear progress bar reference
        self.loading_progress = None

        print("Comparison dialog cleanup completed")

    def _ensure_database_indexes(self):
        """Ensure database indexes exist for optimal query performance."""
        try:
            # Create indexes for frequently queried JSONB fields
            index_queries = [
                # Index for catalyst name queries
                "CREATE INDEX IF NOT EXISTS idx_catalyst_name ON test_data USING GIN ((basic_info->>'Catalyst'))",
                # Index for propellant concentration queries
                "CREATE INDEX IF NOT EXISTS idx_propellant_conc ON test_data USING GIN ((propellant_specs->>'Concentration before testing (%)')) WHERE propellant_specs->>'Concentration before testing (%)' IS NOT NULL",
                # Index for test date queries
                "CREATE INDEX IF NOT EXISTS idx_test_date ON test_data(test_date)",
                # Index for temperature_data foreign key
                "CREATE INDEX IF NOT EXISTS idx_temperature_test_id ON temperature_data(test_id)",
                # Index for pressure_data foreign key
                "CREATE INDEX IF NOT EXISTS idx_pressure_test_id ON pressure_data(test_id)"
            ]

            # Apply indexes to regular database
            try:
                with self.db_handler._get_connection() as conn:
                    with conn.cursor() as cur:
                        for query in index_queries:
                            try:
                                cur.execute(query)
                                conn.commit()
                            except Exception as e:
                                logger.warning(f"Could not create index: {query}, Error: {str(e)}")
                                conn.rollback()
            except Exception as e:
                logger.warning(f"Could not create indexes on main database: {str(e)}")

            # Apply indexes to multi-database handler if available
            if self.multi_db_handler:
                for db_version in ["EM1", "EM2"]:
                    try:
                        with self.multi_db_handler._get_connection(db_version) as conn:
                            with conn.cursor() as cur:
                                for query in index_queries:
                                    try:
                                        cur.execute(query)
                                        conn.commit()
                                    except Exception as e:
                                        logger.warning(
                                            f"Could not create index on {db_version}: {query}, Error: {str(e)}")
                                        conn.rollback()
                    except Exception as e:
                        logger.warning(f"Could not create indexes on {db_version} database: {str(e)}")

            logger.info("Database index optimization completed")

        except Exception as e:
            logger.error(f"Error ensuring database indexes: {str(e)}", exc_info=True)

    def _add_loading_progress_bar(self):
        """Add a progress bar for loading indication."""
        self.loading_progress = QProgressBar(self.comparison_dialog)
        self.loading_progress.setRange(0, 100)
        self.loading_progress.setValue(0)
        self.loading_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #446699;
                border-radius: 5px;
                text-align: center;
                background-color: #2d3748;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4096c1;
                border-radius: 3px;
            }
        """)

        # Insert progress bar at the top of the dialog
        dialog_layout = self.comparison_dialog.layout()
        if dialog_layout:
            dialog_layout.insertWidget(0, self.loading_progress)

    def _start_async_data_loading(self):
        """Start asynchronous loading of comparison data."""
        # Check cache first
        cache_key = f"tests_{self.selected_database}"
        cached_data = self.data_cache.get(cache_key)

        if cached_data:
            print(f"Using cached data for {self.selected_database}")
            self._on_tests_loaded(cached_data)
            self._load_catalysts_async()
        else:
            print(f"Loading fresh data for {self.selected_database}")
            self._load_tests_async()

    def _load_tests_async(self):
        """Load test data asynchronously."""
        if self.database_worker and self.database_worker.isRunning():
            self.database_worker.quit()
            self.database_worker.wait()

        self.database_worker = DatabaseWorker(
            self.multi_db_handler,
            self.db_handler,
            "load_tests",
            database_selection=self.selected_database
        )

        # Connect signals
        self.database_worker.tests_loaded.connect(self._on_tests_loaded)
        self.database_worker.error_occurred.connect(self._on_error_occurred)
        self.database_worker.progress_updated.connect(self._on_progress_updated)
        self.database_worker.finished.connect(lambda: self._load_catalysts_async())

        # Start the worker
        self.database_worker.start()

    def _load_catalysts_async(self):
        """Load catalyst data asynchronously."""
        # Check cache first
        cache_key = f"catalysts_{self.selected_database}"
        cached_catalysts = self.data_cache.get(cache_key)

        if cached_catalysts:
            print(f"Using cached catalysts for {self.selected_database}")
            self._on_catalysts_loaded(cached_catalysts)
            return

        if self.database_worker and self.database_worker.isRunning():
            self.database_worker.quit()
            self.database_worker.wait()

        self.database_worker = DatabaseWorker(
            self.multi_db_handler,
            self.db_handler,
            "load_catalysts",
            database_selection=self.selected_database
        )

        # Connect signals
        self.database_worker.catalysts_loaded.connect(self._on_catalysts_loaded)
        self.database_worker.error_occurred.connect(self._on_error_occurred)
        self.database_worker.progress_updated.connect(self._on_progress_updated)
        self.database_worker.finished.connect(self._on_loading_complete)

        # Start the worker
        self.database_worker.start()

    def _on_tests_loaded(self, test_data_list):
        """Handle loaded test data."""
        # Cache the data
        cache_key = f"tests_{self.selected_database}"
        self.data_cache.set(cache_key, test_data_list)

        # Update the table
        self.update_comparison_dialong_table(test_data_list)
        print(f"Loaded {len(test_data_list)} tests for {self.selected_database}")

    def _on_catalysts_loaded(self, catalysts_dict):
        """Handle loaded catalyst data."""
        # Cache the data
        cache_key = f"catalysts_{self.selected_database}"
        self.data_cache.set(cache_key, catalysts_dict)

        # Update catalyst cards
        self._update_catalyst_cards_with_data(catalysts_dict)
        print(f"Loaded catalysts for {self.selected_database}: {catalysts_dict}")

    def _on_error_occurred(self, error_message):
        """Handle database errors."""
        logger.error(f"Database operation failed: {error_message}")
        if self.loading_progress:
            self.loading_progress.hide()
        QMessageBox.critical(self.comparison_dialog, "Database Error", f"Failed to load data: {error_message}")

    def _on_progress_updated(self, progress):
        """Update progress bar."""
        if self.loading_progress:
            self.loading_progress.setValue(progress)

    def _on_loading_complete(self):
        """Handle completion of all loading operations."""
        if self.loading_progress:
            # Hide progress bar after a short delay
            QTimer.singleShot(500, lambda: self.loading_progress.hide() if self.loading_progress else None)

    def add_database_selection_widget(self):
        """Add database selection toggle switch to the comparison dialog."""
        # Create a frame for database selection
        db_selection_frame = QFrame(self.comparison_dialog)
        db_selection_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
            }
        """)

        # Create layout for database selection
        db_layout = QVBoxLayout(db_selection_frame)

        # Create horizontal layout for centering the toggle switch
        toggle_layout = QHBoxLayout()
        toggle_layout.setSpacing(10)

        # Create MultiStateToggleSwitch with database options
        database_modes = ["EM1", "EM2", "Both"]
        self.database_toggle = MultiStateToggleSwitch(database_modes)

        self.database_toggle.background_color = QColor(232, 249, 253)
        self.database_toggle.handle_color = QColor(89, 206, 143)
        self.database_toggle.text_color = QColor(0, 0, 0)

        # Set initial mode based on selected database
        initial_index = 0
        if self.selected_database in database_modes:
            initial_index = database_modes.index(self.selected_database)
        self.database_toggle.set_mode(initial_index)

        # Connect the mode change signal
        self.database_toggle.modeChanged.connect(self.on_database_toggle_changed)

        # Add toggle switch to layout with centering
        toggle_layout.addStretch()  # Add stretch before
        toggle_layout.addWidget(self.database_toggle)
        toggle_layout.addStretch()  # Add stretch after

        db_layout.addLayout(toggle_layout)

        # Insert the database selection frame at the top of the dialog
        dialog_layout = self.comparison_dialog.layout()
        if dialog_layout:
            dialog_layout.insertWidget(0, db_selection_frame)

    def on_database_toggle_changed(self, mode_name, mode_index):
        """Handle database toggle switch mode change with optimized loading."""
        # Update selected database
        self.selected_database = mode_name

        print(f"Database selection changed to: {mode_name} (index: {mode_index})")

        # Clear existing catalyst cards before loading new ones
        self._clear_catalyst_cards()

        # Show loading progress
        if self.loading_progress:
            self.loading_progress.show()
            self.loading_progress.setValue(0)

        # Start asynchronous data loading for new database selection
        self._start_async_data_loading()

    def update_catalyst_cards(self):
        """Update catalyst cards based on current database selection (legacy method)."""
        # This method is now handled by _update_catalyst_cards_with_data
        # Keep for backward compatibility but delegate to async loading
        if hasattr(self, 'loading_progress') and self.loading_progress:
            self.loading_progress.show()
            self.loading_progress.setValue(0)
        self._load_catalysts_async()

    def _clear_catalyst_cards(self):
        """Properly clear all catalyst cards from the layout."""
        if hasattr(self, 'catalyst_card_layout'):
            # Remove all widgets and delete them
            while self.catalyst_card_layout.count():
                child = self.catalyst_card_layout.takeAt(0)
                if child.widget():
                    widget = child.widget()
                    widget.setParent(None)
                    widget.deleteLater()

            # Process any pending deletions
            from PySide6.QtWidgets import QApplication
            QApplication.processEvents()

    def _update_catalyst_cards_with_data(self, catalysts_dict):
        """Update catalyst cards with loaded data."""
        if not hasattr(self, 'catalyst_card_layout'):
            return

        # Clear existing catalyst cards properly
        self._clear_catalyst_cards()

        # Add "All" card first
        all_card = QPushButton("All Catalysts")
        all_card.setStyleSheet("""
            QPushButton {
                background-color: #4096c1;
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #b089a1;
            }
        """)
        all_card.clicked.connect(lambda: self.filter_by_catalyst(None))
        self.catalyst_card_layout.addWidget(all_card)

        try:
            # Process catalysts from the loaded data
            catalysts = []
            if self.selected_database == "Both":
                # Combine catalysts from both databases and remove duplicates
                all_catalysts = []
                for db_catalysts in catalysts_dict.values():
                    all_catalysts.extend(db_catalysts)
                catalysts = list(set(all_catalysts))
            elif self.selected_database in catalysts_dict:
                catalysts = catalysts_dict[self.selected_database]
            else:
                # Fallback to first available dataset
                if catalysts_dict:
                    catalysts = list(catalysts_dict.values())[0]

            # Debug output
            print(f"Processing catalysts for {self.selected_database}: {len(catalysts)} found")

            # Add card for each catalyst
            if catalysts:
                for catalyst in sorted(catalysts):  # Sort for consistent ordering
                    if catalyst and catalyst.strip():
                        card = QPushButton(str(catalyst))
                        card.setStyleSheet("""
                            QPushButton {
                                background-color: #2d3748;
                                color: white;
                                border-radius: 8px;
                                padding: 8px 15px;
                                font-weight: bold;
                                min-width: 140px;
                            }
                            QPushButton:hover {
                                background-color: #4a5568;
                            }
                        """)
                        # Important: Use a default argument to capture the current value of catalyst
                        card.clicked.connect(lambda checked, c=catalyst: self.filter_by_catalyst(c))
                        self.catalyst_card_layout.addWidget(card)
            else:
                # If no catalysts found, add a placeholder
                no_catalysts = QLabel(f"No catalysts found in {self.selected_database}")
                no_catalysts.setStyleSheet("color: white;")
                self.catalyst_card_layout.addWidget(no_catalysts)

        except Exception as e:
            # Add error message if something goes wrong
            error_label = QLabel(f"Error loading catalysts: {str(e)}")
            error_label.setStyleSheet("color: red;")
            self.catalyst_card_layout.addWidget(error_label)
            logger.error(f"Error loading catalyst cards: {str(e)}", exc_info=True)

    def add_catalyst_filter_cards(self):
        """Add catalyst filter cards to the comparison dialog with horizontal scrolling."""
        # Create a scroll area for the catalyst cards
        scroll_area = QScrollArea(self.comparison_dialog)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setMaximumHeight(70)  # Limit height to prevent vertical expansion

        # Event filter to handle mouse wheel for horizontal scrolling
        scroll_area.installEventFilter(self)

        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            /* Horizontal scrollbar styling */
            QScrollBar:horizontal {
                border: none;
                height: 8px;
                margin: 0px;
                border-radius: 4px;
                background-color: #2d3748;
            }
            QScrollBar::handle:horizontal {
                background-color: #4096c1;
                border-radius: 4px;
                min-width: 30px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #b089a1;
            }
            QScrollBar::add-line:horizontal,
            QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            QScrollBar::add-page:horizontal,
            QScrollBar::sub-page:horizontal {
                background: none;
            }
        """)

        # Create a widget to hold the cards
        card_container = QWidget()
        card_container.setStyleSheet("background-color: transparent;")

        # Create a horizontal layout for the cards
        card_layout = QHBoxLayout(card_container)
        card_layout.setSpacing(10)
        card_layout.setContentsMargins(10, 10, 10, 10)

        # Store reference to layout for later updates
        self.catalyst_card_layout = card_layout

        # Populate initial catalyst cards
        self.update_catalyst_cards()

        # Add spacer to push cards to the left
        card_layout.addStretch()

        # Set the container as the scroll area's widget
        scroll_area.setWidget(card_container)

        # Insert scroll area before the table
        self.ui_data_comparision_dialog.verticalLayout.insertWidget(1, scroll_area)

    def eventFilter(self, obj, event):
        "Event filter to handle mouse wheel events for horizontal scrolling."
        if isinstance(obj, QScrollArea) and event.type() == QEvent.Wheel:
            # Check if this is our catalyst scroll area
            if obj.parent() == self.comparison_dialog:
                # Get horizontal scrollbar
                hbar = obj.horizontalScrollBar()
                if hbar and hbar.isVisible():
                    # Calculate scroll amount (delta/120 gives number of 15-degree steps)
                    delta = event.angleDelta().y() // 8
                    # Scroll horizontally instead of vertically
                    hbar.setValue(hbar.value() - delta)
                    # Prevent event from being handled further
                    return True
        return super().eventFilter(obj, event)

    def filter_by_catalyst(self, catalyst_name):
        """Filter the comparison table by catalyst name with async loading."""
        if catalyst_name is None:
            # Show all tests - use cached data if available
            cache_key = f"tests_{self.selected_database}"
            cached_data = self.data_cache.get(cache_key)
            if cached_data:
                self.update_comparison_dialong_table(cached_data)
            else:
                self._load_tests_async()
            return

        # Show loading progress
        if self.loading_progress:
            self.loading_progress.show()
            self.loading_progress.setValue(0)

        print(f"Filtering by catalyst '{catalyst_name}' in database '{self.selected_database}'")

        # Start async filtering
        if self.database_worker and self.database_worker.isRunning():
            self.database_worker.quit()
            self.database_worker.wait()

        self.database_worker = DatabaseWorker(
            self.multi_db_handler,
            self.db_handler,
            "filter_tests",
            catalyst_name=catalyst_name,
            database_selection=self.selected_database
        )

        # Connect signals
        self.database_worker.tests_loaded.connect(self._on_filtered_tests_loaded)
        self.database_worker.error_occurred.connect(self._on_error_occurred)
        self.database_worker.progress_updated.connect(self._on_progress_updated)
        self.database_worker.finished.connect(self._on_loading_complete)

        # Start the worker
        self.database_worker.start()

    def _on_filtered_tests_loaded(self, filtered_tests):
        """Handle loaded filtered test data."""
        print(f"Loaded {len(filtered_tests)} filtered tests")
        self.update_comparison_dialong_table(filtered_tests)

    def update_comparison_dialong_table(self, results):
        """Update comparison table with optimized batch processing."""
        try:
            # Check if comparison dialog is properly initialized
            if not self.comparison_dialog or not hasattr(self.ui_data_comparision_dialog, 'results_table_compare'):
                return

            table = self.ui_data_comparision_dialog.results_table_compare

            # Disable sorting and updates during batch operation for better performance
            table.setSortingEnabled(False)
            table.setUpdatesEnabled(False)

            print(f"The filetered compare results contain: {results}")

            try:
                table.setRowCount(len(results))

                # Batch process table items
                for i, result in enumerate(results):
                    # Process data once
                    test_no = str(result.get('test_no', ''))
                    test_date = str(result.get('test_date', ''))
                    catalyst_name = str(result.get('catalyst_name', ''))
                    prop_conc = f"{result.get('propellant_conc', 0):.2f}" if result.get(
                        'propellant_conc') is not None else ""
                    database_source = str(result.get('database_source', 'EM2'))
                    tank_temp = str(result.get('tank_temp', ''))

                    # Create and set items in batch
                    items = [
                        QTableWidgetItem(test_no),
                        QTableWidgetItem(test_date),
                        QTableWidgetItem(catalyst_name),
                        QTableWidgetItem(prop_conc),
                        QTableWidgetItem(database_source),
                        QTableWidgetItem(tank_temp)
                    ]

                    for j, item in enumerate(items):
                        table.setItem(i, j, item)

                # Re-enable updates and resize columns
                table.setUpdatesEnabled(True)
                table.resizeColumnsToContents()
                table.setSortingEnabled(True)

                logger.info(f"Updated table with {len(results)} records")

            finally:
                # Ensure updates are re-enabled even if an error occurs
                table.setUpdatesEnabled(True)
                table.setSortingEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error updating table: {str(e)}")
            logger.error(f"Error updating table: {str(e)}", exc_info=True)
            traceback.print_exc()

    def populate_comparison_table(self):
        """Populate the comparison table with all available tests (legacy method)."""
        # This method is now handled by async loading
        # Keep for backward compatibility but delegate to async loading
        print(f"populate_comparison_table called for database: {getattr(self, 'selected_database', 'NOT SET')}")

        # Check if comparison dialog is initialized
        if not self.comparison_dialog or not hasattr(self.ui_data_comparision_dialog, 'results_table_compare'):
            return

        # Use async loading instead
        self._start_async_data_loading()

    def filter_comparison_table(self):
        """Filter the comparison table based on search text."""
        search_text = self.ui_data_comparision_dialog.search_tab.text().lower()
        table = self.ui_data_comparision_dialog.results_table_compare

        for row in range(table.rowCount()):
            match_found = False
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break

            table.setRowHidden(row, not match_found)

    def add_test_to_comparison(self):
        """Add a test from the comparison table to the selected tests list."""
        table = self.ui_data_comparision_dialog.results_table_compare
        selected_rows = table.selectedItems()

        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select a test to add.")
            return

        # Get the test number and database source from the selected row
        row_index = selected_rows[0].row()
        test_no = table.item(row_index, 0).text()

        # Safely get database source from column 4
        database_item = table.item(row_index, 4) if table.columnCount() > 4 else None
        database_source = database_item.text() if database_item else "EM2"

        # Check if test is already selected
        test_info = {'test_no': test_no, 'database_source': database_source}
        if test_info in self.selected_tests:
            QMessageBox.information(self, "Info", f"Test {test_no} is already selected.")
            return

        # Check if it's the same as current test (same test number AND same database source)
        current_database_source = getattr(self, 'database_source', 'EM2')
        if test_no == str(self.test_no) and database_source == current_database_source:
            QMessageBox.warning(self, "Warning", "Cannot compare test with itself.")
            return

        # Add to selected tests
        self.selected_tests.append(test_info)
        self._update_selected_tests_display()
        self._update_compare_button_state()

    def select_test_for_comparison(self):
        """Legacy method for backward compatibility - now adds test to comparison."""
        self.add_test_to_comparison()

    def _add_selected_tests_display(self):
        """Add a display area for selected tests."""
        # Create a frame for selected tests
        selected_tests_frame = QFrame(self.comparison_dialog)
        selected_tests_frame.setStyleSheet("""
            QFrame {
                background-color: #2d3748;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        selected_tests_frame.setMaximumHeight(180)

        # Create layout for selected tests
        selected_tests_layout = QVBoxLayout(selected_tests_frame)

        # Add label
        selected_label = QLabel(f"Selected Tests for Comparison with Test {self.test_no}:")
        selected_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px; background: transparent;")
        selected_tests_layout.addWidget(selected_label)

        # Create horizontal scroll area for selected tests
        self.selected_tests_scroll = QScrollArea()
        self.selected_tests_scroll.setWidgetResizable(True)
        self.selected_tests_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.selected_tests_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.selected_tests_scroll.setMaximumHeight(100)
        self.selected_tests_scroll.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
                border-radius: 5px;
                min-height: 100px;
            }
            QScrollBar:horizontal {
                border: none;
                height: 8px;
                margin: 0px;
                border-radius: 4px;
                background-color: #2d3748;
            }
            QScrollBar::handle:horizontal {
                background-color: #4096c1;
                border-radius: 4px;
                min-width: 30px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #b089a1;
            }
        """)

        # Create container for selected test cards
        self.selected_tests_container = QWidget()
        self.selected_tests_layout = QHBoxLayout(self.selected_tests_container)
        self.selected_tests_layout.setSpacing(10)
        self.selected_tests_layout.setContentsMargins(5, 5, 5, 5)

        # Add initial message
        self._update_selected_tests_display()

        self.selected_tests_scroll.setWidget(self.selected_tests_container)
        selected_tests_layout.addWidget(self.selected_tests_scroll)

        # Insert the selected tests frame into the dialog
        dialog_layout = self.comparison_dialog.layout()
        if dialog_layout:
            dialog_layout.insertWidget(-1, selected_tests_frame)

    def _update_selected_tests_display(self):
        """Update the display of selected tests."""
        # Clear existing cards
        while self.selected_tests_layout.count():
            child = self.selected_tests_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        if not self.selected_tests:
            # Show message when no tests selected
            no_tests_label = QLabel("No tests selected. Double-click on a test above to add it.")
            no_tests_label.setStyleSheet("color: #888; font-style: italic; padding: 10px;")
            self.selected_tests_layout.addWidget(no_tests_label)
        else:
            # Show selected test cards
            for i, test_info in enumerate(self.selected_tests):
                card = self._create_selected_test_card(test_info, i)
                self.selected_tests_layout.addWidget(card)

        # Add spacer to push cards to the left
        self.selected_tests_layout.addStretch()

    def _create_selected_test_card(self, test_info, index):
        """Create a card for a selected test."""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: #fcf1ff;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
                position: relative;
            }
        """)
        card.setMinimumHeight(90)
        card.setMinimumWidth(140)

        # Use absolute positioning for better control
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(3, 3, 3, 3)  # Leave space for remove button

        # Test info
        print(f"Creating card for test: {test_info}")
        test_label = QLabel(f"Test {test_info['test_no']}")
        test_label.setMinimumHeight(15)
        test_label.setStyleSheet("color: black; font-weight: bold; font-size: 13px; background: transparent;")
        test_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(test_label)

        db_label = QLabel(f"({test_info['database_source']})")
        db_label.setMinimumHeight(15)
        db_label.setStyleSheet("color: green; font-size: 11px; background: transparent;")
        db_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card_layout.addWidget(db_label)

        # Remove button positioned in top-left corner for better visibility
        remove_btn = QPushButton("×")
        remove_btn.setStyleSheet("""
            QPushButton {
                background-color: #e53e3e;
                color: white;
                border: none;
                border-radius: 14px;
                font-weight: bold;
                font-size: 14px;
                min-width: 5px;
                min-height: 14px;
                max-width: 5px;
                max-height: 14px;
            }
            QPushButton:hover {
                background-color: #c53030;
            }
            QPushButton:pressed {
                background-color: #a02626;
            }
        """)
        remove_btn.clicked.connect(lambda: self._remove_selected_test(index))
        remove_btn.setParent(card)

        # Position remove button in top-left corner with some margin
        remove_btn.move(98, 4)

        return card

    def _remove_selected_test(self, index):
        """Remove a test from the selected tests list."""
        if 0 <= index < len(self.selected_tests):
            self.selected_tests.pop(index)
            self._update_selected_tests_display()
            self._update_compare_button_state()

    def _add_comparison_action_buttons(self):
        """Add action buttons for comparison operations."""
        # Create a frame for action buttons
        actions_frame = QFrame(self.comparison_dialog)
        actions_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
                padding: 10px;
            }
        """)

        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(10)

        # Clear selection button
        clear_btn = QPushButton("Clear All")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e53e3e;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c53030;
            }
        """)
        clear_btn.clicked.connect(self._clear_selected_tests)
        actions_layout.addWidget(clear_btn)

        actions_layout.addStretch()

        # Compare button
        compare_btn = QPushButton("Compare Selected Tests")
        compare_btn.setStyleSheet("""
            QPushButton {
                background-color: #38a169;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #2f855a;
            }
            QPushButton:disabled {
                background-color: #666;
                color: #999;
            }
        """)
        compare_btn.clicked.connect(self._start_multi_test_comparison)
        actions_layout.addWidget(compare_btn)

        # Store reference to enable/disable
        self.compare_btn = compare_btn
        self._update_compare_button_state()

        # Insert the actions frame into the dialog
        dialog_layout = self.comparison_dialog.layout()
        if dialog_layout:
            dialog_layout.addWidget(actions_frame)

    def _clear_selected_tests(self):
        """Clear all selected tests."""
        self.selected_tests.clear()
        self._update_selected_tests_display()
        self._update_compare_button_state()

    def _update_compare_button_state(self):
        """Update the state of the compare button based on selected tests."""
        if hasattr(self, 'compare_btn'):
            self.compare_btn.setEnabled(len(self.selected_tests) > 0)

    def _start_multi_test_comparison(self):
        """Start the multi-test comparison process."""
        if not self.selected_tests:
            QMessageBox.warning(self, "Warning", "Please select at least one test to compare.")
            return

        # Close the dialog
        self.comparison_dialog.accept()

        # Start the comparison with multiple tests
        self.compare_multiple_tests(self.selected_tests)

    def compare_multiple_tests(self, selected_tests):
        """Compare the current test with multiple selected tests."""
        if not selected_tests:
            QMessageBox.warning(self, "Warning", "No tests selected for comparison.")
            return

        print(f"Starting multi-test comparison with {len(selected_tests)} tests")

        # Collect all test data
        all_test_data = {}
        all_temperature_data = {}
        all_pressure_data = {}

        # Add current test data
        current_database_source = getattr(self, 'database_source', 'EM2')
        current_test_key = f"Test {self.test_no} ({current_database_source})"

        if self.multi_db_handler and current_database_source in ["EM1", "EM2"]:
            current_test_data = self.multi_db_handler.get_test_data_from_database(self.test_no, current_database_source)
            current_temp_data = self.multi_db_handler.get_temperature_data_from_database(
                current_test_data['test_id'], current_database_source) if current_test_data else None
            current_pressure_data = self.multi_db_handler.get_pressure_data_from_database(
                current_test_data['test_id'], current_database_source) if current_test_data else None
        else:
            current_test_data = self.db_handler.get_test_data(self.test_no)
            current_temp_data = self.db_handler.get_temperature_data(
                current_test_data['test_id']) if current_test_data else None
            current_pressure_data = self.db_handler.get_pressure_data(
                current_test_data['test_id']) if current_test_data else None

        if current_test_data:
            all_test_data[current_test_key] = current_test_data
            if current_temp_data is not None:
                all_temperature_data[current_test_key] = current_temp_data
            if current_pressure_data is not None:
                all_pressure_data[current_test_key] = current_pressure_data

        # Add selected tests data
        for test_info in selected_tests:
            test_no = test_info['test_no']
            database_source = test_info['database_source']
            test_key = f"Test {test_no} ({database_source})"

            if self.multi_db_handler and database_source in ["EM1", "EM2"]:
                test_data = self.multi_db_handler.get_test_data_from_database(test_no, database_source)
                temp_data = self.multi_db_handler.get_temperature_data_from_database(
                    test_data['test_id'], database_source) if test_data else None
                pressure_data = self.multi_db_handler.get_pressure_data_from_database(
                    test_data['test_id'], database_source) if test_data else None
            else:
                test_data = self.db_handler.get_test_data(test_no)
                temp_data = self.db_handler.get_temperature_data(test_data['test_id']) if test_data else None
                pressure_data = self.db_handler.get_pressure_data(test_data['test_id']) if test_data else None

            if test_data:
                all_test_data[test_key] = test_data
                if temp_data is not None:
                    all_temperature_data[test_key] = temp_data
                if pressure_data is not None:
                    all_pressure_data[test_key] = pressure_data

        if not all_test_data:
            QMessageBox.warning(self, "Warning", "Could not retrieve data for any of the selected tests.")
            return

        # Setup UI for multi-test comparison
        self._setup_multi_test_comparison_ui(selected_tests)
        self._create_multi_test_performance_table(all_test_data)

        # Add enhanced multi-test analysis
        self._create_enhanced_multi_test_analysis(all_test_data)

        # Setup plots if data is available
        if all_temperature_data:
            self._setup_multi_test_temperature_plots(all_temperature_data)

        if all_pressure_data:
            self._setup_multi_test_pressure_plots(all_pressure_data)

        # Show comparison view
        self.ui.frame_78.hide()
        self.ui.widget_9.show()

    def _setup_multi_test_comparison_ui(self, selected_tests):
        """Setup the UI for multi-test comparison."""
        # Update labels to show multiple tests
        test_numbers = [str(self.test_no)] + [test_info['test_no'] for test_info in selected_tests]
        self.ui.lblTestNumber_DataAnalysis_Compare.show()
        self.ui.lblTestNumber_DataAnalysis_Compare.setText(f"Comparing Tests: {', '.join(test_numbers)}")

        # Clear any existing scroll area and content first
        if hasattr(self, 'scroll_compare') and self.scroll_compare:
            while self.scroll_layout_compare.count():
                child = self.scroll_layout_compare.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            self.scroll_compare.deleteLater()
            self.scroll_compare = None

        # Clear any existing content from widget_9
        if self.ui.widget_9.layout():
            while self.ui.widget_9.layout().count():
                child = self.ui.widget_9.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            self.ui.widget_9.layout().deleteLater()

        # Scroll area for tables
        self.scroll_compare = QScrollArea()
        self.scroll_compare.setContentsMargins(20, 20, 20, 20)
        self.scroll_compare.setStyleSheet(self.scroll_content_style)
        self.scroll_compare.setWidgetResizable(True)
        scroll_content_compare = QWidget()
        self.scroll_layout_compare = QVBoxLayout(scroll_content_compare)
        self.scroll_compare.setWidget(scroll_content_compare)
        self.ui.widget_9.setLayout(QVBoxLayout())
        self.ui.widget_9.layout().addWidget(self.scroll_compare)

        # Add back button
        self._create_back_button()

    def _create_multi_test_performance_table(self, all_test_data):
        """Create a performance comparison table for multiple tests."""
        self.scroll_layout_compare.addWidget(self.create_section_header('Performance Comparison'))
        self.scroll_layout_compare.addSpacing(10)

        required_parameters = [
            'Thrust (mN)', 'Burn_time (s)', 'Total_impulse (Ns)', 'Specific_impulse (s)',
            'Coefficient_of_thrust', 'Mass_flow_rate (mg/s)', 'Chamber_pressure (mbar)',
            'Maximum_temperature (K)', 'Characteristic_velocity (m/s)', 'Vacuum_chamber_pressure (mbar)'
        ]

        # Prepare data for multi-test table
        performance_data = {}
        for test_key, test_data in all_test_data.items():
            if 'performance_data' in test_data:
                performance_data[test_key] = {key: test_data['performance_data'].get(key, 'N/A')
                                              for key in required_parameters}

        if performance_data:
            table = self.create_table_from_dict_for_multi_comparison(performance_data)
            self.scroll_layout_compare.addWidget(table)
            self.scroll_layout_compare.addSpacing(20)

    def create_table_from_dict_for_multi_comparison(self, data_dict):
        """Create a table for multi-test comparison with dynamic columns."""
        table = QTableWidget()
        table.setStyleSheet(self.table_style)

        test_names = list(data_dict.keys())
        num_tests = len(test_names)

        # Set column count: 1 for parameters + number of tests
        table.setColumnCount(num_tests + 1)

        # Get number of rows from first test data
        if test_names:
            table.setRowCount(len(data_dict[test_names[0]]))

        # Set headers
        headers = ["Parameter"] + test_names
        table.setHorizontalHeaderLabels(headers)

        # Populate table
        if test_names:
            # Get parameters from first test
            parameters = list(data_dict[test_names[0]].keys())

            for row_idx, param in enumerate(parameters):
                # Set parameter name in first column
                param_item = QTableWidgetItem(str(param).replace('_', ' ').title())
                table.setItem(row_idx, 0, param_item)

                # Set values for each test
                for col_idx, test_name in enumerate(test_names):
                    value = data_dict[test_name].get(param, 'N/A')
                    value_item = QTableWidgetItem(str(value))
                    table.setItem(row_idx, col_idx + 1, value_item)

        table.resizeColumnsToContents()
        table.setMinimumHeight(min(400, (table.rowHeight(0) * (len(parameters) + 5)) if test_names else 100))
        return table

    def _setup_multi_test_temperature_plots(self, all_temperature_data):
        """Setup temperature plots for multiple tests."""
        self.scroll_layout_compare.addSpacing(15)
        self.scroll_layout_compare.addWidget(self.create_section_header('Temperature Plots'))
        self.scroll_layout_compare.addSpacing(10)

        # Store data for plotting
        self.multi_temperature_data = all_temperature_data
        self.temperature_combos = {}

        # Create plot content frame
        plot_content_frame = QFrame()
        plot_content_frame.setStyleSheet("QFrame{background-color:#171717;}")
        plot_content_layout = QVBoxLayout(plot_content_frame)
        self.scroll_layout_compare.addWidget(plot_content_frame)

        # Create horizontally scrollable controls area
        controls_scroll = QScrollArea()
        controls_scroll.setWidgetResizable(True)
        controls_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        controls_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        controls_scroll.setMinimumHeight(125)  # Set minimum height
        controls_scroll.setMaximumHeight(125)  # Set maximum height

        controls_scroll.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:horizontal {
                border: none;
                height: 8px;
                margin: 0px;
                border-radius: 4px;
                background-color: #2d3748;
            }
            QScrollBar::handle:horizontal {
                background-color: #4096c1;
                border-radius: 4px;
                min-width: 30px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #b089a1;
            }
        """)

        # Create container for controls
        controls_container = QWidget()
        controls_layout = QHBoxLayout(controls_container)
        controls_layout.setSpacing(20)
        controls_layout.setContentsMargins(5, 5, 5, 5)

        # Create combo boxes for each test
        for test_key, temp_data in all_temperature_data.items():
            valid_cols = [col for col in temp_data.columns if col != 'time']
            if valid_cols:
                # Create test group
                test_group = QFrame()
                test_group.setStyleSheet("QFrame { background-color: #2d3748; border-radius: 8px; padding: 8px; }")
                test_group.setMinimumHeight(100)  # Set minimum height for consistency
                test_group.setMinimumWidth(180)  # Set minimum width for better layout
                test_group_layout = QVBoxLayout(test_group)
                test_group_layout.setSpacing(5)
                test_group_layout.setContentsMargins(8, 8, 8, 8)

                # Test label
                test_label = QLabel(test_key)
                test_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold; background: transparent;")
                test_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                test_group_layout.addWidget(test_label)

                # Combo box
                combo = QComboBox()
                combo.addItems(valid_cols)
                combo.setCurrentText('Tank Lid (°C)' if 'Tank Lid (°C)' in valid_cols else valid_cols[0])
                combo.setStyleSheet(self._get_combobox_style())
                combo.currentTextChanged.connect(self._update_multi_temperature_plot)
                test_group_layout.addWidget(combo)

                # Store combo reference
                self.temperature_combos[test_key] = combo
                controls_layout.addWidget(test_group)

        controls_layout.addStretch()
        controls_scroll.setWidget(controls_container)
        plot_content_layout.addWidget(controls_scroll)

        # Create plot frame
        self._create_multi_temperature_plot_frame(plot_content_frame)
        self._initialize_multi_temperature_plot()

    def _setup_multi_test_pressure_plots(self, all_pressure_data):
        """Setup pressure plots for multiple tests."""
        self.scroll_layout_compare.addSpacing(30)
        self.scroll_layout_compare.addWidget(self.create_section_header('Pressure Plots'))
        self.scroll_layout_compare.addSpacing(10)

        # Store data for plotting
        self.multi_pressure_data = all_pressure_data
        self.pressure_combos = {}

        # Create plot content frame
        pressure_content_frame = QFrame()
        pressure_content_frame.setStyleSheet("QFrame{background-color:#171717;}")
        pressure_content_layout = QVBoxLayout(pressure_content_frame)
        self.scroll_layout_compare.addWidget(pressure_content_frame)

        # Create horizontally scrollable controls area
        pressure_controls_scroll = QScrollArea()
        pressure_controls_scroll.setWidgetResizable(True)
        pressure_controls_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        pressure_controls_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        pressure_controls_scroll.setMinimumHeight(125)  # Set minimum height
        pressure_controls_scroll.setMaximumHeight(125)  # Set maximum height
        pressure_controls_scroll.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            QScrollBar:horizontal {
                border: none;
                height: 8px;
                margin: 0px;
                border-radius: 4px;
                background-color: #2d3748;
            }
            QScrollBar::handle:horizontal {
                background-color: #4096c1;
                border-radius: 4px;
                min-width: 30px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #b089a1;
            }
        """)

        # Create container for controls
        pressure_controls_container = QWidget()
        pressure_controls_layout = QHBoxLayout(pressure_controls_container)
        pressure_controls_layout.setSpacing(20)
        pressure_controls_layout.setContentsMargins(5, 5, 5, 5)

        # Create combo boxes for each test
        for test_key, pressure_data in all_pressure_data.items():
            valid_cols = [col for col in pressure_data.columns if col != 'time']
            if valid_cols:
                # Create test group
                test_group = QFrame()
                test_group.setStyleSheet("QFrame { background-color: #2d3748; border-radius: 8px; padding: 8px; }")
                test_group.setMinimumHeight(100)  # Set minimum height for consistency
                test_group.setMinimumWidth(180)  # Set minimum width for better layout
                test_group_layout = QVBoxLayout(test_group)
                test_group_layout.setSpacing(5)
                test_group_layout.setContentsMargins(8, 8, 8, 8)

                # Test label
                test_label = QLabel(test_key)
                test_label.setStyleSheet("color: #FFFFFF; font-size: 12px; font-weight: bold; background: transparent;")
                test_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                test_group_layout.addWidget(test_label)

                # Combo box
                combo = QComboBox()
                combo.addItems(valid_cols)
                combo.setCurrentText(valid_cols[0] if valid_cols else "")
                combo.setStyleSheet(self._get_combobox_style())
                combo.currentTextChanged.connect(self._update_multi_pressure_plot)
                test_group_layout.addWidget(combo)

                # Store combo reference
                self.pressure_combos[test_key] = combo
                pressure_controls_layout.addWidget(test_group)

        pressure_controls_layout.addStretch()
        pressure_controls_scroll.setWidget(pressure_controls_container)
        pressure_content_layout.addWidget(pressure_controls_scroll)

        # Create plot frame
        self._create_multi_pressure_plot_frame(pressure_content_frame)
        self._initialize_multi_pressure_plot()

    def _create_multi_temperature_plot_frame(self, plot_content_frame):
        """Create the temperature plot frame for multi-test comparison."""
        plot_content_layout = plot_content_frame.layout()

        plot_frame = QFrame()
        plot_frame.setStyleSheet('''
            QFrame {
                background-color: #fcf1ff;
                border-radius: 10px;
            }
        ''')
        plot_content_layout.addWidget(plot_frame)
        plot_frame_layout = QVBoxLayout(plot_frame)
        plot_frame_layout.setContentsMargins(0, 10, 0, 0)

        self.multi_temp_plot_widget = QWidget()
        self.multi_temp_plot_layout = QVBoxLayout(self.multi_temp_plot_widget)
        self.multi_temp_plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
        plot_frame_layout.addWidget(self.multi_temp_plot_widget)

    def _create_multi_pressure_plot_frame(self, pressure_content_frame):
        """Create the pressure plot frame for multi-test comparison."""
        pressure_content_layout = pressure_content_frame.layout()

        pressure_plot_frame = QFrame()
        pressure_plot_frame.setStyleSheet('''
            QFrame {
                background-color: #fcf1ff;
                border-radius: 10px;
            }
        ''')
        pressure_content_layout.addWidget(pressure_plot_frame)
        pressure_plot_frame_layout = QVBoxLayout(pressure_plot_frame)
        pressure_plot_frame_layout.setContentsMargins(0, 10, 0, 0)

        self.multi_pressure_plot_widget = QWidget()
        self.multi_pressure_plot_layout = QVBoxLayout(self.multi_pressure_plot_widget)
        self.multi_pressure_plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
        pressure_plot_frame_layout.addWidget(self.multi_pressure_plot_widget)

    def _initialize_multi_temperature_plot(self):
        """Initialize the multi-test temperature plot."""
        self.multi_temp_plot_canvas = None
        self.multi_temp_toolbar = None
        self._update_multi_temperature_plot()

    def _initialize_multi_pressure_plot(self):
        """Initialize the multi-test pressure plot."""
        self.multi_pressure_plot_canvas = None
        self.multi_pressure_toolbar = None
        self._update_multi_pressure_plot()

    def _update_multi_temperature_plot(self, *args):
        """Update the multi-test temperature plot."""
        # Clear previous plot and toolbar if they exist
        if hasattr(self, 'multi_temp_plot_canvas') and self.multi_temp_plot_canvas:
            self.multi_temp_plot_layout.removeWidget(self.multi_temp_plot_canvas)
            self.multi_temp_plot_canvas.deleteLater()
        if hasattr(self, 'multi_temp_toolbar') and self.multi_temp_toolbar:
            self.multi_temp_plot_layout.removeWidget(self.multi_temp_toolbar)
            self.multi_temp_toolbar.deleteLater()

        # Create new plot
        figure, axes = plt.subplots(figsize=(12, 4))

        # Plot data for each test
        plot_labels = {}
        colors = plt.cm.tab10(range(len(self.temperature_combos)))  # Use different colors for each test

        for i, (test_key, combo) in enumerate(self.temperature_combos.items()):
            selected_column = combo.currentText()
            temp_data = self.multi_temperature_data[test_key]

            if selected_column in temp_data.columns:
                axes.plot(
                    temp_data['time'],
                    temp_data[selected_column],
                    label=f'{test_key}: {selected_column}',
                    linewidth=2,
                    alpha=0.8,
                    color=colors[i]
                )
                plot_labels[f'{test_key}_{selected_column}'] = selected_column

        # Customize plot appearance
        figure.patch.set_alpha(0.0)
        axes.patch.set_alpha(0.0)

        self.plot_style(axes, 'Time (s)', 'Temperature (°C)', 'Multi-Test Temperature Comparison')

        # Create canvas and toolbar
        self.multi_temp_plot_canvas = PlotCanvas(figure)
        self.multi_temp_plot_canvas.setStyleSheet("background-color: transparent;")
        self.multi_temp_toolbar = CustomNavigationToolbar(
            self.multi_temp_plot_canvas, self.multi_temp_plot_widget, plot_labels
        )

        # Add toolbar and canvas to layout
        self.multi_temp_plot_layout.setContentsMargins(20, 20, 20, 20)
        self.multi_temp_plot_layout.addWidget(self.multi_temp_toolbar)
        self.multi_temp_plot_layout.addWidget(self.multi_temp_plot_canvas)

        # Ensure the plot is updated
        self.multi_temp_plot_canvas.draw()

    def _update_multi_pressure_plot(self, *args):
        """Update the multi-test pressure plot."""
        # Clear previous plot and toolbar if they exist
        if hasattr(self, 'multi_pressure_plot_canvas') and self.multi_pressure_plot_canvas:
            self.multi_pressure_plot_layout.removeWidget(self.multi_pressure_plot_canvas)
            self.multi_pressure_plot_canvas.deleteLater()
        if hasattr(self, 'multi_pressure_toolbar') and self.multi_pressure_toolbar:
            self.multi_pressure_plot_layout.removeWidget(self.multi_pressure_toolbar)
            self.multi_pressure_toolbar.deleteLater()

        # Create new plot
        figure, axes = plt.subplots(figsize=(12, 4))

        # Plot data for each test
        plot_labels = {}
        colors = plt.cm.tab10(range(len(self.pressure_combos)))  # Use different colors for each test

        for i, (test_key, combo) in enumerate(self.pressure_combos.items()):
            selected_column = combo.currentText()
            pressure_data = self.multi_pressure_data[test_key]

            if selected_column in pressure_data.columns:
                axes.plot(
                    pressure_data['time'],
                    pressure_data[selected_column],
                    label=f'{test_key}: {selected_column}',
                    linewidth=2,
                    alpha=0.8,
                    color=colors[i]
                )
                plot_labels[f'{test_key}_{selected_column}'] = selected_column

        # Customize plot appearance
        figure.patch.set_alpha(0.0)
        axes.patch.set_alpha(0.0)

        self.plot_style(axes, 'Time (s)', 'Pressure (mbar)', 'Multi-Test Pressure Comparison')

        # Create canvas and toolbar
        self.multi_pressure_plot_canvas = PlotCanvas(figure)
        self.multi_pressure_plot_canvas.setStyleSheet("background-color: transparent;")
        self.multi_pressure_toolbar = CustomNavigationToolbar(
            self.multi_pressure_plot_canvas, self.multi_pressure_plot_widget, plot_labels
        )

        # Add toolbar and canvas to layout
        self.multi_pressure_plot_layout.setContentsMargins(20, 20, 20, 20)
        self.multi_pressure_plot_layout.addWidget(self.multi_pressure_toolbar)
        self.multi_pressure_plot_layout.addWidget(self.multi_pressure_plot_canvas)

        # Ensure the plot is updated
        self.multi_pressure_plot_canvas.draw()

    def plot_style(self, ax, x_label, y_label, title):

        # Update plot settings
        ax.set_title(title,
                     color='#09090b',
                     fontsize=12,
                     fontweight='bold')

        ax.set_xlabel(x_label,
                      color='#09090b',
                      fontsize=10,
                      fontweight='bold')
        ax.set_ylabel(y_label,
                      color='#09090b',
                      fontsize=10,
                      fontweight='bold')

        legend = ax.legend(loc='best',
                           ncol=3,
                           columnspacing=1,
                           handletextpad=0.5,
                           borderaxespad=0.5,
                           facecolor='none',  # Transparent background
                           edgecolor='#446699',  # Border color
                           framealpha=0.7  # Semi-transparent frame
                           )
        # Setting legend text color
        for text in legend.get_texts():
            text.set_color('#09090b')

        # Style the axis numbers and grid
        ax.tick_params(axis='both',
                       colors='#09090b',  # White tick labels
                       labelsize=9)
        # Major grids
        ax.grid(which='major', linestyle='-', linewidth='0.75', alpha=0.6, color='#666666')  # Darker grid lines

        # Minor grids
        ax.minorticks_on()
        ax.grid(which='minor', linestyle=':', linewidth='0.5', color='#1e1e1e', alpha=0.6)

        ax.spines['top'].set_visible(True)
        ax.spines['right'].set_visible(True)

        # Style the spines (axis lines)
        for spine in ax.spines.values():
            spine.set_color('#446699')  # Custom spine color

    def show_complete_test_data(self):
        self.ui.frame_78.show()
        self.ui.widget_9.hide()
        self.ui.lblTestNumber_DataAnalysis_Compare.hide()

        # Deleting each and every elements from the scroll area and also the scroll area itself
        if self.scroll_compare:
            while self.scroll_layout_compare.count():
                child = self.scroll_layout_compare.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            self.scroll_compare.deleteLater()
            self.scroll_compare = None

    def compare_tests(self, test_no, test_no_2=None, database_source_2="EM2"):
        """Main method to compare two tests. Orchestrates the comparison process."""
        # Validate test selection
        test_no_2 = self._validate_test_selection(test_no, test_no_2, database_source_2)
        if test_no_2 is None:
            return

        # Get test data - use appropriate database handler based on source
        # For the current test, use the known database source
        current_database_source = getattr(self, 'database_source', 'EM2')
        print(f"Current test {test_no} database source: {current_database_source}")
        print(f"DataVisualizationWidget database_source attribute: {getattr(self, 'database_source', 'NOT SET')}")

        if self.multi_db_handler and current_database_source in ["EM1", "EM2"]:
            print(f"Using multi-database handler to get test {test_no} from {current_database_source}")
            test_data = self.multi_db_handler.get_test_data_from_database(test_no, current_database_source)
        else:
            print(f"Using regular database handler to get test {test_no}")
            test_data = self.db_handler.get_test_data(test_no)

        # Get second test data from specified database
        if self.multi_db_handler and database_source_2 in ["EM1", "EM2"]:
            test_data_2 = self.multi_db_handler.get_test_data_from_database(test_no_2, database_source_2)
        else:
            test_data_2 = self.db_handler.get_test_data(test_no_2)

        print(f"Comparing tests {test_no} ({current_database_source}) and {test_no_2} ({database_source_2})")
        print(f"Test data for {test_no}: {'Found' if test_data else 'None'}")
        print(f"Test data for {test_no_2}: {'Found' if test_data_2 else 'None'}")

        if not test_data:
            print(f"Failed to retrieve test data for test {test_no} from {current_database_source}")
            QMessageBox.warning(self, "Warning",
                                f"Could not retrieve test data for test {test_no} from {current_database_source} database.")
            return

        if not test_data_2:
            print(f"Failed to retrieve test data for test {test_no_2} from {database_source_2}")
            QMessageBox.warning(self, "Warning",
                                f"Could not retrieve test data for test {test_no_2} from {database_source_2} database.")
            return

        # Setup UI components
        self._setup_comparison_ui(test_no_2)
        self._create_performance_comparison_table(test_no, test_no_2, test_data, test_data_2)

        # Setup temperature plotting - get data from appropriate databases
        # Get temperature data for current test from its database
        if self.multi_db_handler and current_database_source in ["EM1", "EM2"]:
            temperature_data = self.multi_db_handler.get_temperature_data_from_database(test_data['test_id'],
                                                                                        current_database_source)
        else:
            temperature_data = self.db_handler.get_temperature_data(test_data['test_id'])

        if self.multi_db_handler and database_source_2 in ["EM1", "EM2"]:
            temperature_data_2 = self.multi_db_handler.get_temperature_data_from_database(test_data_2['test_id'],
                                                                                          database_source_2)
        else:
            temperature_data_2 = self.db_handler.get_temperature_data(test_data_2['test_id'])

        if self._setup_temperature_plot_section(temperature_data, temperature_data_2):
            plot_content_frame = self._create_plot_controls(test_no, test_no_2, temperature_data, temperature_data_2)
            self._create_plot_frame(plot_content_frame)
            self._initialize_temperature_plot(temperature_data, temperature_data_2, test_no, test_no_2)

        # Add spacing between temperature and pressure sections
        self.scroll_layout_compare.addSpacing(30)

        # Setup pressure plotting - get data from appropriate databases
        # Get pressure data for current test from its database
        if self.multi_db_handler and current_database_source in ["EM1", "EM2"]:
            pressure_data = self.multi_db_handler.get_pressure_data_from_database(test_data['test_id'],
                                                                                  current_database_source)
        else:
            pressure_data = self.db_handler.get_pressure_data(test_data['test_id'])

        if self.multi_db_handler and database_source_2 in ["EM1", "EM2"]:
            pressure_data_2 = self.multi_db_handler.get_pressure_data_from_database(test_data_2['test_id'],
                                                                                    database_source_2)
        else:
            pressure_data_2 = self.db_handler.get_pressure_data(test_data_2['test_id'])

        if self._setup_pressure_plot_section(pressure_data, pressure_data_2):
            pressure_content_frame = self._create_pressure_plot_controls(test_no, test_no_2, pressure_data,
                                                                         pressure_data_2)
            self._create_pressure_plot_frame(pressure_content_frame)
            self._initialize_pressure_plot(pressure_data, pressure_data_2, test_no, test_no_2)

        # Show comparison view
        self.ui.frame_78.hide()
        self.ui.widget_9.show()

    def _validate_test_selection(self, test_no, test_no_2, database_source_2="EM2"):
        """Validate and get the second test number for comparison."""
        if test_no_2 is None:
            # Get all test numbers
            test_numbers = self.db_handler.get_all_test_numbers()
            if not test_numbers:
                QMessageBox.information(self, "Info", "No test data found in database.")
                return None

            # Creating a dialog for test number selection
            test_no_2, ok = QInputDialog.getItem(
                self,
                "Compare Test Data",
                "Select Test Number:",
                [str(num) for num in test_numbers],  # Convert to strings for display
                0,
                False
            )

            if not ok:
                return None

        # Check if it's the same test (same test number AND same database source)
        current_database_source = getattr(self, 'database_source', 'EM2')
        if str(test_no) == str(test_no_2) and current_database_source == database_source_2:
            QMessageBox.warning(self, "Warning", "Please select a different test number to compare.")
            return None

        return test_no_2

    def _setup_comparison_ui(self, test_no_2):
        """Setup the basic UI structure for comparison."""
        self.ui.lblTestNumber_DataAnalysis_Compare.show()
        self.ui.lblTestNumber_DataAnalysis_Compare.setText(f"Test Number: {test_no_2}")

        # Clear any existing scroll area and content first
        if hasattr(self, 'scroll_compare') and self.scroll_compare:
            while self.scroll_layout_compare.count():
                child = self.scroll_layout_compare.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            self.scroll_compare.deleteLater()
            self.scroll_compare = None

        # Clear any existing content from widget_9
        if self.ui.widget_9.layout():
            while self.ui.widget_9.layout().count():
                child = self.ui.widget_9.layout().takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
            self.ui.widget_9.layout().deleteLater()

        # Scroll area for tables
        self.scroll_compare = QScrollArea()
        self.scroll_compare.setStyleSheet(self.scroll_content_style)
        self.scroll_compare.setWidgetResizable(True)
        scroll_content_compare = QWidget()
        self.scroll_layout_compare = QVBoxLayout(scroll_content_compare)
        self.scroll_compare.setWidget(scroll_content_compare)
        self.ui.widget_9.setLayout(QVBoxLayout())
        self.ui.widget_9.layout().addWidget(self.scroll_compare)

        # Add back button using the dedicated method
        self._create_back_button()

    def _create_back_button(self):
        """Create and style the back button."""
        back_button = QPushButton('Back')
        back_button.setStyleSheet('''
            QPushButton{
                background-color:#4096c1;
                border-radius:6px;
                padding:5px;
                font-size: 17px;
                font-family: Arial;
            }

            QPushButton:hover{
                background-color:#b089a1;
            }

            QPushButton:pressed{
                background-color:b03781;
            }
        ''')
        back_button.setFixedHeight(40)
        back_button.setFixedWidth(100)
        back_button.clicked.connect(self.show_complete_test_data)
        self.scroll_layout_compare.addWidget(back_button)
        self.scroll_layout_compare.addSpacing(10)

    def _create_performance_comparison_table(self, test_no, test_no_2, test_data, test_data_2):
        """Create the performance comparison table."""

        self.scroll_layout_compare.addWidget(self.create_section_header('Performance'))
        self.scroll_layout_compare.addSpacing(10)

        required_parameters = [
            'Thrust (mN)', 'Burn_time (s)', 'Total_impulse (Ns)', 'Specific_impulse (s)',
            'Coefficient_of_thrust', 'Mass_flow_rate (mg/s)', 'Chamber_pressure (mbar)',
            'Maximum_temperature (K)', 'Characteristic_velocity (m/s)', 'Vacuum_chamber_pressure (mbar)'
        ]

        test_A_performance = {key: test_data['performance_data'][key] for key in required_parameters}
        test_B_performance = {key: test_data_2['performance_data'][key] for key in required_parameters}
        combined_performance = {f'Test {test_no}': test_A_performance, f'Test {test_no_2}': test_B_performance}

        print(f"Combined performance: {combined_performance}")
        table = self.create_table_from_dict_for_comparison(combined_performance)
        self.scroll_layout_compare.addWidget(table)
        self.scroll_layout_compare.addSpacing(20)

        # Add enhanced analysis sections
        self._create_enhanced_analysis_sections(test_no, test_no_2, test_data, test_data_2)

    def _setup_temperature_plot_section(self, temperature_data, temperature_data_2):
        """Setup the temperature plot section and validate data."""
        self.scroll_layout_compare.addSpacing(15)
        self.scroll_layout_compare.addWidget(self.create_section_header('Temperature Plots'))
        self.scroll_layout_compare.addSpacing(10)

        valid_cols_A = [col for col in temperature_data.columns if col != 'time']
        valid_cols_B = [col for col in temperature_data_2.columns if col != 'time']

        if not valid_cols_A or not valid_cols_B:
            QMessageBox.warning(self, "Warning", "No valid temperature columns found in one or both test datasets.")
            return False

        return True

    def _setup_pressure_plot_section(self, pressure_data, pressure_data_2):
        """setup the pressure plot section and validate data."""
        self.scroll_layout_compare.addWidget(self.create_section_header('Pressure Plots'))
        self.scroll_layout_compare.addSpacing(10)

        valid_cols_A = [col for col in pressure_data.columns if col != 'time']
        valid_cols_B = [col for col in pressure_data_2.columns if col != 'time']

        if not valid_cols_A or not valid_cols_B:
            QMessageBox.warning(self, "Warning", "No valid pressure columns found in one or both test datasets.")
            return False

        return True

    def _create_pressure_plot_controls(self, test_no, test_no_2, pressure_data, pressure_data_2):
        """Create pressure plot controls widgets (combo boxes and labels)."""
        valid_cols_A = [col for col in pressure_data.columns if col != 'time']
        valid_cols_B = [col for col in pressure_data_2.columns if col != 'time']

        plot_content_frame = QFrame()
        plot_content_frame.setStyleSheet("QFrame{background-color:#171717;}")
        plot_content_layout = QVBoxLayout(plot_content_frame)
        self.scroll_layout_compare.addWidget(plot_content_frame)

        plot_top_frame = QFrame()
        plot_top_frame_layout = QHBoxLayout(plot_top_frame)
        plot_content_layout.addWidget(plot_top_frame)

        # Create spacers
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        # Test A controls
        plot_top_frame_layout.addItem(spacer)
        test_A_label = QLabel(f'Test {test_no}:')
        test_A_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(test_A_label)

        self.pressure_A_plot_combo = QComboBox()
        self.pressure_A_plot_combo.addItems(valid_cols_A)
        self.pressure_A_plot_combo.setCurrentText(
            'Tank Lid (°C)' if 'Tank Lid (°C)' in valid_cols_A else valid_cols_A[0])
        self.pressure_A_plot_combo.setStyleSheet(self._get_combobox_style())
        plot_top_frame_layout.addWidget(self.pressure_A_plot_combo)

        plot_top_frame_layout.addItem(spacer_3)

        # Test B controls
        test_B_label = QLabel(f'Test {test_no_2}:')
        test_B_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(test_B_label)

        self.pressure_B_plot_combo = QComboBox()
        self.pressure_B_plot_combo.addItems(valid_cols_B)
        self.pressure_B_plot_combo.setCurrentText(valid_cols_B[0] if valid_cols_B else "")
        self.pressure_B_plot_combo.setStyleSheet(self._get_combobox_style())
        plot_top_frame_layout.addWidget(self.pressure_B_plot_combo)

        plot_top_frame_layout.addItem(spacer_2)

        return plot_content_frame

    def _initialize_pressure_plot(self, pressure_data, pressure_data_2, test_no, test_no_2):
        """Initialize the pressure plot with data and connections."""
        # Store pressure data for use in update method
        self.pressure_data = pressure_data
        self.pressure_data_2 = pressure_data_2
        self.pressure_plot_canvas = None
        self.pressure_toolbar = None

        # Connect combo box signals to update plot method
        self.pressure_A_plot_combo.currentTextChanged.connect(self.update_pressure_plot)
        self.pressure_B_plot_combo.currentTextChanged.connect(self.update_pressure_plot)

        # Initial plot creation
        self.update_pressure_plot()

    def update_pressure_plot(self, *args):
        """Update the pressure plot based on the selected columns from both combo boxes."""
        # Clear previous plot and toolbar if they exist
        if hasattr(self, 'pressure_plot_canvas') and self.pressure_plot_canvas:
            self.pressure_widget_layout.removeWidget(self.pressure_plot_canvas)
            self.pressure_plot_canvas.deleteLater()
        if hasattr(self, 'pressure_toolbar') and self.pressure_toolbar:
            self.pressure_widget_layout.removeWidget(self.pressure_toolbar)
            self.pressure_toolbar.deleteLater()

        # Get selected columns from both combo boxes
        selected_column_A = self.pressure_A_plot_combo.currentText()
        selected_column_B = self.pressure_B_plot_combo.currentText()

        # Validate selected columns
        if selected_column_A not in self.pressure_data.columns:
            QMessageBox.warning(self, "Warning",
                                f"Selected column '{selected_column_A}' not found in Test {self.test_no} dataset.")
            return
        if selected_column_B not in self.pressure_data_2.columns:
            QMessageBox.warning(self, "Warning",
                                f"Selected column '{selected_column_B}' not found in Test {self.test_no_2} dataset.")
            return

        # Create new plot
        figure, axes = plt.subplots(figsize=(9, 3))
        axes.plot(
            self.pressure_data['time'],
            self.pressure_data[selected_column_A],
            label=f'Test {self.test_no}: {selected_column_A}',
            linewidth=1,
            alpha=0.7  # Semi-transparent lines
        )
        axes.plot(
            self.pressure_data_2['time'],
            self.pressure_data_2[selected_column_B],
            label=f'Test {self.test_no_2}: {selected_column_B}',
            linewidth=1,
            alpha=0.7  # Semi-transparent lines
        )

        # Customize plot appearance
        figure.patch.set_alpha(0.0)
        axes.patch.set_alpha(0.0)
        axes.legend()

        self.plot_style(axes, 'Time (s)', 'Pressure (mbar)', 'Pressure Plot')

        # Create canvas and toolbar
        self.pressure_plot_canvas = PlotCanvas(figure)
        self.pressure_plot_canvas.setStyleSheet("background-color: transparent;")
        self.pressure_toolbar = CustomNavigationToolbar(
            self.pressure_plot_canvas, self.pressure_widget,
            {selected_column_A: selected_column_A, selected_column_B: selected_column_B}
        )

        # Add toolbar and canvas to layout - ensure they're added in the correct order
        self.pressure_widget_layout.setContentsMargins(20, 20, 20, 20)
        self.pressure_widget_layout.addWidget(self.pressure_toolbar)
        self.pressure_widget_layout.addWidget(self.pressure_plot_canvas)

        # Ensure the plot is updated
        self.pressure_plot_canvas.draw()

    def _create_enhanced_analysis_sections(self, test_no, test_no_2, test_data, test_data_2):
        """Create enhanced analysis sections with new comparison capabilities."""
        try:
            # Import the new analyzers
            from src.analysis import HeaterAnalyzer, ThermalAnalyzer, EfficiencyAnalyzer, ComparisonAnalyzer

            # Initialize analyzers
            heater_analyzer = HeaterAnalyzer()
            thermal_analyzer = ThermalAnalyzer()
            efficiency_analyzer = EfficiencyAnalyzer()
            comparison_analyzer = ComparisonAnalyzer()

            # Perform enhanced analysis for both tests
            analysis_results_1 = self._perform_enhanced_analysis(test_data, heater_analyzer, thermal_analyzer,
                                                                 efficiency_analyzer)
            analysis_results_2 = self._perform_enhanced_analysis(test_data_2, heater_analyzer, thermal_analyzer,
                                                                 efficiency_analyzer)

            # Create comparison sections
            self._create_heater_comparison_section(test_no, test_no_2, analysis_results_1, analysis_results_2)
            self._create_thermal_comparison_section(test_no, test_no_2, analysis_results_1, analysis_results_2)
            self._create_efficiency_comparison_section(test_no, test_no_2, analysis_results_1, analysis_results_2)

            # Create comparison plots following the same pattern as temperature/pressure plots
            self._setup_comparison_plots_section(test_no, test_no_2, test_data, test_data_2, analysis_results_1,
                                                 analysis_results_2)

            # Perform comprehensive comparison analysis
            test_data_list = [test_data, test_data_2]
            analysis_results_list = [analysis_results_1, analysis_results_2]
            comparison_results = comparison_analyzer.compare_multiple_tests(test_data_list, analysis_results_list)

            self._create_comparison_insights_section(comparison_results)

        except Exception as e:
            print(f"Error creating enhanced analysis sections: {str(e)}")
            # Add error message to UI
            error_label = QLabel(f"Enhanced analysis unavailable: {str(e)}")
            error_label.setStyleSheet("color: #ff6b6b; font-style: italic;")
            self.scroll_layout_compare.addWidget(error_label)

    def _perform_enhanced_analysis(self, test_data, heater_analyzer, thermal_analyzer, efficiency_analyzer):
        """Perform enhanced analysis on a single test."""
        try:
            results = {}

            # Heater analysis
            heater_cycles = test_data.get('heater_cycles', [])
            heater_info = test_data.get('heater_info', {})

            if heater_cycles and heater_info:
                heater_results = heater_analyzer.analyze_heater_cycles(heater_cycles, heater_info)
                results['heater_analysis'] = heater_analyzer.get_summary_metrics()

            # Thermal analysis (would need temperature data)
            # For now, create placeholder thermal analysis
            results['thermal_analysis'] = {
                'max_temperature_rise_rate': 0,
                'temperature_stability_score': 75,  # Default values
                'spatial_consistency_score': 80,
                'average_thermal_time_constant': 10,
                'max_temperature_difference': 5
            }

            # Efficiency analysis
            performance_data = test_data.get('performance_data', {})
            if performance_data:
                efficiency_results = efficiency_analyzer.analyze_system_efficiency(
                    test_data, performance_data, results.get('heater_analysis'), results.get('thermal_analysis'))
                # results['efficiency_analysis'] = efficiency_analyzer.get_efficiency_summary()
                results['efficiency_analysis'] = efficiency_results

            return results

        except Exception as e:
            print(f"Error in enhanced analysis: {str(e)}")
            return {}

    def _create_heater_comparison_section(self, test_no, test_no_2, analysis_1, analysis_2):
        """Create heater performance comparison section."""
        try:
            self.scroll_layout_compare.addWidget(self.create_section_header('Heater Performance Analysis'))
            self.scroll_layout_compare.addSpacing(10)

            heater_1 = analysis_1.get('heater_analysis', {})
            heater_2 = analysis_2.get('heater_analysis', {})

            if not heater_1 and not heater_2:
                no_data_label = QLabel("No heater analysis data available")
                no_data_label.setStyleSheet("color: #888; font-style: italic;")
                self.scroll_layout_compare.addWidget(no_data_label)
                return

            # Create heater comparison table
            heater_metrics = [
                'Total Heater On Time (s)', 'Average Cycle Duration (s)', 'First Cycle Duration (s)',
                'Total Energy Consumed (kWh)', 'Average Cut-off Temperature (°C)',
                'Thermal Efficiency Score', 'Consistency Score'
            ]

            heater_data_keys = [
                'total_heater_on_time', 'average_cycle_duration', 'first_cycle_duration',
                'total_energy_consumed', 'average_cut_off_temperature', 'thermal_efficiency', 'consistency_score'
            ]

            heater_comparison_data = {}
            for i, (metric, key) in enumerate(zip(heater_metrics, heater_data_keys)):
                heater_comparison_data[metric] = {
                    f'Test {test_no}': heater_1.get(key, 'N/A'),
                    f'Test {test_no_2}': heater_2.get(key, 'N/A')
                }

            heater_table = self.create_table_from_dict_for_comparison(heater_comparison_data)
            self.scroll_layout_compare.addWidget(heater_table)
            self.scroll_layout_compare.addSpacing(20)

        except Exception as e:
            print(f"Error creating heater comparison section: {str(e)}")

    def _create_thermal_comparison_section(self, test_no, test_no_2, analysis_1, analysis_2):
        """Create thermal characteristics comparison section."""
        try:
            self.scroll_layout_compare.addWidget(self.create_section_header('Thermal Response Analysis'))
            self.scroll_layout_compare.addSpacing(10)

            thermal_1 = analysis_1.get('thermal_analysis', {})
            thermal_2 = analysis_2.get('thermal_analysis', {})

            # Create thermal comparison table
            thermal_metrics = [
                'Max Temperature Rise Rate (°C/s)', 'Temperature Stability Score (%)',
                'Spatial Consistency Score (%)', 'Average Thermal Time Constant (s)',
                'Max Temperature Difference (°C)'
            ]

            thermal_data_keys = [
                'max_temperature_rise_rate', 'temperature_stability_score', 'spatial_consistency_score',
                'average_thermal_time_constant', 'max_temperature_difference'
            ]

            thermal_comparison_data = {}
            for metric, key in zip(thermal_metrics, thermal_data_keys):
                thermal_comparison_data[metric] = {
                    f'Test {test_no}': thermal_1.get(key, 'N/A'),
                    f'Test {test_no_2}': thermal_2.get(key, 'N/A')
                }

            thermal_table = self.create_table_from_dict_for_comparison(thermal_comparison_data)
            self.scroll_layout_compare.addWidget(thermal_table)
            self.scroll_layout_compare.addSpacing(20)

        except Exception as e:
            print(f"Error creating thermal comparison section: {str(e)}")

    def _create_efficiency_comparison_section(self, test_no, test_no_2, analysis_1, analysis_2):
        """Create efficiency metrics comparison section."""
        try:
            self.scroll_layout_compare.addWidget(self.create_section_header('System Efficiency Analysis'))
            self.scroll_layout_compare.addSpacing(10)

            efficiency_1 = analysis_1.get('efficiency_analysis', {})
            efficiency_2 = analysis_2.get('efficiency_analysis', {})

            # Create efficiency comparison table
            efficiency_metrics = [
                'Energy Utilization Score (%)', 'Propellant Utilization (%)',
                'Thermal Efficiency Score (%)', 'Performance Index (%)',
                'Optimization Potential (%)'
            ]

            efficiency_data_keys = [
                'energy_utilization_score', 'propellant_utilization', 'thermal_efficiency_score',
                'performance_index', 'optimization_potential'
            ]

            efficiency_comparison_data = {}
            for metric, key in zip(efficiency_metrics, efficiency_data_keys):
                efficiency_comparison_data[metric] = {
                    f'Test {test_no}': efficiency_1.get(key, 'N/A'),
                    f'Test {test_no_2}': efficiency_2.get(key, 'N/A')
                }

            efficiency_table = self.create_table_from_dict_for_comparison(efficiency_comparison_data)
            self.scroll_layout_compare.addWidget(efficiency_table)
            self.scroll_layout_compare.addSpacing(20)

        except Exception as e:
            print(f"Error creating efficiency comparison section: {str(e)}")

    def _create_comparison_insights_section(self, comparison_results):
        """Create comparison insights and recommendations section."""
        try:
            self.scroll_layout_compare.addWidget(self.create_section_header('Comparison Insights & Recommendations'))
            self.scroll_layout_compare.addSpacing(10)

            if 'error' in comparison_results:
                error_label = QLabel(f"Comparison analysis error: {comparison_results['error']}")
                error_label.setStyleSheet("color: #ff6b6b; font-style: italic;")
                self.scroll_layout_compare.addWidget(error_label)
                return

            # Get comparison summary
            summary = comparison_results.get('optimization_insights', {})

            # Best practices section
            best_practices = summary.get('best_practices', {})
            if best_practices:
                best_practices_label = QLabel("Best Practices from Top Performer:")
                best_practices_label.setStyleSheet("font-weight: bold; color: #4CAF50; margin-bottom: 5px;")
                self.scroll_layout_compare.addWidget(best_practices_label)

                for category, practices in best_practices.items():
                    if isinstance(practices, dict):
                        category_label = QLabel(f"• {category.replace('_', ' ').title()}:")
                        category_label.setStyleSheet("font-weight: bold; margin-left: 10px;")
                        self.scroll_layout_compare.addWidget(category_label)

                        for key, value in practices.items():
                            practice_label = QLabel(f"  - {key.replace('_', ' ').title()}: {value}")
                            practice_label.setStyleSheet("margin-left: 20px; color: #666;")
                            self.scroll_layout_compare.addWidget(practice_label)

            # Improvement opportunities
            opportunities = summary.get('improvement_opportunities', [])
            if opportunities:
                self.scroll_layout_compare.addSpacing(10)
                opportunities_label = QLabel("Improvement Opportunities:")
                opportunities_label.setStyleSheet("font-weight: bold; color: #FF9800; margin-bottom: 5px;")
                self.scroll_layout_compare.addWidget(opportunities_label)

                for opportunity in opportunities:
                    opp_label = QLabel(f"• {opportunity}")
                    opp_label.setStyleSheet("margin-left: 10px; color: #666;")
                    self.scroll_layout_compare.addWidget(opp_label)

            # Configuration recommendations
            recommendations = summary.get('configuration_recommendations', [])
            if recommendations:
                self.scroll_layout_compare.addSpacing(10)
                recommendations_label = QLabel("Configuration Recommendations:")
                recommendations_label.setStyleSheet("font-weight: bold; color: #2196F3; margin-bottom: 5px;")
                self.scroll_layout_compare.addWidget(recommendations_label)

                for recommendation in recommendations:
                    rec_label = QLabel(f"• {recommendation}")
                    rec_label.setStyleSheet("margin-left: 10px; color: #666;")
                    self.scroll_layout_compare.addWidget(rec_label)

            self.scroll_layout_compare.addSpacing(20)

        except Exception as e:
            print(f"Error creating comparison insights section: {str(e)}")

    def _create_enhanced_multi_test_analysis(self, all_test_data):
        """Create enhanced analysis for multiple test comparison."""
        try:
            from src.analysis import HeaterAnalyzer, ThermalAnalyzer, EfficiencyAnalyzer, ComparisonAnalyzer

            # Initialize analyzers
            heater_analyzer = HeaterAnalyzer()
            thermal_analyzer = ThermalAnalyzer()
            efficiency_analyzer = EfficiencyAnalyzer()
            comparison_analyzer = ComparisonAnalyzer()

            # Perform analysis for all tests
            test_data_list = list(all_test_data.values())
            analysis_results_list = []

            for test_data in test_data_list:
                analysis_results = self._perform_enhanced_analysis(test_data, heater_analyzer, thermal_analyzer,
                                                                   efficiency_analyzer)
                analysis_results_list.append(analysis_results)

            # Perform comprehensive comparison
            comparison_results = comparison_analyzer.compare_multiple_tests(test_data_list, analysis_results_list)

            # Create multi-test comparison sections
            self._create_multi_test_heater_analysis(all_test_data, analysis_results_list)

            # Create multi-test visual comparisons following the same pattern
            self._setup_multi_test_comparison_plots(all_test_data, analysis_results_list)

        except Exception as e:
            print(f"Error in enhanced multi-test analysis: {str(e)}")
            error_label = QLabel(f"Enhanced multi-test analysis unavailable: {str(e)}")
            error_label.setStyleSheet("color: #ff6b6b; font-style: italic;")
            self.scroll_layout_compare.addWidget(error_label)

    def safe_round(value, decimals=2):
        if type(value) == int or type(value) == float:
            return round(value, decimals)
        else:
            return value


    def _create_multi_test_heater_analysis(self, all_test_data, analysis_results_list):
        """Create multi-test heater analysis section."""
        try:
            self.scroll_layout_compare.addWidget(self.create_section_header('Multi-Test Heater Performance'))
            self.scroll_layout_compare.addSpacing(10)

            # Create heater performance table
            heater_data = {}
            test_names = list(all_test_data.keys())

            for i, (test_name, analysis_results) in enumerate(zip(test_names, analysis_results_list)):
                heater_analysis = analysis_results.get('heater_analysis', {})

                # Get heater input power from test data
                test_data = all_test_data.get(test_name, {})
                current_database_source = getattr(self, 'database_source', 'EM2')

                if current_database_source == "EM1":
                    heater_info = test_data.get('component_details', {})
                elif current_database_source == "EM2":
                    heater_info = test_data.get('heater_info', {})
                print(f"Current database source: {current_database_source}, Heater info: {heater_info}")
                heater_power = self._extract_heater_power(heater_info)

                heater_data[test_name] = {
                    'Total On Time (s)': heater_analysis.get('total_heater_on_time', 'N/A'),
                    'Avg Cycle Duration (s)': heater_analysis.get('average_cycle_duration', 'N/A'),
                    'Heater Input Power (W)': heater_power if heater_power else 'N/A',
                    'Energy Consumed (kWh)': round(heater_analysis.get('total_energy_consumed', 'N/A'), 2),
                    'Heater Efficiency (°C/s)': round(heater_analysis.get('heater_efficiency', 'N/A'), 2)
                }

            if heater_data:
                heater_table = self.create_table_from_dict_for_multi_comparison(heater_data)
                self.scroll_layout_compare.addWidget(heater_table)
                self.scroll_layout_compare.addSpacing(20)

        except Exception as e:
            print(f"Error creating multi-test heater analysis: {str(e)}")

    def _setup_comparison_plots_section(self, test_no, test_no_2, test_data, test_data_2, analysis_results_1,
                                        analysis_results_2):
        """Setup comparison plots section following the same pattern as temperature/pressure plots."""
        # Add spacing before comparison plots section
        self.scroll_layout_compare.addSpacing(30)

        # Setup comparison plots if analysis data is available
        if analysis_results_1 or analysis_results_2:
            if self._setup_comparison_plot_section(test_no, test_no_2, analysis_results_1, analysis_results_2):
                comparison_content_frame = self._create_comparison_plot_controls(test_no, test_no_2, test_data,
                                                                                 test_data_2, analysis_results_1,
                                                                                 analysis_results_2)
                self._create_comparison_plot_frame(comparison_content_frame)
                self._initialize_comparison_plot(test_no, test_no_2, test_data, test_data_2, analysis_results_1,
                                                 analysis_results_2)

    def _setup_comparison_plot_section(self, test_no, test_no_2, analysis_results_1, analysis_results_2):
        """Setup the comparison plot section and validate data."""
        self.scroll_layout_compare.addWidget(self.create_section_header('Performance Comparison Plots'))
        self.scroll_layout_compare.addSpacing(10)

        # Check if we have any analysis data to plot
        has_data_1 = bool(analysis_results_1)
        has_data_2 = bool(analysis_results_2)

        if not has_data_1 and not has_data_2:
            QMessageBox.warning(self, "Warning", "No analysis data available for comparison plots.")
            return False

        return True

    def _create_comparison_plot_controls(self, test_no, test_no_2, test_data, test_data_2, analysis_results_1,
                                         analysis_results_2):
        """Create comparison plot controls widgets (combo boxes and labels)."""
        # Available plot types
        plot_types = [
            'Heater Performance Bar Chart',
            'Performance Correlation Scatter Plot',
            'Efficiency Radar Chart',
            'Thermal Characteristics Line Plot'
        ]

        plot_content_frame = QFrame()
        plot_content_frame.setStyleSheet("QFrame{background-color:#171717;}")
        plot_content_layout = QVBoxLayout(plot_content_frame)
        plot_content_layout.setContentsMargins(0, 0, 0, 0)

        # Create top frame for controls
        plot_top_frame = QFrame()
        plot_top_frame.setStyleSheet("QFrame{background-color:#171717;}")
        plot_top_frame_layout = QHBoxLayout(plot_top_frame)
        plot_top_frame_layout.setContentsMargins(10, 10, 10, 10)
        plot_content_layout.addWidget(plot_top_frame)

        # Add spacers and controls
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_3 = QSpacerItem(20, 20, QSizePolicy.Fixed, QSizePolicy.Minimum)

        plot_top_frame_layout.addItem(spacer)

        # Plot type selection label and combo
        plot_type_label = QLabel('Plot Type:')
        plot_type_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(plot_type_label)

        self.comparison_plot_type_combo = QComboBox()
        self.comparison_plot_type_combo.addItems(plot_types)
        self.comparison_plot_type_combo.setCurrentText(plot_types[0])
        self.comparison_plot_type_combo.setStyleSheet(self._get_combobox_style())
        plot_top_frame_layout.addWidget(self.comparison_plot_type_combo)

        plot_top_frame_layout.addItem(spacer_3)

        # Test labels for reference
        test_A_label = QLabel(f'Test {test_no}')
        test_A_label.setStyleSheet("color: #1f77b4; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(test_A_label)

        vs_label = QLabel('vs')
        vs_label.setStyleSheet("color: #FFFFFF; font-size: 12px;")
        plot_top_frame_layout.addWidget(vs_label)

        test_B_label = QLabel(f'Test {test_no_2}')
        test_B_label.setStyleSheet("color: #ff7f0e; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(test_B_label)

        plot_top_frame_layout.addItem(spacer_2)

        return plot_content_frame

    def _create_comparison_plot_frame(self, plot_content_frame):
        """Create the comparison plot frame and widget."""
        plot_content_layout = plot_content_frame.layout()

        # Create a frame for the plot with proper styling
        plot_frame = QFrame()
        plot_frame.setStyleSheet('''
            QFrame {
                background-color: #fcf1ff;
                border-radius: 10px;
            }
        ''')
        plot_content_layout.addWidget(plot_frame)
        plot_frame_layout = QVBoxLayout(plot_frame)
        plot_frame_layout.setContentsMargins(0, 10, 0, 0)

        # Create widget for the plot
        self.comparison_plot_widget = QWidget()
        self.comparison_plot_widget_layout = QVBoxLayout(self.comparison_plot_widget)
        self.comparison_plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
        plot_frame_layout.addWidget(self.comparison_plot_widget)

        # Add the plot content frame to the main layout
        self.scroll_layout_compare.addWidget(plot_content_frame)

    def _initialize_comparison_plot(self, test_no, test_no_2, test_data, test_data_2, analysis_results_1,
                                    analysis_results_2):
        """Initialize the comparison plot with data and connections."""
        # Store data for use in update method
        self.comparison_test_no = test_no
        self.comparison_test_no_2 = test_no_2
        self.comparison_test_data = test_data
        self.comparison_test_data_2 = test_data_2
        self.comparison_analysis_results_1 = analysis_results_1
        self.comparison_analysis_results_2 = analysis_results_2
        self.comparison_plot_canvas = None
        self.comparison_toolbar = None

        # Connect combo box signal to update plot method
        self.comparison_plot_type_combo.currentTextChanged.connect(self.update_comparison_plot)

        # Initial plot creation
        self.update_comparison_plot()

    def update_comparison_plot(self, *args):
        """Update the comparison plot based on the selected plot type."""
        try:
            # Clear previous plot and toolbar if they exist
            if hasattr(self, 'comparison_plot_canvas') and self.comparison_plot_canvas:
                self.comparison_plot_widget_layout.removeWidget(self.comparison_plot_canvas)
                self.comparison_plot_canvas.deleteLater()
            if hasattr(self, 'comparison_toolbar') and self.comparison_toolbar:
                self.comparison_plot_widget_layout.removeWidget(self.comparison_toolbar)
                self.comparison_toolbar.deleteLater()

            # Get selected plot type
            selected_plot_type = self.comparison_plot_type_combo.currentText()

            # Import plot manager
            from src.visualization import ComparisonPlotManager
            plot_manager = ComparisonPlotManager()

            # Prepare data for plotting
            test_names = [f'Test {self.comparison_test_no}', f'Test {self.comparison_test_no_2}']

            # Create the selected plot
            figure = None
            plot_labels = {}

            if selected_plot_type == 'Heater Performance Bar Chart':
                heater_data = {
                    f'Test {self.comparison_test_no}': self.comparison_analysis_results_1.get('heater_analysis', {}),
                    f'Test {self.comparison_test_no_2}': self.comparison_analysis_results_2.get('heater_analysis', {})
                }
                figure = plot_manager.create_heater_performance_bar_plot(heater_data, test_names)
                plot_labels = {'plot_type': 'Heater Performance'}

            elif selected_plot_type == 'Performance Correlation Scatter Plot':
                performance_data = {
                    f'Test {self.comparison_test_no}': self.comparison_test_data.get('performance_data', {}),
                    f'Test {self.comparison_test_no_2}': self.comparison_test_data_2.get('performance_data', {})
                }
                figure = plot_manager.create_performance_scatter_plot(performance_data, test_names)
                plot_labels = {'plot_type': 'Performance Correlation'}

            elif selected_plot_type == 'Efficiency Radar Chart':
                efficiency_data = {
                    f'Test {self.comparison_test_no}': self.comparison_analysis_results_1.get('efficiency_analysis',
                                                                                              {}),
                    f'Test {self.comparison_test_no_2}': self.comparison_analysis_results_2.get('efficiency_analysis',
                                                                                                {})
                }
                figure = plot_manager.create_efficiency_radar_plot(efficiency_data, test_names)
                plot_labels = {'plot_type': 'Efficiency Radar'}

            elif selected_plot_type == 'Thermal Characteristics Line Plot':
                thermal_data = {
                    f'Test {self.comparison_test_no}': self.comparison_analysis_results_1.get('thermal_analysis', {}),
                    f'Test {self.comparison_test_no_2}': self.comparison_analysis_results_2.get('thermal_analysis', {})
                }
                figure = plot_manager.create_thermal_comparison_line_plot(thermal_data, test_names)
                plot_labels = {'plot_type': 'Thermal Characteristics'}

            if figure:
                # Customize plot appearance to match existing plots
                figure.patch.set_alpha(0.0)

                # Create canvas and toolbar
                self.comparison_plot_canvas = PlotCanvas(figure)
                self.comparison_plot_canvas.setStyleSheet("background-color: transparent;")
                self.comparison_toolbar = CustomNavigationToolbar(
                    self.comparison_plot_canvas, self.comparison_plot_widget, plot_labels
                )

                # Add toolbar and canvas to layout
                self.comparison_plot_widget_layout.setContentsMargins(20, 20, 20, 20)
                self.comparison_plot_widget_layout.addWidget(self.comparison_toolbar)
                self.comparison_plot_widget_layout.addWidget(self.comparison_plot_canvas)

                # Ensure the plot is updated
                self.comparison_plot_canvas.draw()
            else:
                # Show error message if plot creation failed
                error_label = QLabel(f"Could not create {selected_plot_type}")
                error_label.setStyleSheet("color: #ff6b6b; font-style: italic; text-align: center; padding: 20px;")
                self.comparison_plot_widget_layout.addWidget(error_label)

        except Exception as e:
            print(f"Error updating comparison plot: {str(e)}")
            error_label = QLabel(f"Plot error: {str(e)}")
            error_label.setStyleSheet("color: #ff6b6b; font-style: italic; text-align: center; padding: 20px;")
            self.comparison_plot_widget_layout.addWidget(error_label)

    def _setup_multi_test_comparison_plots(self, all_test_data, analysis_results_list):
        """Setup multi-test comparison plots following the same pattern as temperature/pressure plots."""
        # Add spacing before multi-test comparison plots section
        self.scroll_layout_compare.addSpacing(30)

        # Setup multi-test comparison plots if data is available
        if all_test_data and analysis_results_list:
            if self._setup_multi_test_comparison_plot_section(all_test_data, analysis_results_list):
                multi_comparison_content_frame = self._create_multi_test_comparison_plot_controls(all_test_data,
                                                                                                  analysis_results_list)
                self._create_multi_test_comparison_plot_frame(multi_comparison_content_frame)
                self._initialize_multi_test_comparison_plot(all_test_data, analysis_results_list)

    def _setup_multi_test_comparison_plot_section(self, all_test_data, analysis_results_list):
        """Setup the multi-test comparison plot section and validate data."""
        self.scroll_layout_compare.addWidget(self.create_section_header('Multi-Test Comparison Plots'))
        self.scroll_layout_compare.addSpacing(10)

        # Check if we have data to plot
        if not all_test_data or len(all_test_data) < 2:
            QMessageBox.warning(self, "Warning", "Need at least 2 tests for multi-test comparison plots.")
            return False

        return True

    def _create_multi_test_comparison_plot_controls(self, all_test_data, analysis_results_list):
        """Create multi-test comparison plot controls widgets with custom option."""
        # Available multi-test plot types (now includes Custom)
        multi_plot_types = [
            'Performance Heatmap',
            'Multi-Test Heater Performance',
            # 'Multi-Test Performance Correlations',
            'Custom'
        ]

        # Performance parameters for custom plots
        self.performance_parameters = [
            'Thrust (mN)',
            'Specific Impulse (s)',
            'Burn Time (s)',
            'Total Impulse (Ns)',
            'Characteristic Velocity (m/s)',
            'Coefficient of Thrust',
            'Mass Flow Rate (mg/s)',
            'Vacuum Chamber Pressure (mbar)',
            'Chamber Pressure (mbar)',
            'Maximum Temperature (K)'
        ]

        # Custom plot types
        self.custom_plot_types = [
            'Scatter Plot',
            'Bar Chart'
        ]

        plot_content_frame = QFrame()
        plot_content_frame.setStyleSheet("QFrame{background-color:#171717;}")
        plot_content_layout = QVBoxLayout(plot_content_frame)
        plot_content_layout.setContentsMargins(0, 0, 0, 0)

        # Create top frame for main controls
        plot_top_frame = QFrame()
        plot_top_frame.setStyleSheet("QFrame{background-color:#171717;}")
        plot_top_frame_layout = QHBoxLayout(plot_top_frame)
        plot_top_frame_layout.setContentsMargins(10, 10, 10, 10)
        plot_content_layout.addWidget(plot_top_frame)

        # Add spacers and controls
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_3 = QSpacerItem(20, 20, QSizePolicy.Fixed, QSizePolicy.Minimum)

        plot_top_frame_layout.addItem(spacer)

        # Plot type selection label and combo
        plot_type_label = QLabel('Multi-Test Plot Type:')
        plot_type_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(plot_type_label)

        self.multi_comparison_plot_type_combo = QComboBox()
        self.multi_comparison_plot_type_combo.addItems(multi_plot_types)
        self.multi_comparison_plot_type_combo.setCurrentText(multi_plot_types[0])
        self.multi_comparison_plot_type_combo.setStyleSheet(self._get_combobox_style())
        plot_top_frame_layout.addWidget(self.multi_comparison_plot_type_combo)

        plot_top_frame_layout.addItem(spacer_3)

        # Test count info
        test_count_label = QLabel(f'Comparing {len(all_test_data)} Tests')
        test_count_label.setStyleSheet("color: #4CAF50; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(test_count_label)

        plot_top_frame_layout.addItem(spacer_2)

        # Create custom plot controls frame (initially hidden)
        self.custom_plot_controls_frame = QFrame()
        self.custom_plot_controls_frame.setStyleSheet(
            "QFrame{background-color:#2d3748; border-radius: 8px; padding: 10px;}")
        self.custom_plot_controls_frame.setVisible(False)  # Initially hidden
        custom_controls_layout = QHBoxLayout(self.custom_plot_controls_frame)
        custom_controls_layout.setContentsMargins(15, 10, 15, 10)
        custom_controls_layout.setSpacing(20)

        # Add expanding spacer at start
        custom_controls_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # Plot Type selection for custom plots
        plot_type_custom_label = QLabel('Plot Type:')
        plot_type_custom_label.setStyleSheet("color: #FFFFFF; font-size: 13px; font-weight: bold;")
        custom_controls_layout.addWidget(plot_type_custom_label)

        self.custom_plot_type_combo = QComboBox()
        self.custom_plot_type_combo.addItems(self.custom_plot_types)
        self.custom_plot_type_combo.setCurrentText('Scatter Plot') # Default custom plot type
        self.custom_plot_type_combo.setStyleSheet(self._get_combobox_style())
        self.custom_plot_type_combo.setMinimumWidth(150)
        custom_controls_layout.addWidget(self.custom_plot_type_combo)

        # Add Spacing between controls
        custom_controls_layout.addItem(QSpacerItem(25, 20, QSizePolicy.Fixed, QSizePolicy.Minimum))

        # X-axis selection
        self.x_axis_label = QLabel('X-Axis:')
        self.x_axis_label.setStyleSheet("color: #FFFFFF; font-size: 13px; font-weight: bold;")
        custom_controls_layout.addWidget(self.x_axis_label)

        self.custom_x_axis_combo = QComboBox()
        self.custom_x_axis_combo.addItems(self.performance_parameters)
        self.custom_x_axis_combo.setCurrentText('Thrust (mN)')  # Default X-axis
        self.custom_x_axis_combo.setStyleSheet(self._get_combobox_style())
        self.custom_x_axis_combo.setMinimumWidth(180)
        custom_controls_layout.addWidget(self.custom_x_axis_combo)

        # Add some spacing between X and Y controls
        custom_controls_layout.addItem(QSpacerItem(25, 20, QSizePolicy.Fixed, QSizePolicy.Minimum))

        # Y-axis selection
        y_axis_label = QLabel('Y-Axis:')
        y_axis_label.setStyleSheet("color: #FFFFFF; font-size: 13px; font-weight: bold;")
        custom_controls_layout.addWidget(y_axis_label)

        self.custom_y_axis_combo = QComboBox()
        self.custom_y_axis_combo.addItems(self.performance_parameters)
        self.custom_y_axis_combo.setCurrentText('Specific Impulse (s)')  # Default Y-axis
        self.custom_y_axis_combo.setStyleSheet(self._get_combobox_style())
        self.custom_y_axis_combo.setMinimumWidth(180)
        custom_controls_layout.addWidget(self.custom_y_axis_combo)

        # Add expanding spacer at end
        custom_controls_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # Add custom controls frame to main layout
        plot_content_layout.addWidget(self.custom_plot_controls_frame)

        # Connect signals
        self.multi_comparison_plot_type_combo.currentTextChanged.connect(self._on_plot_type_changed)
        self.custom_plot_type_combo.currentTextChanged.connect(self._on_custom_control_changed)
        self.custom_x_axis_combo.currentTextChanged.connect(self._on_custom_control_changed)
        self.custom_y_axis_combo.currentTextChanged.connect(self._on_custom_control_changed)

        return plot_content_frame

    def _on_custom_control_changed(self):
        """Handle any custom control change (plot type, X-axis, Y-axis) and update the plot."""
        # Only update if custom plot is selected
        if self.multi_comparison_plot_type_combo.currentText() == 'Custom':
            self.update_multi_test_comparison_plot()

    def _on_plot_type_changed(self, plot_type):
        """Handle plot type change and show/hide custom controls."""
        # Show/hide custom controls based on selected plot type
        is_custom = (plot_type == 'Custom')
        self.custom_plot_controls_frame.setVisible(is_custom)

        # Update the plot
        self.update_multi_test_comparison_plot()

    def _create_multi_test_comparison_plot_frame(self, plot_content_frame):
        """Create the multi-test comparison plot frame and widget."""
        plot_content_layout = plot_content_frame.layout()

        # Create a frame for the plot with proper styling
        plot_frame = QFrame()
        plot_frame.setStyleSheet('''
            QFrame {
                background-color: #fcf1ff;
                border-radius: 10px;
            }
        ''')
        plot_content_layout.addWidget(plot_frame)
        plot_frame_layout = QVBoxLayout(plot_frame)
        plot_frame_layout.setContentsMargins(0, 10, 0, 0)

        # Create widget for the plot
        self.multi_comparison_plot_widget = QWidget()
        self.multi_comparison_plot_widget_layout = QVBoxLayout(self.multi_comparison_plot_widget)
        self.multi_comparison_plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
        plot_frame_layout.addWidget(self.multi_comparison_plot_widget)

        # Add the plot content frame to the main layout
        self.scroll_layout_compare.addWidget(plot_content_frame)

    def _initialize_multi_test_comparison_plot(self, all_test_data, analysis_results_list):
        """Initialize the multi-test comparison plot with data and connections."""
        # Store data for use in update method
        self.multi_comparison_test_data = all_test_data
        self.multi_comparison_analysis_results = analysis_results_list
        self.multi_comparison_plot_canvas = None
        self.multi_comparison_toolbar = None

        # Connect combo box signal to update plot method
        self.multi_comparison_plot_type_combo.currentTextChanged.connect(self.update_multi_test_comparison_plot)

        # Initial plot creation
        self.update_multi_test_comparison_plot()

    def update_multi_test_comparison_plot(self, *args):
        """Update the multi-test comparison plot based on the selected plot type."""
        try:
            # Clear previous plot and toolbar if they exist
            if hasattr(self, 'multi_comparison_plot_canvas') and self.multi_comparison_plot_canvas:
                self.multi_comparison_plot_widget_layout.removeWidget(self.multi_comparison_plot_canvas)
                self.multi_comparison_plot_canvas.deleteLater()
            if hasattr(self, 'multi_comparison_toolbar') and self.multi_comparison_toolbar:
                self.multi_comparison_plot_widget_layout.removeWidget(self.multi_comparison_toolbar)
                self.multi_comparison_toolbar.deleteLater()

            # Get selected plot type
            selected_plot_type = self.multi_comparison_plot_type_combo.currentText()

            # Import plot manager
            from src.visualization import ComparisonPlotManager
            plot_manager = ComparisonPlotManager()

            # Prepare data for plotting
            test_names = list(self.multi_comparison_test_data.keys())

            print('*************************************____________________________________****************************************')
            print( "The test names are: ", test_names)  # Debug
            print(f"The multi_comparison_test_data is: {self.multi_comparison_test_data}")  # Debug

            # Create the selected plot
            figure = None
            plot_labels = {}

            if selected_plot_type == 'Performance Heatmap':
                performance_data = {}
                for test_name, test_data in self.multi_comparison_test_data.items():
                    perf_data = test_data.get('performance_data', {})
                    print(f"Debug - Test {test_name} heatmap data: {perf_data}")  # Debug

                    # Check if we have valid numeric performance data
                    valid_data = {}
                    for key, value in perf_data.items():
                        try:
                            # Skip non-performance keys
                            if key in ['filtered_data']:
                                continue

                            if value is not None and value != 'N/A' and str(value).strip() != '':
                                # Extract numeric value from string with units
                                numeric_val = self._extract_numeric_value(str(value))
                                if numeric_val is not None and not (numeric_val != numeric_val):  # Not NaN
                                    # Map to standard performance metric names
                                    standard_key = self._map_to_standard_key(key)
                                    if standard_key:
                                        valid_data[standard_key] = numeric_val
                        except (ValueError, TypeError):
                            continue

                    if valid_data:
                        performance_data[test_name] = valid_data
                        print(f"Debug - Valid heatmap data for {test_name}: {valid_data}")  # Debug
                    else:
                        print(f"Debug - No valid heatmap data for {test_name}, using defaults")  # Debug
                        # Provide realistic default values for heatmap with more variation
                        test_index = len(performance_data)
                        performance_data[test_name] = {
                            'thrust': 15.0 + test_index * 3.0 + (test_index % 2) * 1.5,
                            'specific_impulse': 120.0 + test_index * 8.0 + (test_index % 3) * 2.0,
                            'total_impulse': 0.45 + test_index * 0.08 + (test_index % 2) * 0.03,
                            'burn_time': 30.0 + test_index * 2.0,
                            'mass_flow_rate': 2.8 + test_index * 0.3 + (test_index % 2) * 0.15,
                            'chamber_pressure': 1250 + test_index * 80 + (test_index % 3) * 30,
                            'thrust_coefficient': 1.45 + test_index * 0.08 + (test_index % 2) * 0.03
                        }

                print(f"Final performance data for heatmap: {performance_data}")  # Debug
                figure = plot_manager.create_performance_heatmap(performance_data, test_names)
                plot_labels = {'plot_type': 'Performance Heatmap'}

            elif selected_plot_type == 'Multi-Test Heater Performance':
                heater_data = {}
                for i, test_name in enumerate(test_names):
                    if i < len(self.multi_comparison_analysis_results):
                        heater_analysis = self.multi_comparison_analysis_results[i].get('heater_analysis', {})
                        print(f"Debug - Test {test_name} heater analysis: {heater_analysis}")  # Debug

                        # Extract heater data with validation
                        valid_heater_data = {}
                        heater_metrics = ['total_heater_on_time', 'average_cycle_duration', 'total_energy_consumed',
                                          'heater_efficiency']

                        for metric in heater_metrics:
                            value = heater_analysis.get(metric, None)
                            if value is not None and value != 'N/A':
                                try:
                                    numeric_val = self._extract_numeric_value(str(value))
                                    if numeric_val is not None and not (numeric_val != numeric_val):  # Not NaN
                                        valid_heater_data[metric] = numeric_val
                                except (ValueError, TypeError):
                                    continue

                        if valid_heater_data:
                            heater_data[test_name] = valid_heater_data
                            print(f"Debug - Valid heater data for {test_name}: {valid_heater_data}")  # Debug
                        else:
                            print(f"Debug - No valid heater data for {test_name}, using defaults")  # Debug
                            # Provide realistic default values
                            test_index = len(heater_data)
                            heater_data[test_name] = {
                                'total_heater_on_time': 25.0 + test_index * 5.0,
                                'average_cycle_duration': 3.5 + test_index * 0.5,
                                'total_energy_consumed': 0.15 + test_index * 0.03,
                                'heater_efficiency': 2.8 + test_index * 0.4
                            }

                print(f"Final heater data: {heater_data}")  # Debug

                # Check if the method exists, if not use a fallback
                if hasattr(plot_manager, 'create_heater_performance_comparison'):
                    figure = plot_manager.create_heater_performance_comparison(heater_data, test_names)
                else:
                    # Fallback: create a simple heater bar plot
                    figure = self._create_simple_heater_bar_plot(heater_data, test_names)
                figure.set_alpha(0.0)
                plot_labels = {'plot_type': 'Multi-Test Heater Performance'}

            # elif selected_plot_type == 'Multi-Test Performance Correlations':
            #     correlation_data = {}
            #     for test_name, test_data in self.multi_comparison_test_data.items():
            #         perf_data = test_data.get('performance_data', {})
            #
            #         # Extract key performance metrics for correlation
            #         metrics = {}
            #         performance_keys = ['thrust', 'specific_impulse', 'total_impulse', 'mass_flow_rate',
            #                             'chamber_pressure']
            #
            #         for key, value in perf_data.items():
            #             try:
            #                 if value is not None and value != 'N/A' and str(value).strip() != '':
            #                     numeric_val = self._extract_numeric_value(str(value))
            #                     if numeric_val is not None and not (numeric_val != numeric_val):
            #                         standard_key = self._map_to_standard_key(key)
            #                         if standard_key and standard_key in performance_keys:
            #                             metrics[standard_key] = numeric_val
            #             except (ValueError, TypeError):
            #                 continue
            #
            #         if len(metrics) >= 2:  # Need at least 2 metrics for correlation
            #             correlation_data[test_name] = metrics
            #
            #     print(f"Correlation data: {correlation_data}")  # Debug
            #
            #     # Check if the method exists, if not use a fallback
            #     if hasattr(plot_manager, 'create_performance_correlation_matrix'):
            #         figure = plot_manager.create_performance_correlation_matrix(correlation_data, test_names)
            #     else:
            #         # Fallback: create a simple correlation plot
            #         figure = self._create_simple_correlation_plot(correlation_data, test_names)
            #     plot_labels = {'plot_type': 'Multi-Test Performance Correlations'}

            elif selected_plot_type == 'Custom':
                # ENHANCED: Handle custom plot creation with plot type selection
                custom_plot_type = self.custom_plot_type_combo.currentText()
                x_axis_param = self.custom_x_axis_combo.currentText()
                y_axis_param = self.custom_y_axis_combo.currentText()

                custom_data = {}
                for test_name, test_data in self.multi_comparison_test_data.items():
                    perf_data = test_data.get('performance_data', {})

                    # Extract X and Y values for the selected parameters
                    x_value = None
                    y_value = None

                    # Map display names to data keys and extract values
                    x_key = self._map_display_name_to_key(x_axis_param)
                    y_key = self._map_display_name_to_key(y_axis_param)

                    for key, value in perf_data.items():
                        try:
                            if value is not None and value != 'N/A' and str(value).strip() != '':
                                numeric_val = self._extract_numeric_value(str(value))
                                if numeric_val is not None and not (numeric_val != numeric_val):
                                    standard_key = self._map_to_standard_key(key)
                                    if standard_key == x_key:
                                        x_value = numeric_val
                                    elif standard_key == y_key:
                                        y_value = numeric_val
                        except (ValueError, TypeError):
                            continue

                    # Store data if both values are available
                    if x_value is not None and y_value is not None:
                        custom_data[test_name] = {'x': x_value, 'y': y_value}
                    else:
                        # Provide default values if data not available
                        import random
                        test_index = len(custom_data)
                        custom_data[test_name] = {
                            'x': 10.0 + test_index * 5.0 + random.uniform(-2, 2),
                            'y': 100.0 + test_index * 20.0 + random.uniform(-10, 10)
                        }

                print(f"Custom plot data: {custom_data}")  # Debug
                print(f"Custom plot type: {custom_plot_type}, X-axis: {x_axis_param}, Y-axis: {y_axis_param}")  # Debug

                # Create plot based on selected plot type
                if custom_plot_type == 'Scatter Plot':
                    # Show the X-axis combobox for scatter plots
                    self.custom_x_axis_combo.show()
                    self.x_axis_label.show()

                    # Check if the method exists, if not create our own
                    if hasattr(plot_manager, 'create_custom_scatter_plot'):
                        figure = plot_manager.create_custom_scatter_plot(custom_data, test_names, x_axis_param,
                                                                         y_axis_param)
                    else:
                        # Create custom scatter plot ourselves
                        figure = self._create_custom_scatter_plot(custom_data, test_names, x_axis_param, y_axis_param)
                elif custom_plot_type == 'Bar Chart':
                    # Hide the X-axis combobox for bar charts
                    self.custom_x_axis_combo.hide()
                    self.x_axis_label.hide()

                    # Create custom bar chart
                    # figure = self._create_custom_bar_chart(custom_data, test_names, x_axis_param, y_axis_param)
                    figure = self._create_custom_bar_chart(custom_data, self.multi_comparison_test_data, x_axis_param, y_axis_param)

                plot_labels = {'plot_type': 'Custom', 'custom_type': custom_plot_type, 'x_axis': x_axis_param, 'y_axis': y_axis_param}

            if figure:
                # FIXED: Create canvas with correct signature (only figure parameter)
                self.multi_comparison_plot_canvas = PlotCanvas(figure)
                self.multi_comparison_plot_canvas.setStyleSheet("background-color: transparent;")
                self.multi_comparison_toolbar = CustomNavigationToolbar(
                    self.multi_comparison_plot_canvas, self.multi_comparison_plot_widget, plot_labels)

                # Add to layout
                self.multi_comparison_plot_widget_layout.setContentsMargins(20, 20, 20, 20)
                self.multi_comparison_plot_widget_layout.addWidget(self.multi_comparison_toolbar)
                self.multi_comparison_plot_widget_layout.addWidget(self.multi_comparison_plot_canvas)

        except Exception as e:
            print(f"Error updating multi-test comparison plot: {str(e)}")
            import traceback
            traceback.print_exc()

    def _create_custom_bar_chart(self, custom_data, test_data, x_label, y_label):
        """Create a custom bar chart for selected parameters"""
        test_names = list(test_data.keys())
        try:
            fig, ax = plt.subplots(figsize=(12, 15))
            fig.patch.set_alpha(0.0)
            ax.patch.set_alpha(0.0)

            if not custom_data:
                ax.text(0.5, 0.5, 'No Custom Data Available', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title(f'{y_label} vs {x_label}', fontsize=16, fontweight='bold')
                return fig

            # Extract data for plotting
            test_labels = []
            y_values = []

            # Color palette for different tests
            colors = plt.cm.Set3(np.linspace(0, 1, len(test_names)))

            # for i, test_name in enumerate(test_names):
            #     if test_name in custom_data:
            #         data = custom_data[test_name]
            #         test_labels.append(test_name)
            #         y_values.append(data['y'])  # Use Y value for bar height

            for i, test_name in enumerate(test_names):
                if test_name in custom_data:
                    data = custom_data[test_name]
                    heater_set_temp = test_data[test_names[i]]['heater_info']['Heater_cut_off_temp (°C)']   # Use heater cut-off temp as label
                    catalyst_used = test_data[test_names[i]]['basic_info']['Catalyst']
                    burn_time = test_data[test_names[i]]['performance_data']['Burn_time (s)'].split('.')[0]  # Use burn time as label
                    mass_flow_rate = test_data[test_names[i]]['performance_data']['Mass_flow_rate (mg/s)'].split()  # Use mass flow rate as label
                    maximum_temperature = test_data[test_names[i]]['performance_data']['Maximum_temperature (K)']  # Use maximum temperature as label
                    complete_label = (test_name + ' - ' + heater_set_temp + '°C' + '\n' + catalyst_used + '\n' + burn_time +
                                      's' + ' | ' + mass_flow_rate[0] + mass_flow_rate[1] + ' | ' + maximum_temperature.split('@')[0])
                    test_labels.append(complete_label)
                    y_values.append(data['y'])  # Use Y value for bar height
            print(f"The test labels are: {test_labels}")  # Debug

            if not y_values:
                ax.text(0.5, 0.5, 'No Valid Data for Custom Bar Chart', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title(f'{y_label} by Tests', fontsize=16, fontweight='bold')
                return fig

            # Create bar chart
            bars = ax.bar(range(len(test_labels)), y_values, color=colors[:len(test_labels)], alpha=0.8,
                          edgecolor='black', linewidth=1.5)

            # Calculate consistent dimensions for highlighters
            bar_width = bars[0].get_width() if bars else 0.8  # Standard bar width

            # Data range calculations for consistent positioning
            data_min = min(y_values)
            data_max = max(y_values)
            data_range = data_max - data_min if data_max != data_min else data_max * 0.1

            # Consistent highlighter dimensions (as percentage of bar width and data range)
            highlighter_width_ratio = 0.6  # 60% of bar width
            highlighter_height_ratio = 0.08  # 8% of data range (or minimum height)

            # Calculate highlighter dimensions
            highlighter_width = bar_width * highlighter_width_ratio
            highlighter_height = max(data_range * highlighter_height_ratio,
                                     abs(data_max) * 0.02)  # Minimum 2% of max value

            # Vertical offset from bar top (consistent across all bars)
            vertical_offset_ratio = 0.03  # 3% of data range above bar
            vertical_offset = max(data_range * vertical_offset_ratio, abs(data_max) * 0.01)  # Minimum 1% of max value

            # Add value labels and highlighters on bars
            for i, (bar, value) in enumerate(zip(bars, y_values)):
                bar_height = bar.get_height()
                bar_center_x = bar.get_x() + bar.get_width() / 2

                # Consistent positioning: always same distance above bar
                label_y_position = bar_height + vertical_offset + (highlighter_height / 2)

                # Add value label
                ax.text(bar_center_x, label_y_position,
                        f'{float(value):.2f}',
                        ha='center', va='center',
                        fontsize=10, color='black', fontweight='bold',
                        zorder=10)  # Ensure text is above highlighter

                # Calculate highlighter position (centered on text)
                highlighter_x = bar_center_x - (highlighter_width / 2)
                highlighter_y = label_y_position - (highlighter_height / 2)

                # Create consistent highlighter box
                highlighter = patches.FancyBboxPatch(
                    (highlighter_x, highlighter_y),
                    highlighter_width,
                    highlighter_height,
                    boxstyle="round,pad=0.02",  # Small consistent padding
                    linewidth=1.2,
                    edgecolor='darkblue',
                    facecolor='lightblue',
                    alpha=0.3,
                    linestyle='-',
                    zorder=5  # Behind text but above bars
                )
                ax.add_patch(highlighter)

            # Formatting
            ax.set_xlabel('Tests', fontsize=12, fontweight='bold')
            ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
            ax.set_title(f'{y_label} Comparison',
                         fontsize=14, fontweight='bold', pad=20)

            # Set x-axis labels
            ax.set_xticks(range(len(test_labels)))
            ax.set_xticklabels(test_labels, rotation=45, ha='right')

            # Grid and styling
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')
            ax.set_axisbelow(True)

            # Adjust y-axis limits to accommodate labels and highlighters
            if y_values:
                # Calculate required space above highest bar
                max_label_space = vertical_offset + highlighter_height + (data_range * 0.02)  # Extra 2% padding

                # Set limits with consistent spacing
                y_bottom = min(y_values) - (data_range * 0.05) if data_range > 0 else min(y_values) - 1
                y_top = max(y_values) + max_label_space + (data_range * 0.02)  # Extra padding above labels

                ax.set_ylim(y_bottom, y_top)

            # Enhanced statistics box with better positioning
            stats_text = f'Tests: {len(test_labels)}\n'
            if y_values:
                stats_text += f'Min: {min(y_values):.2f}\n'
                stats_text += f'Max: {max(y_values):.2f}\n'
                stats_text += f'Avg: {np.mean(y_values):.2f}\n'
                stats_text += f'Range: {data_range:.2f}'

            # # Position stats box to avoid overlap with highlighters
            # ax.text(0, 0, stats_text, transform=ax.transAxes, fontsize=9,
            #         bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow',
            #                   alpha=0.6, edgecolor='gray', linewidth=1),
            #         verticalalignment='top', horizontalalignment='left',
            #         zorder=15)  # Ensure stats box is on top

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating custom bar chart: {str(e)}")
            return self._create_error_plot(f"Custom Bar Chart Error: {y_label}")


    def _create_simple_heater_bar_plot(self, heater_data, test_names):
        """Create a simple heater performance bar plot as fallback."""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            fig, ax = plt.subplots(figsize=(10, 6))
            fig.patch.set_alpha(0.0)
            ax.patch.set_alpha(0.0)

            if not heater_data:
                ax.text(0.5, 0.5, 'No Heater Data Available', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title('Heater Performance Comparison', fontsize=16, fontweight='bold')
                return fig

            # Extract metrics
            metrics = ['total_heater_on_time', 'average_cycle_duration', 'total_energy_consumed', 'heater_efficiency']
            metric_labels = ['On Time (s)', 'Cycle Duration (s)', 'Energy Consumed (J)', 'Efficiency (%)']

            # Prepare data for bar plot
            test_list = list(heater_data.keys())
            x = np.arange(len(test_list))  # the label locations
            width = 0.2  # the width of the bars

            colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']  # Different colors for each metric

            for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
                values = [heater_data[test].get(metric, 0) for test in test_list]

                # Normalize values for better visualization
                if max(values) > 0:
                    normalized_values = [v / max(values) * 100 for v in values]
                else:
                    normalized_values = values

                ax.bar(x + i * width, normalized_values, width, label=label, color=colors[i], alpha=0.8)

            ax.set_xlabel('Tests', fontsize=12, fontweight='bold')
            ax.set_ylabel('Normalized Values (%)', fontsize=12, fontweight='bold')
            ax.set_title('Heater Performance Comparison', fontsize=14, fontweight='bold')
            ax.set_xticks(x + width * (len(metrics) - 1) / 2)
            ax.set_xticklabels(test_list, rotation=45, ha='right')
            ax.legend(title='Metrics', fontsize=10, title_fontsize='12')
            ax.grid(True, alpha=0.3)

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating simple heater bar plot: {str(e)}")
            return self._create_error_plot("Heater Performance Error")

    def _create_simple_correlation_plot(self, correlation_data, test_names):
        """Create a simple correlation plot as fallback."""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            fig, ax = plt.subplots(figsize=(10, 6))
            fig.patch.set_alpha(0.0)
            ax.patch.set_alpha(0.0)

            if not correlation_data:
                ax.text(0.5, 0.5, 'No Correlation Data Available', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title('Performance Correlation Comparison', fontsize=16, fontweight='bold')
                return fig

            # Prepare data for scatter plot
            x_values = []
            y_values = []
            labels = []

            for test_name, metrics in correlation_data.items():
                if 'thrust' in metrics and 'specific_impulse' in metrics:
                    x_values.append(metrics['thrust'])
                    y_values.append(metrics['specific_impulse'])
                    labels.append(test_name)

            if x_values or y_values:
                colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
                scatter = ax.scatter(x_values, y_values, c=colors, s=100, alpha=0.7, edgecolors='black')

                for i, label in enumerate(labels):
                    ax.annotate(label, (x_values[i], y_values[i]),
                                xytext=(5, 5), textcoords='offset points', fontsize=9)

                ax.set_xlabel('Thrust (mN)', fontsize=12, fontweight='bold')
                ax.set_ylabel('Specific Impulse (s)', fontsize=12, fontweight='bold')
                ax.set_title('Performance Correlation Comparison: Thrust vs Specific Impulse', fontsize=14, fontweight='bold')
                ax.grid(True, alpha=0.3)
            else:
                ax.text(0.5, 0.5, 'No Valid Data for Correlation', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title('Performance Correlation Comparison', fontsize=16, fontweight='bold')

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating simple correlation plot: {str(e)}")
            return self._create_error_plot("Correlation Analysis Error", "Could not create performance correlation plot.")

    def _create_custom_scatter_plot(self, custom_data, test_names, x_label, y_label):
        """Create a custom scatter plot with specified X and Y axes."""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            fig, ax = plt.subplots(figsize=(10, 6))
            fig.patch.set_alpha(0.0)
            ax.patch.set_alpha(0.0)

            if not custom_data:
                ax.text(0.5, 0.5, 'No Custom Data Available', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title(f'{x_label} vs {y_label}', fontsize=16, fontweight='bold')
                return fig

            x_values = []
            y_values = []
            labels = []

            # Color palette for scatter points
            colors = plt.cm.Set3(np.linspace(0, 1, len(test_names)))

            for i, test_name in enumerate(test_names):
                if test_name in custom_data:
                    data = custom_data[test_name]
                    x_values.append(data['x'])
                    y_values.append(data['y'])
                    labels.append(test_name)

            if not x_values or not y_values:
                ax.text(0.5, 0.5, 'No Valid Data for Custom Plot', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title(f'{x_label} vs {y_label}', fontsize=16, fontweight='bold')
                return fig

            # Create scatter plot
            scatter = ax.scatter(x_values, y_values, c=colors[:len(labels)],
                                 s=100, alpha=0.7, edgecolors='black', linewidth=1.5)

            # Add data labels for each point
            for i, (x, y, lable) in enumerate(zip(x_values, y_values, labels)):
                ax.annotate(lable, (x, y), xytext=(5, 5), textcoords='offset points',
                            fontsize=9, ha='left', va='bottom',
                            bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3'))

            # Calculate and display correlation if we have enough points
            if len(x_values) >= 3:
                try:
                    correlation = np.corrcoef(x_values, y_values)[0, 1]

                    # Add trend line
                    z = np.polyfit(x_values, y_values, 1)
                    p = np.poly1d(z)
                    ax.plot(x_values, p(x_values), linewidth=2, label='Trend Line (R={correlation:.3f})')

                    # Add correlation text
                    ax.text(0.05, 0.95, f'Correlation: {correlation:.3f}', transform=ax.transAxes,
                            fontsize=12, bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7,
                                                   verticalalignment='top'))
                    ax.legend(loc='upper right', fontsize=10)

                except Exception as e:
                    print(f"Error calculating correlation: {str(e)}")
                    ax.text(0.05, 0.95, 'Correlation: N/A', transform=ax.transAxes,
                            fontsize=12, bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7,
                                                   verticalalignment='top'))
            # Formatting
            ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
            ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
            ax.set_title(f'{x_label} vs {y_label}', fontsize=14, fontweight='bold', pad=20)

            # Grid and styling
            ax.grid(True, alpha=0.3, linestyle='--')
            ax.set_axisbelow(True)

            # Add some padding to the axis limits
            if len(x_values) > 1:
                x_range = max(x_values) - min(x_values)
                y_range = max(y_values) - min(y_values)

                if x_range > 0:
                    ax.set_xlim(min(x_values) - 0.1 * x_range, max(x_values) + 0.1 * x_range)
                if y_range > 0:
                    ax.set_ylim(min(y_values) - 0.1 * y_range, max(y_values) + 0.1 * y_range)

            # Add statistics box
            stats_text = f'Tests: {len(labels)}\n'
            if x_values:
                stats_text += f'{x_label.split("(")[0].strip()}: {min(x_values):.2f} - {max(x_values):.2f}\n'
            if y_values:
                stats_text += f'{y_label.split("(")[0].strip()}: {min(y_values):.2f} - {max(y_values):.2f}'

            ax.text(0.95, 0.05, stats_text, transform=ax.transAxes, fontsize=9,
                    bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8),
                    verticalalignment='bottom', horizontalalignment='right')

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating custom scatter plot: {str(e)}")
            return self._create_error_plot(f"Custom Plot Error: {x_label} vs {y_label}")

    def _create_error_plot(self, error_message):
        """Create a simple error plot when data is insufficient or invalid."""
        try:
            import matplotlib.pyplot as plt

            fig, ax = plt.subplots(figsize=(10, 6))
            fig.patch.set_alpha(0.0)
            ax.patch.set_alpha(0.0)

            ax.text(0.5, 0.5, error_message, ha='center', va='center',
                    transform=ax.transAxes, fontsize=14, style='italic',
                    bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))

            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.set_xticks([])
            ax.set_yticks([])
            ax.set_title('Plot Generation Error', fontsize=16, fontweight='bold')

            return fig
        except Exception as e:
            print(f"Error creating error plot: {str(e)}")
            # Return a minimal figure as last resort
            fig, ax = plt.subplots(figsize=(6, 4))
            ax.text(0.5, 0.5, 'Plot Error', ha='center', va='center')
            return fig

    def _map_display_name_to_key(self, display_name):
        """Map display parameter names to internal keys."""
        display_to_key_mapping = {
            'Thrust (mN)': 'thrust',
            'Specific Impulse (s)': 'specific_impulse',
            'Burn Time (s)': 'burn_time',
            'Total Impulse (Ns)': 'total_impulse',
            'Characteristic Velocity (m/s)': 'characteristic_velocity',
            'Coefficient of Thrust': 'thrust_coefficient',
            'Mass Flow Rate (mg/s)': 'mass_flow_rate',
            'Vacuum Chamber Pressure (mbar)': 'vacuum_pressure',
            'Chamber Pressure (mbar)': 'chamber_pressure',
            'Maximum Temperature (K)': 'max_temperature'
        }
        return display_to_key_mapping.get(display_name, 'thrust')  # Default to thrust if not found

    def _create_pressure_plot_frame(self, plot_content_frame):
        """Create the pressure plot frame and widget."""
        plot_content_layout = plot_content_frame.layout()

        # Create a frame for the plot with proper styling
        plot_frame = QFrame()
        plot_frame.setStyleSheet('''
            QFrame {
                background-color: #fcf1ff;
                border-radius: 10px;
            }
        ''')
        plot_content_layout.addWidget(plot_frame)

        # Set up the layout for the plot frame
        plot_frame_layout = QVBoxLayout(plot_frame)
        plot_frame_layout.setContentsMargins(0, 10, 0, 0)

        # Create the widget that will contain the plot
        self.pressure_widget = QWidget()
        self.pressure_widget_layout = QVBoxLayout(self.pressure_widget)
        self.pressure_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
        plot_frame_layout.addWidget(self.pressure_widget)

    def _create_plot_controls(self, test_no, test_no_2, temperature_data, temperature_data_2):
        """Create plot control widgets (combo boxes and labels)."""
        valid_cols_A = [col for col in temperature_data.columns if col != 'time']
        valid_cols_B = [col for col in temperature_data_2.columns if col != 'time']

        plot_content_frame = QFrame()
        plot_content_frame.setStyleSheet("QFrame{background-color:#171717;}")
        plot_content_layout = QVBoxLayout(plot_content_frame)
        self.scroll_layout_compare.addWidget(plot_content_frame)

        plot_top_frame = QFrame()
        plot_top_frame_layout = QHBoxLayout(plot_top_frame)
        plot_content_layout.addWidget(plot_top_frame)

        # Create spacers
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        spacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        # Test A controls
        plot_top_frame_layout.addItem(spacer)
        test_A_label = QLabel(f'Test {test_no}:')
        test_A_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(test_A_label)

        self.temperature_A_plot_combo = QComboBox()
        self.temperature_A_plot_combo.addItems(valid_cols_A)
        self.temperature_A_plot_combo.setCurrentText(
            'Tank Lid (°C)' if 'Tank Lid (°C)' in valid_cols_A else valid_cols_A[0])
        self.temperature_A_plot_combo.setStyleSheet(self._get_combobox_style())
        plot_top_frame_layout.addWidget(self.temperature_A_plot_combo)

        plot_top_frame_layout.addItem(spacer_3)

        # Test B controls
        test_B_label = QLabel(f'Test {test_no_2}:')
        test_B_label.setStyleSheet("color: #FFFFFF; font-size: 14px; font-weight: bold;")
        plot_top_frame_layout.addWidget(test_B_label)

        self.temperature_B_plot_combo = QComboBox()
        self.temperature_B_plot_combo.addItems(valid_cols_B)
        self.temperature_B_plot_combo.setCurrentText(
            'Tank Lid (°C)' if 'Tank Lid (°C)' in valid_cols_B else valid_cols_B[0])
        self.temperature_B_plot_combo.setStyleSheet(self._get_combobox_style())
        plot_top_frame_layout.addWidget(self.temperature_B_plot_combo)

        plot_top_frame_layout.addItem(spacer_2)

        return plot_content_frame

    def _get_combobox_style(self):
        """Return the common combo box style to eliminate duplication."""
        return '''
            QComboBox {
                border: 1px solid #446699;
                border-radius: 15px;
                padding: 4px 10px;
                background-color: #1C1C1C;
                color: #EEEEEE;
                min-width: 6em;
                font-size: 16px;
            }
            QComboBox::drop-down {
                border: none;
                background: transparent;
                width: 20px;
                margin-right: 8px;
            }
            QComboBox::down-arrow {
                image: url("assets/down-arrow.png");
                width: 24px;
                height: 24px;
            }
            QComboBox QAbstractItemView {
                border: none;
                margin: 5px;
                border-radius: 10px;
                padding: 5px 0px;
                background-color: #2a2a2a;
                color: white;
                selection-background-color: #3a3a3a;
                outline: 0px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border: none;
                min-height: 24px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #3d3d3d;
                border-radius: 5px;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #3d3d3d;
            }
            QComboBox:focus {
                border: none;
                outline: none;
            }
            QComboBox:hover {
                background-color: #252525;
            }
        '''

    def _create_plot_frame(self, plot_content_frame):
        """Create the plot frame and widget."""
        plot_content_layout = plot_content_frame.layout()

        plot_frame = QFrame()
        plot_frame.setStyleSheet('''
            QFrame {
                background-color: #fcf1ff;
                border-radius: 10px;
            }
        ''')
        plot_content_layout.addWidget(plot_frame)
        # plot_frame.setMinimumHeight(800)
        plot_frame_layout = QVBoxLayout(plot_frame)
        plot_frame_layout.setContentsMargins(0, 10, 0, 0)

        self.plot_widget = QWidget()
        self.plot_widget_layout = QVBoxLayout(self.plot_widget)
        self.plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
        plot_frame_layout.addWidget(self.plot_widget)

    def _initialize_temperature_plot(self, temperature_data, temperature_data_2, test_no, test_no_2):
        """Initialize the temperature plot with data and connections."""
        # Store temperature data for use in update method
        self.temperature_data = temperature_data
        self.temperature_data_2 = temperature_data_2
        self.test_no = test_no
        self.test_no_2 = test_no_2
        self.plot_canvas = None
        self.toolbar = None

        # Connect combo box signals to update plot method
        self.temperature_A_plot_combo.currentTextChanged.connect(self.update_temperature_plot)
        self.temperature_B_plot_combo.currentTextChanged.connect(self.update_temperature_plot)

        # Initial plot creation
        self.update_temperature_plot()

    def update_temperature_plot(self, *args):
        """Update the temperature plot based on the selected columns from both combo boxes."""
        # Clear previous plot and toolbar if they exist
        if self.plot_canvas:
            self.plot_widget_layout.removeWidget(self.plot_canvas)
            self.plot_canvas.deleteLater()
        if self.toolbar:
            self.plot_widget_layout.removeWidget(self.toolbar)
            self.toolbar.deleteLater()

        # Get selected columns from both combo boxes
        selected_column_A = self.temperature_A_plot_combo.currentText()
        selected_column_B = self.temperature_B_plot_combo.currentText()

        # Validate selected columns
        if selected_column_A not in self.temperature_data.columns:
            QMessageBox.warning(self, "Warning",
                                f"Selected column '{selected_column_A}' not found in Test {self.test_no} dataset.")
            return
        if selected_column_B not in self.temperature_data_2.columns:
            QMessageBox.warning(self, "Warning",
                                f"Selected column '{selected_column_B}' not found in Test {self.test_no_2} dataset.")
            return

        # Create new plot
        figure, axes = plt.subplots(figsize=(9, 3))
        axes.plot(
            self.temperature_data['time'],
            self.temperature_data[selected_column_A],
            label=f'Test {self.test_no}: {selected_column_A}',
            linewidth=2,
            alpha=0.7  # Semi-transparent lines
        )
        axes.plot(
            self.temperature_data_2['time'],
            self.temperature_data_2[selected_column_B],
            label=f'Test {self.test_no_2}: {selected_column_B}',
            linewidth=2,
            alpha=0.7  # Semi-transparent lines
        )

        # Customize plot appearance
        figure.patch.set_alpha(0.0)
        axes.patch.set_alpha(0.0)
        axes.legend()

        self.plot_style(axes, 'Time (s)', 'Temperature (°C)', 'Temperature Plot')

        # Create canvas and toolbar
        self.plot_canvas = PlotCanvas(figure)
        self.plot_canvas.setStyleSheet("background-color: transparent;")
        self.toolbar = CustomNavigationToolbar(
            self.plot_canvas, self.plot_widget,
            {selected_column_A: selected_column_A, selected_column_B: selected_column_B}
        )

        # Add toolbar and canvas to layout
        self.plot_widget_layout.setContentsMargins(20, 20, 20, 20)
        self.plot_widget_layout.addWidget(self.toolbar)
        self.plot_widget_layout.addWidget(self.plot_canvas)

        # Ensure the plot is updated
        self.plot_canvas.draw()

    def load_test_data(self, test_no):
        if not test_no:
            return

        self.show_current_test_number(test_no)

        print(f"Loading data for test: {test_no}")  # Debug print

        # Clear existing content
        while self.scroll_layout.count():
            child = self.scroll_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # Getting test data - use the known database source first, but verify it's correct
        test_data = None

        if self.multi_db_handler:
            test_data = self.multi_db_handler.get_test_data_from_database(test_no, self.database_source)

        print(f"Final database source for test {test_no}: {self.database_source}")
        if not test_data:
            print(f"No data found for test: {test_no} in any database")  # Debug print
            return

        # Getting section data list
        sections = self.get_section_wise_data(test_data)

        for section_title, section_data in sections:
            if section_data:
                self.scroll_layout.addWidget(self.create_section_header(section_title))
                print(f"Section data: {section_data}")
                if section_title == "Component Details":
                    if type(section_data) == list:
                        print(f"Component details: {section_data}")

                        for i in section_data:
                            table_order = section_data.index(i)
                            if table_order == 0:
                                self.scroll_layout.addWidget(
                                    self.create_section_header("Pressure Sensor 1-(Vacuum Chamber)"))
                            elif table_order == 1:
                                self.scroll_layout.addWidget(
                                    self.create_section_header("Pressure Sensor 2-(Propellant Tank)"))
                            elif table_order == 2:
                                self.scroll_layout.addWidget(self.create_section_header("Pressure Sensor 3-(Thruster)"))
                            elif table_order == 3:
                                self.scroll_layout.addWidget(self.create_section_header("Heater Specifications"))

                            table = self.create_table_from_dict(i)
                            self.scroll_layout.addWidget(table)
                            self.scroll_layout.addSpacing(20)
                        continue
                table = self.create_table_from_dict(section_data)
                self.scroll_layout.addWidget(table)
                self.scroll_layout.addSpacing(20)

        # Add heater cycles section
        heater_cycles = test_data.get('heater_cycles', [])
        if heater_cycles:
            self.scroll_layout.addWidget(self.create_section_header("Heater Cycles"))
            cycles_table = self.create_heater_cycles_table(heater_cycles)
            self.scroll_layout.addWidget(cycles_table)
            self.scroll_layout.addSpacing(20)

        # Add note section if exists
        note = test_data.get('note')
        if note:
            self.scroll_layout.addWidget(self.create_section_header("Notes"))
            note_label = QLabel(note)
            note_label.setWordWrap(True)
            note_label.setStyleSheet("color: white; padding: 10px;")
            self.scroll_layout.addWidget(note_label)

        self.scroll_layout.addStretch()

        # Adding the PDF Report at the end
        current_database_source = getattr(self, 'database_source', 'EM2')
        report = self.db_handler.get_test_report(test_data['test_id'], current_database_source)
        pdf_button = QPushButton('Show Report')
        pdf_button.clicked.connect(lambda: open_pdf(self, report))
        self.scroll_layout.addWidget(pdf_button)

        pdf_icon = QIcon()
        pdf_icon_path = get_resource_path("assets/pdf.png")
        pdf_icon.addFile(pdf_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        pdf_button.setIcon(pdf_icon)
        pdf_button.setIconSize(QSize(70, 70))

        compare_test_btn = QPushButton('Compare with another test')
        compare_test_btn.setStyleSheet('''
            QPushButton{
	background-color:rgb(30, 62, 98);
	border-radius:10px;
	padding:5px;
	font-size: 17px;
	font-family: Arial;
}

QPushButton:hover{
	background-color:#b089a1;
}

QPushButton:pressed{
	background-color:b03781;
}
        ''')
        # compare_test.clicked.connect(lambda: self.compare_tests(test_no))
        compare_test_btn.clicked.connect(self.show_comparison_dialog)
        compare_test_btn_frame = QFrame()
        compare_test_btn_frame_layout = QHBoxLayout(compare_test_btn_frame)
        compare_test_btn_frame_layout.addWidget(compare_test_btn)
        compare_test_btn_frame_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        compare_test_btn_frame.setStyleSheet("background-color: transparent;")
        compare_test_btn.setFixedHeight(35)
        compare_test_btn.setFixedWidth(300)
        self.scroll_layout.addWidget(compare_test_btn_frame)

    def show_differences(self, differences):
        # Creating a dialog to show differences
        diff_dialog = QDialog(self)
        diff_dialog.setWindowTitle("Differences Found")
        diff_dialog.setStyleSheet("background-color: transparent/; color: white;")

        diff_layout = QVBoxLayout(diff_dialog)

        for key, diff in differences.items():
            diff_label = QLabel(f"{key}: {diff}")
            diff_label.setWordWrap(True)
            diff_label.setStyleSheet("color: white; padding: 10px;")
            diff_layout.addWidget(diff_label)

        diff_dialog.exec()

    def _extract_numeric_value(self, value_str):
        """Extract numeric value from string with units."""
        try:
            import re
            # Remove units and extract the numeric part
            # Handle formats like "4.200 mN", "7815.00 s", "63.85mbar", "531.47K @ Thruster Chamber"

            # First, handle special cases with @ symbol
            if '@' in value_str:
                value_str = value_str.split('@')[0].strip()

            # Extract the first number found in the string
            match = re.search(r'[-+]?(?:\d*\.\d+|\d+)', value_str)
            if match:
                return float(match.group())

            return None
        except Exception:
            return None

    def _map_to_standard_key(self, original_key):
        """Map original performance keys to standard plot keys."""
        key_mapping = {
            'Thrust (mN)': 'thrust',
            'Specific_impulse (s)': 'specific_impulse',
            'Total_impulse (Ns)': 'total_impulse',
            'Burn_time (s)': 'burn_time',
            'Mass_flow_rate (mg/s)': 'mass_flow_rate',
            'Chamber_pressure (mbar)': 'chamber_pressure',
            'Coefficient_of_thrust': 'thrust_coefficient',
            'Characteristic_velocity (m/s)': 'characteristic_velocity',
            'Vacuum_chamber_pressure (mbar)': 'vacuum_pressure',
            'Maximum_temperature (K)': 'max_temperature'
        }

        return key_mapping.get(original_key, None)

    @staticmethod
    def _extract_heater_power(heater_info):
        """Extract heater power from heater info dictionary."""
        try:
            # Try different possible keys for heater power
            power_keys = ['Heater_input_Wattage', 'Heater_input_power (W)']

            for key in power_keys:
                if key in heater_info:
                    print(f"Extracting heater power from key: {key}")  # Debug print
                    power_str = str(heater_info[key]).strip()
                    print(f"Heater power string: {power_str}")  # Debug print
                    if power_str and power_str.lower() != 'n/a':
                        # Extract numeric value
                        import re
                        numbers = re.findall(r'\d+\.?\d*', power_str.replace(',', '.'))
                        if numbers:
                            return float(numbers[0])

            return None

        except Exception:
            return None