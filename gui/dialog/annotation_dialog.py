"""
Annotation Edit Dialog
Provides interface for editing annotation properties
"""

from PySide6.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QLineEdit, QPushButton, QColorDialog, QSpinBox,
                               QComboBox, QCheckBox, QGroupBox, QFormLayout)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor, QPalette
import matplotlib.colors as mcolors


class AnnotationEditDialog(QDialog):
    """Dialog for editing annotation properties"""
    
    def __init__(self, draggable_annotation, parent=None):
        super().__init__(parent)
        self.draggable_annotation = draggable_annotation
        self.annotation = draggable_annotation.annotation
        
        self.setWindowTitle("Edit Annotation")
        self.setModal(True)
        self.resize(400, 300)
        
        self.setup_ui()
        self.load_current_values()
        
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Text group
        text_group = QGroupBox("Text")
        text_layout = QFormLayout(text_group)
        
        self.text_edit = QLineEdit()
        text_layout.addRow("Text:", self.text_edit)
        
        layout.addWidget(text_group)
        
        # Appearance group
        appearance_group = QGroupBox("Appearance")
        appearance_layout = QFormLayout(appearance_group)
        
        # Font size
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(6, 72)
        self.font_size_spin.setValue(10)
        appearance_layout.addRow("Font Size:", self.font_size_spin)
        
        # Text color
        color_layout = QHBoxLayout()
        self.color_button = QPushButton()
        self.color_button.setFixedSize(50, 30)
        self.color_button.clicked.connect(self.choose_text_color)
        self.current_text_color = QColor("black")
        self.update_color_button()
        
        color_layout.addWidget(self.color_button)
        color_layout.addStretch()
        appearance_layout.addRow("Text Color:", color_layout)
        
        # Background color
        bg_color_layout = QHBoxLayout()
        self.bg_color_button = QPushButton()
        self.bg_color_button.setFixedSize(50, 30)
        self.bg_color_button.clicked.connect(self.choose_bg_color)
        self.current_bg_color = QColor("yellow")
        self.update_bg_color_button()
        
        self.bg_enabled_checkbox = QCheckBox("Show Background")
        self.bg_enabled_checkbox.setChecked(True)
        
        bg_color_layout.addWidget(self.bg_color_button)
        bg_color_layout.addWidget(self.bg_enabled_checkbox)
        bg_color_layout.addStretch()
        appearance_layout.addRow("Background:", bg_color_layout)
        
        # Alignment
        self.h_align_combo = QComboBox()
        self.h_align_combo.addItems(["left", "center", "right"])
        self.h_align_combo.setCurrentText("center")
        appearance_layout.addRow("Horizontal Align:", self.h_align_combo)
        
        self.v_align_combo = QComboBox()
        self.v_align_combo.addItems(["bottom", "center", "top"])
        self.v_align_combo.setCurrentText("center")
        appearance_layout.addRow("Vertical Align:", self.v_align_combo)
        
        layout.addWidget(appearance_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.delete_button = QPushButton("Delete")
        self.delete_button.setStyleSheet("QPushButton { background-color: #ff4444; color: white; }")
        self.delete_button.clicked.connect(self.delete_annotation)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setDefault(True)
        
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
        
        # Apply dark theme styling
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QSpinBox, QComboBox {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 5px;
                color: white;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QCheckBox {
                color: white;
            }
        """)
    
    def load_current_values(self):
        """Load current annotation values into the dialog"""
        try:
            # Text
            self.text_edit.setText(self.annotation.get_text())
            
            # Font size
            if hasattr(self.annotation, 'get_fontsize'):
                self.font_size_spin.setValue(int(self.annotation.get_fontsize()))
            
            # Text color
            if hasattr(self.annotation, 'get_color'):
                color = self.annotation.get_color()
                if isinstance(color, str):
                    self.current_text_color = QColor(color)
                else:
                    # Convert matplotlib color to QColor
                    rgba = mcolors.to_rgba(color)
                    self.current_text_color = QColor.fromRgbF(*rgba)
                self.update_color_button()
            
            # Alignment
            if hasattr(self.annotation, 'get_ha'):
                self.h_align_combo.setCurrentText(self.annotation.get_ha())
            if hasattr(self.annotation, 'get_va'):
                self.v_align_combo.setCurrentText(self.annotation.get_va())
                
        except Exception as e:
            print(f"[ERROR] Failed to load annotation values: {e}")
    
    def choose_text_color(self):
        """Open color dialog for text color"""
        color = QColorDialog.getColor(self.current_text_color, self)
        if color.isValid():
            self.current_text_color = color
            self.update_color_button()
    
    def choose_bg_color(self):
        """Open color dialog for background color"""
        color = QColorDialog.getColor(self.current_bg_color, self)
        if color.isValid():
            self.current_bg_color = color
            self.update_bg_color_button()
    
    def update_color_button(self):
        """Update the color button appearance"""
        self.color_button.setStyleSheet(
            f"QPushButton {{ background-color: {self.current_text_color.name()}; }}"
        )
    
    def update_bg_color_button(self):
        """Update the background color button appearance"""
        self.bg_color_button.setStyleSheet(
            f"QPushButton {{ background-color: {self.current_bg_color.name()}; }}"
        )
    
    def delete_annotation(self):
        """Delete the annotation"""
        self.done(2)  # Custom return code for deletion
    
    def accept(self):
        """Apply changes and close dialog"""
        try:
            # Update text
            self.draggable_annotation.update_text(self.text_edit.text())
            
            # Update style properties
            style_updates = {
                'fontsize': self.font_size_spin.value(),
                'color': self.current_text_color.name(),
                'ha': self.h_align_combo.currentText(),
                'va': self.v_align_combo.currentText()
            }
            
            # Update background
            if self.bg_enabled_checkbox.isChecked():
                bbox_style = dict(
                    boxstyle='round,pad=0.3',
                    facecolor=self.current_bg_color.name(),
                    alpha=0.7
                )
                self.annotation.set_bbox(bbox_style)
            else:
                self.annotation.set_bbox(None)
            
            # Apply style updates
            self.draggable_annotation.set_style(**style_updates)
            
            super().accept()
            
        except Exception as e:
            print(f"[ERROR] Failed to apply annotation changes: {e}")
            super().accept()  # Close anyway to avoid getting stuck
