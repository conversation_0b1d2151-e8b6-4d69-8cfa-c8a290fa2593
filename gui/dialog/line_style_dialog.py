"""
Line Style Dialog
Provides interface for editing line and arrow properties
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QColorDialog, QSpinBox, QDoubleSpinBox,
                               QComboBox, QCheckBox, QGroupBox, QFormLayout,
                               QSlider, QButtonGroup, QRadioButton)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor, QPalette
import matplotlib.colors as mcolors


class LineStyleDialog(QDialog):
    """Dialog for editing line and arrow properties"""
    
    def __init__(self, draggable_line, parent=None):
        super().__init__(parent)
        self.draggable_line = draggable_line
        self.line = draggable_line
        
        self.setWindowTitle("Edit Line/Arrow Style")
        self.setModal(True)
        self.resize(450, 500)
        
        self.setup_ui()
        self.load_current_values()
        
    def setup_ui(self):
        """Setup the dialog UI"""
        layout = QVBoxLayout(self)
        
        # Line Type Group
        type_group = QGroupBox("Line Type")
        type_layout = QVBoxLayout(type_group)
        
        self.type_button_group = QButtonGroup()
        self.line_radio = QRadioButton("Line")
        self.arrow_radio = QRadioButton("Arrow")
        
        self.type_button_group.addButton(self.line_radio, 0)
        self.type_button_group.addButton(self.arrow_radio, 1)
        
        type_layout.addWidget(self.line_radio)
        type_layout.addWidget(self.arrow_radio)
        
        layout.addWidget(type_group)
        
        # Basic Properties Group
        basic_group = QGroupBox("Basic Properties")
        basic_layout = QFormLayout(basic_group)
        
        # Color
        color_layout = QHBoxLayout()
        self.color_button = QPushButton()
        self.color_button.setFixedSize(50, 30)
        self.color_button.clicked.connect(self.choose_color)
        self.current_color = QColor("red")
        self.update_color_button()
        
        color_layout.addWidget(self.color_button)
        color_layout.addStretch()
        basic_layout.addRow("Color:", color_layout)
        
        # Line Width
        self.width_spin = QDoubleSpinBox()
        self.width_spin.setRange(0.5, 10.0)
        self.width_spin.setSingleStep(0.5)
        self.width_spin.setValue(2.0)
        basic_layout.addRow("Line Width:", self.width_spin)
        
        # Line Style
        self.style_combo = QComboBox()
        self.style_combo.addItems([
            "Solid (-)",
            "Dashed (--)", 
            "Dash-dot (-·)",
            "Dotted (···)"
        ])
        basic_layout.addRow("Line Style:", self.style_combo)
        
        # Transparency
        transparency_layout = QHBoxLayout()
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setRange(10, 100)
        self.alpha_slider.setValue(100)
        self.alpha_label = QLabel("100%")
        self.alpha_slider.valueChanged.connect(self.update_alpha_label)
        
        transparency_layout.addWidget(self.alpha_slider)
        transparency_layout.addWidget(self.alpha_label)
        basic_layout.addRow("Opacity:", transparency_layout)
        
        layout.addWidget(basic_group)
        
        # Arrow Properties Group
        self.arrow_group = QGroupBox("Arrow Properties")
        arrow_layout = QFormLayout(self.arrow_group)
        
        # Arrow Style
        self.arrow_style_combo = QComboBox()
        self.arrow_style_combo.addItems([
            "Arrow (->)",
            "Double Arrow (<->)",
            "Reverse Arrow (<-)",
            "No Arrow (line only)"
        ])
        arrow_layout.addRow("Arrow Style:", self.arrow_style_combo)
        
        # Arrow Size
        self.arrow_size_spin = QSpinBox()
        self.arrow_size_spin.setRange(5, 50)
        self.arrow_size_spin.setValue(15)
        arrow_layout.addRow("Arrow Size:", self.arrow_size_spin)
        
        # Connection Style
        self.connection_combo = QComboBox()
        self.connection_combo.addItems([
            "Straight",
            "Curved (arc3,rad=0.1)",
            "Curved (arc3,rad=0.3)",
            "Curved (arc3,rad=-0.1)",
            "Curved (arc3,rad=-0.3)"
        ])
        arrow_layout.addRow("Connection:", self.connection_combo)
        
        layout.addWidget(self.arrow_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.delete_button = QPushButton("Delete")
        self.delete_button.setStyleSheet("QPushButton { background-color: #ff4444; color: white; }")
        self.delete_button.clicked.connect(self.delete_line)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setDefault(True)
        
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)
        
        layout.addLayout(button_layout)
        
        # Connect signals
        self.type_button_group.buttonClicked.connect(self.on_type_changed)
        
        # Apply dark theme styling
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QSpinBox, QDoubleSpinBox, QComboBox {
                background-color: #3c3c3c;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 5px;
                color: white;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QRadioButton, QCheckBox {
                color: white;
            }
            QSlider::groove:horizontal {
                border: 1px solid #555;
                height: 8px;
                background: #3c3c3c;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #0078d4;
                border: 1px solid #555;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }
        """)
    
    def on_type_changed(self):
        """Handle line type change"""
        is_arrow = self.arrow_radio.isChecked()
        self.arrow_group.setEnabled(is_arrow)
    
    def update_alpha_label(self, value):
        """Update the alpha percentage label"""
        self.alpha_label.setText(f"{value}%")
    
    def load_current_values(self):
        """Load current line values into the dialog"""
        try:
            props = self.line.get_properties()
            
            # Line type
            if self.line.line_type == 'arrow':
                self.arrow_radio.setChecked(True)
            else:
                self.line_radio.setChecked(True)
            self.on_type_changed()
            
            # Color
            color = props.get('color', 'red')
            if isinstance(color, str):
                self.current_color = QColor(color)
            else:
                rgba = mcolors.to_rgba(color)
                self.current_color = QColor.fromRgbF(*rgba)
            self.update_color_button()
            
            # Line width
            self.width_spin.setValue(props.get('linewidth', 2.0))
            
            # Line style
            linestyle = props.get('linestyle', '-')
            style_map = {'-': 0, '--': 1, '-.': 2, ':': 3}
            self.style_combo.setCurrentIndex(style_map.get(linestyle, 0))
            
            # Alpha
            alpha = props.get('alpha', 1.0)
            self.alpha_slider.setValue(int(alpha * 100))
            
            # Arrow properties
            arrow_style = props.get('arrow_style', '->')
            arrow_map = {'->': 0, '<->': 1, '<-': 2, 'none': 3}
            self.arrow_style_combo.setCurrentIndex(arrow_map.get(arrow_style, 0))
            
            self.arrow_size_spin.setValue(props.get('arrow_size', 15))
            
            # Connection style
            connection = props.get('connection_style', 'arc3,rad=0')
            if 'rad=0.1' in connection:
                self.connection_combo.setCurrentIndex(1)
            elif 'rad=0.3' in connection:
                self.connection_combo.setCurrentIndex(2)
            elif 'rad=-0.1' in connection:
                self.connection_combo.setCurrentIndex(3)
            elif 'rad=-0.3' in connection:
                self.connection_combo.setCurrentIndex(4)
            else:
                self.connection_combo.setCurrentIndex(0)
                
        except Exception as e:
            print(f"[ERROR] Failed to load line values: {e}")
    
    def choose_color(self):
        """Open color dialog"""
        color = QColorDialog.getColor(self.current_color, self)
        if color.isValid():
            self.current_color = color
            self.update_color_button()
    
    def update_color_button(self):
        """Update the color button appearance"""
        self.color_button.setStyleSheet(
            f"QPushButton {{ background-color: {self.current_color.name()}; }}"
        )
    
    def delete_line(self):
        """Delete the line"""
        self.done(2)  # Custom return code for deletion
    
    def accept(self):
        """Apply changes and close dialog"""
        try:
            # Get new properties
            new_props = {}
            
            # Basic properties
            new_props['color'] = self.current_color.name()
            new_props['linewidth'] = self.width_spin.value()
            
            # Line style
            style_map = ['-', '--', '-.', ':']
            new_props['linestyle'] = style_map[self.style_combo.currentIndex()]
            
            # Alpha
            new_props['alpha'] = self.alpha_slider.value() / 100.0
            
            # Arrow properties
            if self.arrow_radio.isChecked():
                arrow_map = ['->', '<->', '<-', 'none']
                new_props['arrow_style'] = arrow_map[self.arrow_style_combo.currentIndex()]
                new_props['arrow_size'] = self.arrow_size_spin.value()
                
                # Connection style
                connection_map = [
                    'arc3,rad=0',
                    'arc3,rad=0.1',
                    'arc3,rad=0.3',
                    'arc3,rad=-0.1',
                    'arc3,rad=-0.3'
                ]
                new_props['connection_style'] = connection_map[self.connection_combo.currentIndex()]
            
            # Update line type if changed
            new_type = 'arrow' if self.arrow_radio.isChecked() else 'line'
            if new_type != self.line.line_type:
                self.line.set_line_type(new_type)
            
            # Apply properties
            self.line.update_properties(**new_props)
            
            super().accept()
            
        except Exception as e:
            print(f"[ERROR] Failed to apply line changes: {e}")
            super().accept()  # Close anyway to avoid getting stuck
