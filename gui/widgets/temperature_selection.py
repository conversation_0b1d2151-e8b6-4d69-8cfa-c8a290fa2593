from typing import List, Dict
from PySide6.QtCore import Signal, Qt
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QLabel, QScrollArea,
                              QCheckBox, QListWidget, QListWidgetItem)
from color_manager import color_manager

class DraggableListWidget(QListWidget):
    """Custom QListWidget that supports drag and drop reordering"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setDragDropMode(QListWidget.InternalMove)
        self.setSelectionMode(QListWidget.SingleSelection)
        self.setStyleSheet("""
            QListWidget {
                background-color: #2d2d2d;
                border: 1px solid #446699;
                border-radius: 3px;
            }
            QListWidget::item {
                border-bottom: 1px solid #3d3d3d;
                border-radius: 2px;
            }
            QListWidget::item:hover {
                background-color: #3d3d3d;
            }
        """)

class TemperatureSelectionWidget(QWidget):
    dataSelectionChanged = Signal()  # Signal for selection changes
    orderChanged = Signal(list)      # Signal for order changes

    # Using centralized color manager for consistent color assignment

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checkbox_map = {}
        # No longer need local color_map - using centralized color manager
        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        self.layout = QVBoxLayout(self)
        self.layout.setSpacing(5)
        self.layout.setContentsMargins(1, 1, 1, 1)

        # Create list widget for draggable items
        self.list_widget = DraggableListWidget()
        self.list_widget.setMinimumHeight(150)
        self.list_widget.model().rowsMoved.connect(self._on_rows_moved)

        self.layout.addWidget(self.list_widget)

    def on_selection_changed(self, state):
        """Handle checkbox state changes"""
        # Emit our custom signal
        self.dataSelectionChanged.emit()

    def add_temperature_columns(self, columns: List[str]):
        """Add temperature columns with checkboxes"""
        self.list_widget.clear()
        self.checkbox_map.clear()

        for column in columns:
            # Create list item
            item = QListWidgetItem()
            self.list_widget.addItem(item)

            # Create widget for the item
            item_widget = QWidget()
            item_layout = QVBoxLayout(item_widget)
            item_layout.setContentsMargins(2, 2, 2, 2)

            # Create checkbox
            checkbox = QCheckBox(column)
            checkbox.setChecked(True)
            checkbox.setStyleSheet("""
                QCheckBox {
                    color: white;
                    font-size: 16px;
                    font-family: Arial;
                }
            """)

            # Get color from centralized color manager
            # This ensures consistent colors across all components
            color = color_manager.get_color(column)

            # Connect checkbox signal
            checkbox.stateChanged.connect(self.on_selection_changed)
            self.checkbox_map[column] = checkbox
            item_layout.addWidget(checkbox)

            # Set the custom widget for the item
            item.setSizeHint(item_widget.sizeHint())
            self.list_widget.setItemWidget(item, item_widget)

    def _on_rows_moved(self):
        """Handle row movement in the list widget"""
        new_order = self.get_all_columns()
        self.orderChanged.emit(new_order)
        # Don't emit dataSelectionChanged here as order change
        # doesn't affect selection state

    def get_selected_columns(self) -> List[str]:
        """Get list of currently selected columns in current order"""
        selected = []
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            widget = self.list_widget.itemWidget(item)
            checkbox = widget.findChild(QCheckBox)
            if checkbox and checkbox.isChecked():
                selected.append(checkbox.text())
        return selected

    def get_all_columns(self) -> List[str]:
        """Get list of all columns in current order"""
        columns = []
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            widget = self.list_widget.itemWidget(item)
            checkbox = widget.findChild(QCheckBox)
            if checkbox:
                columns.append(checkbox.text())
        return columns

    def get_column_color(self, column: str) -> str:
        """Get the color assigned to a column"""
        return color_manager.get_color(column)

    def set_column_checked(self, column: str, checked: bool):
        """Set the checked state of a specific column"""
        if column in self.checkbox_map:
            self.checkbox_map[column].setChecked(checked)

    def are_any_selected(self) -> bool:
        """Check if any columns are selected"""
        return any(checkbox.isChecked() for checkbox in self.checkbox_map.values())

    def get_checkbox_states(self) -> Dict[str, bool]:
        """Get the current state of all checkboxes"""
        return {col: checkbox.isChecked()
                for col, checkbox in self.checkbox_map.items()}

    def clear_all_columns(self):
        """Remove all temperature columns and their checkboxes"""
        try:
            self.list_widget.clear()
            self.checkbox_map.clear()
            # Note: Not clearing color manager here to maintain consistency
            # Colors will be reused if same columns are loaded again
            self.dataSelectionChanged.emit()
        except Exception as e:
            print(f"Error clearing temperature columns: {str(e)}")
