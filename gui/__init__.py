"""
GUI Package
-----------
A comprehensive package for integrating GUI

components:
- Dialog box (Choosing New test or Existing test)
- MainWindow (Containing all the components required for report generation and data analysis

Example Usage:
    from gui import Ui_dialogBoxWidget, UIVaprIdexMainWindow

    # Initialize components
    dialong_window = Ui_dialogBoxWidget()
    main_window = UIVaprIdexMainWindow()
"""

from .ui_VAPR_iDEX_Thruster_Analysis import Ui_VaprIdexMainWindow
from .checkableComboBox import CheckableComboBox
from .toggle_switch import MultiStateToggleSwitch
from .custom_splash_screen import CustomSplashScreen
from .hoverButton import HoverButton

__all__ = [
    'Ui_VaprIdexMainWindow',
    'CheckableComboBox',
    'MultiStateToggleSwitch'
]

__version__ = '1.0.0'
__author__ = '<PERSON><PERSON><PERSON>'
