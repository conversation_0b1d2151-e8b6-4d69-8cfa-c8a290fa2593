"""
Draggable Line/Arrow Implementation
Handles creation and manipulation of draggable lines and arrows on plots
"""

import uuid
import numpy as np
from matplotlib.patches import FancyArrowPatch
from matplotlib.lines import Line2D
import matplotlib.patches as mpatches


class DraggableLine:
    """
    A class to create and manage draggable lines and arrows on matplotlib plots.
    Supports various line styles, colors, thickness, and arrow configurations.
    """
    
    def __init__(self, start_point, end_point, canvas, main_window=None, line_type='arrow'):
        self.start_point = start_point
        self.end_point = end_point
        self.canvas = canvas
        self.main_window = main_window
        self.line_type = line_type  # 'line' or 'arrow'
        self.line_id = f"line_{uuid.uuid4().hex[:8]}"
        
        # Line properties
        self.properties = {
            'color': 'red',
            'linewidth': 2,
            'linestyle': '-',  # '-', '--', '-.', ':'
            'alpha': 1.0,
            'arrow_style': '->',  # '->', '<->', '<-', 'none'
            'arrow_size': 15,
            'connection_style': 'arc3,rad=0'  # For curved arrows
        }
        
        # Interaction state
        self.press = None
        self.dragging_point = None  # 'start', 'end', or 'line'
        self.connected = False
        self.artist = None
        
        # Create the visual element
        self.create_artist()
        
        # Register with main window if available
        if self.main_window and hasattr(self.main_window, 'register_draggable_line'):
            self.main_window.register_draggable_line(self)
    
    def create_artist(self):
        """Create the matplotlib artist (line or arrow)"""
        if self.line_type == 'arrow':
            self.artist = FancyArrowPatch(
                self.start_point, self.end_point,
                arrowstyle=self.properties['arrow_style'],
                color=self.properties['color'],
                linewidth=self.properties['linewidth'],
                linestyle=self.properties['linestyle'],
                alpha=self.properties['alpha'],
                mutation_scale=self.properties['arrow_size'],
                connectionstyle=self.properties['connection_style'],
                picker=True,
                pickradius=5
            )
        else:  # line
            self.artist = Line2D(
                [self.start_point[0], self.end_point[0]],
                [self.start_point[1], self.end_point[1]],
                color=self.properties['color'],
                linewidth=self.properties['linewidth'],
                linestyle=self.properties['linestyle'],
                alpha=self.properties['alpha'],
                picker=True,
                pickradius=5
            )
        
        # Add to the current axes
        if hasattr(self.canvas, 'figure') and self.canvas.figure.axes:
            ax = self.canvas.figure.axes[0]  # Use first axes
            ax.add_patch(self.artist) if self.line_type == 'arrow' else ax.add_line(self.artist)
            self.canvas.draw_idle()
    
    def update_artist(self):
        """Update the visual appearance of the artist"""
        if self.line_type == 'arrow' and isinstance(self.artist, FancyArrowPatch):
            # Remove old artist
            self.artist.remove()
            # Create new one with updated properties
            self.create_artist()
        elif self.line_type == 'line' and isinstance(self.artist, Line2D):
            self.artist.set_data([self.start_point[0], self.end_point[0]], 
                               [self.start_point[1], self.end_point[1]])
            self.artist.set_color(self.properties['color'])
            self.artist.set_linewidth(self.properties['linewidth'])
            self.artist.set_linestyle(self.properties['linestyle'])
            self.artist.set_alpha(self.properties['alpha'])
            self.canvas.draw_idle()
    
    def handle_pick(self, event):
        """Handle pick events when this line is clicked"""
        if event.artist == self.artist and self.connected:
            print(f"[DEBUG] Line {self.line_id} PICKED!")
            
            # Determine what part was clicked
            click_point = (event.mouseevent.xdata, event.mouseevent.ydata)
            if click_point[0] is None or click_point[1] is None:
                return False
            
            # Calculate distances to start, end, and line
            start_dist = np.sqrt((click_point[0] - self.start_point[0])**2 + 
                               (click_point[1] - self.start_point[1])**2)
            end_dist = np.sqrt((click_point[0] - self.end_point[0])**2 + 
                             (click_point[1] - self.end_point[1])**2)
            
            # Threshold for point selection (in data coordinates)
            threshold = self._get_selection_threshold()
            
            if start_dist < threshold:
                self.dragging_point = 'start'
            elif end_dist < threshold:
                self.dragging_point = 'end'
            else:
                self.dragging_point = 'line'
            
            self.press = (click_point, event.mouseevent.x, event.mouseevent.y)
            print(f"[DEBUG] Dragging: {self.dragging_point}")
            return True
        return False
    
    def handle_motion(self, event):
        """Handle mouse motion events"""
        if self.press is None or not self.connected:
            return False
        
        if event.xdata is None or event.ydata is None:
            return False
        
        try:
            (x0, y0), xpress, ypress = self.press
            dx = event.xdata - x0
            dy = event.ydata - y0
            
            if self.dragging_point == 'start':
                self.start_point = (self.start_point[0] + dx, self.start_point[1] + dy)
            elif self.dragging_point == 'end':
                self.end_point = (self.end_point[0] + dx, self.end_point[1] + dy)
            elif self.dragging_point == 'line':
                # Move entire line
                self.start_point = (self.start_point[0] + dx, self.start_point[1] + dy)
                self.end_point = (self.end_point[0] + dx, self.end_point[1] + dy)
            
            self.update_artist()
            self.press = ((event.xdata, event.ydata), xpress, ypress)
            return True
            
        except Exception as e:
            print(f"[DEBUG] Error in line motion: {e}")
            return False
    
    def handle_release(self, event):
        """Handle mouse release events"""
        if self.press is None or not self.connected:
            return False
        
        print(f"[DEBUG] Line {self.line_id} released")
        self.press = None
        self.dragging_point = None
        self.canvas.draw_idle()
        return True
    
    def _get_selection_threshold(self):
        """Get the threshold for point selection based on current zoom level"""
        if hasattr(self.canvas, 'figure') and self.canvas.figure.axes:
            ax = self.canvas.figure.axes[0]
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()
            # Use 2% of the smaller axis range as threshold
            return min(abs(xlim[1] - xlim[0]), abs(ylim[1] - ylim[0])) * 0.02
        return 1.0  # Default threshold
    
    def connect(self):
        """Enable interaction"""
        self.connected = True
        print(f"[DEBUG] Connected line {self.line_id}")
    
    def disconnect(self):
        """Disable interaction"""
        self.connected = False
        print(f"[DEBUG] Disconnected line {self.line_id}")
    
    def remove(self):
        """Remove the line from the plot"""
        if self.artist:
            self.artist.remove()
            self.canvas.draw_idle()
    
    def update_properties(self, **kwargs):
        """Update line properties"""
        self.properties.update(kwargs)
        self.update_artist()
    
    def get_properties(self):
        """Get current line properties"""
        return self.properties.copy()
    
    def set_line_type(self, line_type):
        """Change between line and arrow"""
        if line_type != self.line_type:
            self.line_type = line_type
            self.artist.remove()
            self.create_artist()
    
    def contains_point(self, point, tolerance=None):
        """Check if a point is near this line"""
        if tolerance is None:
            tolerance = self._get_selection_threshold()
        
        # Check if point is near the line segment
        x, y = point
        x1, y1 = self.start_point
        x2, y2 = self.end_point
        
        # Distance from point to line segment
        A = x - x1
        B = y - y1
        C = x2 - x1
        D = y2 - y1
        
        dot = A * C + B * D
        len_sq = C * C + D * D
        
        if len_sq == 0:
            # Line is actually a point
            distance = np.sqrt(A * A + B * B)
        else:
            param = dot / len_sq
            if param < 0:
                xx, yy = x1, y1
            elif param > 1:
                xx, yy = x2, y2
            else:
                xx = x1 + param * C
                yy = y1 + param * D
            
            dx = x - xx
            dy = y - yy
            distance = np.sqrt(dx * dx + dy * dy)
        
        return distance <= tolerance
