"""
Line Drawing Manager
Handles creation, editing, and management of draggable lines and arrows
"""

from typing import Dict, List, Optional, Tuple
from .draggable_line import DraggableLine


class LineDrawingManager:
    """
    Manages line and arrow drawing functionality on plots.
    Handles creation, editing, deletion, and styling of lines/arrows.
    """
    
    def __init__(self, main_window=None):
        self.main_window = main_window
        self.lines: Dict[str, DraggableLine] = {}
        self.drawing_mode = False
        self.current_line_type = 'arrow'  # 'line' or 'arrow'
        self.drawing_start_point = None
        self.preview_line = None
        
        # Default properties for new lines
        self.default_properties = {
            'color': 'red',
            'linewidth': 2,
            'linestyle': '-',
            'alpha': 1.0,
            'arrow_style': '->',
            'arrow_size': 15,
            'connection_style': 'arc3,rad=0'
        }
    
    def set_drawing_mode(self, enabled: bool):
        """Enable or disable line drawing mode"""
        self.drawing_mode = enabled
        if self.main_window:
            if enabled:
                self.main_window.ui.lblLogInfo.setText("Line drawing mode enabled - click and drag to draw lines/arrows")
            else:
                self.main_window.ui.lblLogInfo.setText("Line drawing mode disabled")
                # Cancel any ongoing drawing
                self.cancel_drawing()
    
    def set_line_type(self, line_type: str):
        """Set the type of line to draw ('line' or 'arrow')"""
        if line_type in ['line', 'arrow']:
            self.current_line_type = line_type
            print(f"[DEBUG] Line type set to: {line_type}")
    
    def is_drawing_mode_enabled(self) -> bool:
        """Check if line drawing mode is enabled"""
        return self.drawing_mode
    
    def start_drawing(self, axes, start_point: Tuple[float, float]):
        """Start drawing a line from the given point"""
        if not self.drawing_mode:
            return False
        
        self.drawing_start_point = start_point
        print(f"[DEBUG] Started drawing {self.current_line_type} from {start_point}")
        
        # Create preview line
        self.create_preview_line(axes, start_point, start_point)
        return True
    
    def update_drawing(self, end_point: Tuple[float, float]):
        """Update the preview line while drawing"""
        if self.drawing_start_point is None or self.preview_line is None:
            return False
        
        # Update preview line end point
        self.preview_line.end_point = end_point
        self.preview_line.update_artist()
        return True
    
    def finish_drawing(self, end_point: Tuple[float, float]) -> Optional[DraggableLine]:
        """Finish drawing and create the final line"""
        if self.drawing_start_point is None:
            return None
        
        # Remove preview line
        if self.preview_line:
            self.preview_line.remove()
            self.preview_line = None
        
        # Create final line
        if self.main_window and hasattr(self.main_window, 'current_canvas'):
            line = DraggableLine(
                start_point=self.drawing_start_point,
                end_point=end_point,
                canvas=self.main_window.current_canvas,
                main_window=self.main_window,
                line_type=self.current_line_type
            )
            
            # Apply default properties
            line.update_properties(**self.default_properties)
            
            # Store the line
            self.lines[line.line_id] = line
            
            # Enable dragging if drag mode is on
            if hasattr(self.main_window, 'annotation_drag_mode') and self.main_window.annotation_drag_mode:
                line.connect()
            
            print(f"[DEBUG] Created {self.current_line_type}: {line.line_id}")
            
            # Reset drawing state
            self.drawing_start_point = None
            
            return line
        
        return None
    
    def cancel_drawing(self):
        """Cancel the current drawing operation"""
        if self.preview_line:
            self.preview_line.remove()
            self.preview_line = None
        self.drawing_start_point = None
        print("[DEBUG] Drawing cancelled")
    
    def create_preview_line(self, axes, start_point: Tuple[float, float], end_point: Tuple[float, float]):
        """Create a preview line for visual feedback during drawing"""
        if self.main_window and hasattr(self.main_window, 'current_canvas'):
            self.preview_line = DraggableLine(
                start_point=start_point,
                end_point=end_point,
                canvas=self.main_window.current_canvas,
                main_window=None,  # Don't register preview lines
                line_type=self.current_line_type
            )
            
            # Make preview line semi-transparent
            preview_props = self.default_properties.copy()
            preview_props['alpha'] = 0.5
            preview_props['linestyle'] = '--'
            self.preview_line.update_properties(**preview_props)
    
    def delete_line(self, line: DraggableLine):
        """Delete a line"""
        try:
            # Remove from matplotlib
            line.remove()
            
            # Disconnect from event system
            line.disconnect()
            
            # Remove from our tracking
            if line.line_id in self.lines:
                del self.lines[line.line_id]
            
            print(f"[DEBUG] Deleted line: {line.line_id}")
            
        except Exception as e:
            print(f"[ERROR] Failed to delete line: {e}")
    
    def clear_all_lines(self):
        """Clear all lines"""
        for line_id in list(self.lines.keys()):
            self.delete_line(self.lines[line_id])
        self.lines.clear()
        self.cancel_drawing()
    
    def get_line_count(self) -> int:
        """Get the number of lines"""
        return len(self.lines)
    
    def find_line_at_point(self, point: Tuple[float, float], tolerance=None) -> Optional[DraggableLine]:
        """Find a line near the given point"""
        for line in self.lines.values():
            if line.contains_point(point, tolerance):
                return line
        return None
    
    def update_default_properties(self, **kwargs):
        """Update default properties for new lines"""
        self.default_properties.update(kwargs)
        print(f"[DEBUG] Updated default line properties: {kwargs}")
    
    def get_default_properties(self):
        """Get current default properties"""
        return self.default_properties.copy()
    
    def export_lines(self) -> List[Dict]:
        """Export line data for saving/loading"""
        lines_data = []
        for line_id, line in self.lines.items():
            try:
                line_data = {
                    'line_id': line_id,
                    'start_point': line.start_point,
                    'end_point': line.end_point,
                    'line_type': line.line_type,
                    'properties': line.get_properties()
                }
                lines_data.append(line_data)
            except Exception as e:
                print(f"[ERROR] Failed to export line {line_id}: {e}")
        
        return lines_data
    
    def import_lines(self, axes, lines_data: List[Dict]):
        """Import line data from saved state"""
        for line_data in lines_data:
            try:
                if self.main_window and hasattr(self.main_window, 'current_canvas'):
                    line = DraggableLine(
                        start_point=line_data['start_point'],
                        end_point=line_data['end_point'],
                        canvas=self.main_window.current_canvas,
                        main_window=self.main_window,
                        line_type=line_data['line_type']
                    )
                    
                    # Apply saved properties
                    line.update_properties(**line_data['properties'])
                    
                    # Store the line
                    self.lines[line_data['line_id']] = line
                    
                    print(f"[DEBUG] Imported line: {line_data['line_id']}")
                    
            except Exception as e:
                print(f"[ERROR] Failed to import line: {e}")
    
    def enable_line_dragging(self):
        """Enable dragging for all lines"""
        for line in self.lines.values():
            line.connect()
        print(f"[DEBUG] Enabled dragging for {len(self.lines)} lines")
    
    def disable_line_dragging(self):
        """Disable dragging for all lines"""
        for line in self.lines.values():
            line.disconnect()
        print(f"[DEBUG] Disabled dragging for {len(self.lines)} lines")
