class DraggableAnnotation:
    """
    A class to make matplotlib annotations draggable.
    Uses a centralized event manager to avoid conflicts.
    """
    def __init__(self, annotation, canvas, main_window=None, position_key=None):
        print(f"[DEBUG] Creating DraggableAnnotation for {position_key}")
        self.annotation = annotation
        self.canvas = canvas
        self.main_window = main_window
        self.press = None
        self.position_key = position_key
        self.connected = False

        # Store original position
        if hasattr(annotation, 'xytext') and annotation.xytext is not None:
            self.original_position = annotation.xytext
            self.use_xytext = True
        else:
            self.original_position = annotation.get_position()
            self.use_xytext = False

        # Make annotation pickable
        self.annotation.set_picker(True)
        print(f"[DEBUG] Set picker=True for annotation {position_key}")

        # Register with the main window's event manager
        if self.main_window and hasattr(self.main_window, 'register_draggable_annotation'):
            print(f"[DEBUG] Registering {position_key} with main window")
            self.main_window.register_draggable_annotation(self)
            self.connected = True
        else:
            print(f"[DEBUG] Cannot register {position_key}: main_window={self.main_window}, has_method={hasattr(self.main_window, 'register_draggable_annotation') if self.main_window else False}")

    def connect(self):
        """Connect to event manager"""
        if self.main_window and hasattr(self.main_window, 'register_draggable_annotation'):
            self.main_window.register_draggable_annotation(self)
            self.connected = True
            print(f"[DEBUG] Connected annotation {self.position_key} to event manager")

    def disconnect(self):
        """Disconnect from event manager"""
        if self.main_window and hasattr(self.main_window, 'unregister_draggable_annotation'):
            self.main_window.unregister_draggable_annotation(self)
            self.connected = False
            print(f"[DEBUG] Disconnected annotation {self.position_key} from event manager")

    def handle_pick(self, event):
        """Handle pick events when this annotation is clicked"""
        if event.artist == self.annotation and self.connected:
            print(f"[DEBUG] Annotation {self.position_key} PICKED! Starting drag...")
            # Store current position for dragging
            if self.use_xytext:
                current_pos = self.annotation.xytext
            else:
                current_pos = self.annotation.get_position()
            self.press = (current_pos, event.mouseevent.x, event.mouseevent.y)
            print(f"[DEBUG] Stored press data: {self.press}")
            return True
        return False

    def handle_motion(self, event):
        """Handle mouse motion events"""
        if self.press is None or not self.connected:
            return False

        print(f"[DEBUG] Motion event for {self.position_key}: mouse=({event.x}, {event.y})")

        try:
            (x0, y0), xpress, ypress = self.press
            dx = event.x - xpress
            dy = event.y - ypress

            # Convert canvas pixels to data coordinates
            trans = self.annotation.axes.transData.inverted()
            dxa, dya = trans.transform((dx, dy)) - trans.transform((0, 0))

            # Update annotation position
            new_x = x0 + dxa
            new_y = y0 + dya

            print(f"[DEBUG] Moving {self.position_key}: delta=({dx}, {dy}), new_pos=({new_x:.2f}, {new_y:.2f})")

            if self.use_xytext:
                # Update xytext position for annotations with arrows
                self.annotation.xytext = (new_x, new_y)
            else:
                # Update regular position
                self.annotation.set_position((new_x, new_y))

            self.canvas.draw_idle()
            return True
        except Exception as e:
            print(f"[DEBUG] Error in motion for {self.position_key}: {e}")
            return False

    def handle_release(self, event):
        """Handle mouse release events"""
        if self.press is None or not self.connected:
            return False

        print(f"[DEBUG] Release event for {self.position_key}")
        # Save the new position
        if self.position_key and self.main_window:
            if self.use_xytext:
                current_pos = self.annotation.xytext
            else:
                current_pos = self.annotation.get_position()
            self.main_window.annotation_positions[self.position_key] = current_pos
            print(f"[DEBUG] Saved position for {self.position_key}: {current_pos}")

        self.press = None
        self.canvas.draw_idle()
        return True

    def reset_position(self):
        """Reset annotation to its original position"""
        if self.use_xytext:
            self.annotation.xytext = self.original_position
        else:
            self.annotation.set_position(self.original_position)
        self.canvas.draw_idle()