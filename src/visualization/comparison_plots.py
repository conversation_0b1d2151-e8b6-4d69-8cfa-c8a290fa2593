"""
Comparison Visualization Module
------------------------------
Creates various plots for enhanced comparison analysis.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import warnings

# Try to import optional dependencies
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False
    warnings.warn("Seaborn not installed. Some features may be limited.")

try:
    from scipy import stats
    from scipy.cluster.hierarchy import dendrogram, linkage
    from scipy.spatial.distance import pdist
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False
    warnings.warn("SciPy not installed. Some features may be limited.")


class ComparisonPlotManager:
    """
    Manages creation of various comparison plots for enhanced analysis.

    Features:
    - Bar plots for metric comparisons
    - Line plots for trend analysis
    - Scatter plots for correlation analysis
    - Radar plots for multi-dimensional comparison
    - Enhanced heatmaps for performance matrices with multiple normalization options
    """

    def __init__(self):
        # Set style for better-looking plots
        plt.style.use('default')
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        self.figure_size = (10, 6)

        # Performance metric weights for scoring
        # These weights determine the importance of each metric in the overall score
        # Higher weight = more important metric
        self.metric_weights = {
            'thrust': 1.5,              # High importance - primary performance metric
            'specific_impulse': 2.0,    # Highest importance - efficiency metric
            'total_impulse': 1.0,       # Medium importance
            'burn_time': 0.8,           # Lower importance
            'mass_flow_rate': 0.5,      # Low importance
            'chamber_pressure': 0.7,    # Medium-low importance (lower is better)
            'thrust_coefficient': 1.2   # Medium-high importance
        }

        # Performance categories for classification
        # Based on average normalized performance (0.0 to 1.0)
        self.performance_categories = {
            'Excellent': (0.8, 1.0),    # 80-100% performance
            'Good': (0.6, 0.8),         # 60-80% performance
            'Average': (0.4, 0.6),      # 40-60% performance
            'Below Average': (0.2, 0.4), # 20-40% performance
            'Poor': (0.0, 0.2)          # 0-20% performance
        }

    def create_heater_performance_bar_plot(self, heater_data: Dict, test_names: List[str]) -> plt.Figure:
        """Create bar plot comparing heater performance metrics."""
        try:
            fig, ax = plt.subplots(figsize=self.figure_size)

            metrics = ['total_heater_on_time', 'average_cycle_duration', 'heater_efficiency']
            metric_labels = ['Total On Time (s)', 'Avg Cycle Duration (s)', 'Efficiency (°C/s)']

            x = np.arange(len(test_names))
            width = 0.25

            for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
                values = []
                for test_name in test_names:
                    if test_name in heater_data and metric in heater_data[test_name]:
                        values.append(heater_data[test_name][metric])
                    else:
                        values.append(0)

                ax.bar(x + i*width, values, width, label=label, alpha=0.8)

            ax.set_xlabel('Tests')
            ax.set_ylabel('Values')
            ax.set_title('Heater Performance Comparison', fontsize=14, fontweight='bold')
            ax.set_xticks(x + width)
            ax.set_xticklabels(test_names)
            ax.legend()
            ax.grid(True, alpha=0.3)

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating heater performance bar plot: {str(e)}")
            return self._create_error_plot("Heater Performance Bar Plot Error")

    def create_performance_scatter_plot(self, performance_data: Dict, test_names: List[str]) -> plt.Figure:
        """Create scatter plot showing performance correlations."""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle('Performance Correlations', fontsize=16, fontweight='bold')

            # correlations = [
            #     ('thrust', 'specific_impulse', 'Thrust vs Specific Impulse', axes[0, 0]),
            #     ('thrust', 'mass_flow_rate', 'Thrust vs Mass Flow Rate', axes[0, 1]),
            #     ('specific_impulse', 'total_impulse', 'Specific Impulse vs Total Impulse', axes[1, 0]),
            #     ('chamber_pressure', 'thrust_coefficient', 'Chamber Pressure vs Thrust Coefficient', axes[1, 1])
            # ]

            correlations = [
                ('specific_impulse', 'thrust', 'Specific Impulse vs Thrust', axes[0, 0]),
                ('mass_flow_rate', 'thrust', 'Mass Flow Rate vs Thrust', axes[0, 1]),
                ('burn_time', 'specific_impulse', 'Burn time vs Specific Impulse', axes[1, 0]),
                ('chamber_pressure', 'thrust_coefficient', 'Chamber Pressure vs Thrust Coefficient', axes[1, 1])
            ]


            for x_metric, y_metric, title, ax in correlations:
                x_values = []
                y_values = []
                labels = []

                for i, test_name in enumerate(test_names):
                    if test_name in performance_data:
                        x_val = performance_data[test_name].get(x_metric, None)
                        y_val = performance_data[test_name].get(y_metric, None)

                        if x_val is not None and y_val is not None:
                            x_values.append(x_val)
                            y_values.append(y_val)
                            labels.append(test_name)

                if x_values and y_values:
                    scatter = ax.scatter(x_values, y_values, c=range(len(x_values)),
                                       cmap='viridis', s=100, alpha=0.7)

                    # Add labels
                    for i, label in enumerate(labels):
                        ax.annotate(label, (x_values[i], y_values[i]),
                                  xytext=(5, 5), textcoords='offset points', fontsize=8)

                    # Add trend line if we have enough points
                    if len(x_values) > 1:
                        z = np.polyfit(x_values, y_values, 1)
                        p = np.poly1d(z)
                        ax.plot(sorted(x_values), p(sorted(x_values)), "r--", alpha=0.8)

                    ax.set_xlabel(x_metric.replace('_', ' ').title())
                    ax.set_ylabel(y_metric.replace('_', ' ').title())
                    ax.set_title(title)
                    ax.grid(True, alpha=0.3)
                else:
                    ax.text(0.5, 0.5, 'No Data Available', ha='center', va='center',
                           transform=ax.transAxes, fontsize=12, style='italic')
                    ax.set_title(title)

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating performance scatter plot: {str(e)}")
            return self._create_error_plot("Performance Scatter Plot Error")

    def create_efficiency_radar_plot(self, efficiency_data: Dict, test_names: List[str]) -> plt.Figure:
        """Create radar plot comparing efficiency metrics."""
        try:
            fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))

            metrics = [
                'propellant_utilization_efficiency',
                'energy_efficiency',
                'thermal_efficiency',
                'overall_system_efficiency',
                'performance_efficiency_index'
            ]

            metric_labels = [
                'Propellant\nUtilization',
                'Energy\nEfficiency',
                'Thermal\nEfficiency',
                'Overall\nSystem',
                'Performance\nIndex'
            ]

            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            angles += angles[:1]  # Complete the circle

            for i, test_name in enumerate(test_names):
                if test_name in efficiency_data:
                    values = []
                    for metric in metrics:
                        value = efficiency_data[test_name].get(metric, 0)
                        values.append(min(100, value))  # Cap at 100

                    values += values[:1]  # Complete the circle

                    ax.plot(angles, values, 'o-', linewidth=2, label=test_name,
                           color=self.colors[i % len(self.colors)])
                    ax.fill(angles, values, alpha=0.25, color=self.colors[i % len(self.colors)])

            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metric_labels)
            ax.set_ylim(0, 100)
            ax.set_title('Efficiency Comparison Radar Chart', fontsize=16, fontweight='bold', pad=20)
            ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.1))
            ax.grid(True)

            return fig

        except Exception as e:
            print(f"Error creating efficiency radar plot: {str(e)}")
            return self._create_error_plot("Efficiency Radar Plot Error")

    def create_thermal_comparison_line_plot(self, thermal_data: Dict, test_names: List[str]) -> plt.Figure:
        """Create line plots comparing thermal characteristics."""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle('Thermal Characteristics Comparison', fontsize=16, fontweight='bold')

            metrics = [
                ('max_temperature_rise_rate', 'Max Rise Rate (°C/s)', axes[0, 0]),
                ('temperature_stability_score', 'Stability Score (%)', axes[0, 1]),
                ('spatial_consistency_score', 'Spatial Consistency (%)', axes[1, 0]),
                ('average_thermal_time_constant', 'Time Constant (s)', axes[1, 1])
            ]

            for metric_key, ylabel, ax in metrics:
                values = []
                labels = []

                for test_name in test_names:
                    if test_name in thermal_data and metric_key in thermal_data[test_name]:
                        value = thermal_data[test_name][metric_key]
                        if isinstance(value, (int, float)) and not np.isnan(value):
                            values.append(value)
                            labels.append(test_name)

                if values:
                    # Create line plot with markers
                    ax.plot(range(len(labels)), values, 'o-', linewidth=2, markersize=8,
                           color=self.colors[0], alpha=0.7)

                    # Add value labels
                    for i, (label, value) in enumerate(zip(labels, values)):
                        ax.annotate(f'{value:.2f}', (i, value),
                                  textcoords="offset points", xytext=(0,10), ha='center')

                    ax.set_xticks(range(len(labels)))
                    ax.set_xticklabels(labels, rotation=45 if len(labels) > 3 else 0)
                    ax.set_ylabel(ylabel)
                    ax.set_title(ylabel.split('(')[0].strip(), fontweight='bold')
                    ax.grid(True, alpha=0.3)

                    # Highlight best/worst performers
                    if len(values) > 1:
                        best_idx = np.argmax(values) if 'time_constant' not in metric_key else np.argmin(values)
                        ax.scatter(best_idx, values[best_idx], color='green', s=150, alpha=0.7, marker='*')
                else:
                    ax.text(0.5, 0.5, 'No Data Available', ha='center', va='center',
                           transform=ax.transAxes, fontsize=12, style='italic')
                    ax.set_title(ylabel.split('(')[0].strip(), fontweight='bold')

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating thermal comparison line plot: {str(e)}")
            return self._create_error_plot("Thermal Comparison Line Plot Error")

    def create_performance_heatmap(self, all_data: Dict, test_names: List[str],
                                  normalization_method: str = 'minmax',
                                  show_missing: bool = True,
                                  cluster_tests: bool = True,
                                  add_overall_score: bool = True) -> plt.Figure:
        """
        Create enhanced heatmap showing normalized performance across all metrics.

        Args:
            all_data: Dictionary of test data
            test_names: List of test names
            normalization_method: 'minmax', 'zscore', or 'percentile'
            show_missing: Whether to show missing data indicators
            cluster_tests: Whether to cluster similar tests
            add_overall_score: Whether to add overall performance score column
        """
        try:
            # Adjust figure size based on number of tests and metrics
            n_tests = len(test_names)
            fig_height = max(8, 6 + (n_tests - 5) * 0.3)
            fig, ax = plt.subplots(figsize=(14, fig_height))

            # Define metrics to include in heatmap
            metrics = [
                'thrust', 'specific_impulse', 'total_impulse', 'burn_time',
                'mass_flow_rate', 'chamber_pressure', 'thrust_coefficient'
            ]

            metric_labels = [
                'Thrust (mN)', 'Specific\nImpulse (s)', 'Total\nImpulse (Ns)', 'Burn\nTime (s)',
                'Mass Flow\nRate (mg/s)', 'Chamber\nPressure (mbar)', 'Thrust\nCoefficient'
            ]

            # Prepare data matrix
            data_matrix = []
            valid_tests = []
            test_dates = []  # For date range in title
            missing_data_mask = []

            print(f"\nProcessing {len(test_names)} tests for heatmap...")

            for test_idx, test_name in enumerate(test_names):
                if test_name in all_data:
                    print(f"\nProcessing {test_name}:")
                    test_values = []
                    missing_mask = []
                    valid_metrics = 0

                    for metric in metrics:
                        value = all_data[test_name].get(metric, None)

                        # Handle various data types and formats
                        if value is not None:
                            # If value is a string, try to extract numeric value
                            if isinstance(value, str):
                                import re
                                # Extract numeric value from string (e.g., "1.71 mN" -> 1.71)
                                match = re.search(r'[-+]?\d*\.?\d+', value)
                                if match:
                                    value = float(match.group())
                                else:
                                    value = None

                            # Now check if it's a valid number
                            if isinstance(value, (int, float)) and not np.isnan(value) and value != 0:
                                test_values.append(float(value))
                                missing_mask.append(False)
                                valid_metrics += 1
                            else:
                                test_values.append(0)
                                missing_mask.append(True)
                        else:
                            test_values.append(0)
                            missing_mask.append(True)

                    if valid_metrics > 0:  # Only include tests with some valid data
                        data_matrix.append(test_values)
                        valid_tests.append(test_name)
                        missing_data_mask.append(missing_mask)

                        # Extract date if available in test name
                        try:
                            # Assuming test names contain dates
                            import re
                            date_match = re.search(r'\d{4}-\d{2}-\d{2}', test_name)
                            if date_match:
                                test_dates.append(date_match.group())
                        except:
                            pass

            if not data_matrix or len(data_matrix) == 0:
                ax.text(0.5, 0.5, 'No Valid Data for Heatmap', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title('Performance Heatmap', fontsize=16, fontweight='bold')
                return fig

            # Convert to numpy array
            data_array = np.array(data_matrix)
            missing_array = np.array(missing_data_mask)

            # Debug: Print data before normalization
            print("\nData before normalization:")
            for i, test in enumerate(valid_tests):
                print(f"{test}: thrust={data_array[i, 0]:.2f}, specific_impulse={data_array[i, 1]:.2f}")

            # Apply normalization with metric names for proper direction
            normalized_data = self._normalize_data(data_array, method=normalization_method, metrics=metrics)

            # Debug: Print data after normalization
            print("\nData after normalization:")
            for i, test in enumerate(valid_tests):
                print(f"{test}: thrust_norm={normalized_data[i, 0]:.2f}, specific_impulse_norm={normalized_data[i, 1]:.2f}")

            # Calculate overall performance scores if requested
            if add_overall_score:
                # Use normalized data for scoring (chamber pressure already inverted)
                performance_scores = self._calculate_weighted_scores(normalized_data[:, :len(metrics)], metrics)

                # The scores are already between 0-1 from the normalized data
                normalized_scores = performance_scores

                # Add to data
                normalized_data = np.column_stack((normalized_data, normalized_scores))
                metric_labels.append('Overall\nScore')

            # Cluster tests if requested and scipy is available
            if cluster_tests and HAS_SCIPY and len(valid_tests) > 2:
                cluster_order = self._cluster_tests(normalized_data[:, :-1] if add_overall_score else normalized_data)
                # CRITICAL: Reorder ALL arrays consistently
                normalized_data = normalized_data[cluster_order, :]
                data_array = data_array[cluster_order, :]  # This was missing!
                valid_tests = [valid_tests[i] for i in cluster_order]
                missing_array = missing_array[cluster_order, :]
                if add_overall_score:
                    performance_scores = performance_scores[cluster_order]

            # Create masked array for missing data
            if show_missing:
                masked_data = np.ma.masked_where(
                    np.column_stack((missing_array, np.zeros(len(missing_array), dtype=bool))) if add_overall_score else missing_array,
                    normalized_data
                )
            else:
                masked_data = normalized_data

            # Create heatmap
            cmap = plt.cm.RdYlGn
            cmap.set_bad(color='lightgray', alpha=0.5)
            im = ax.imshow(masked_data, cmap=cmap, aspect='auto', vmin=0, vmax=1)

            # Set ticks and labels
            ax.set_xticks(range(len(metric_labels)))
            ax.set_xticklabels(metric_labels, rotation=45, ha='right')
            ax.set_yticks(range(len(valid_tests)))
            ax.set_yticklabels(valid_tests)

            # Add colorbar with description
            cbar = plt.colorbar(im, ax=ax)
            # cbar.set_label('Normalized Performance (0=Worst, 1=Best)', rotation=270, labelpad=20)

            # Add text annotations with both normalized and actual values
            for i in range(len(valid_tests)):
                for j in range(len(metrics) + (1 if add_overall_score else 0)):
                    if j < len(metrics):
                        # Use the properly ordered data_array
                        actual_val = data_array[i, j]
                        is_missing = missing_array[i, j]
                    else:
                        # Overall score column - show the weighted score
                        actual_val = performance_scores[i]
                        is_missing = False

                    norm_val = normalized_data[i, j]

                    if not is_missing:
                        # Determine text color based on background
                        text_color = "white" if norm_val < 0.3 else "black"

                        # Format text based on value magnitude
                        # if actual_val > 100:
                        #     text = f'{norm_val:.2f}\n({actual_val:.0f})'
                        # elif actual_val > 10:
                        #     text = f'{norm_val:.2f}\n({actual_val:.1f})'
                        # else:
                        #     text = f'{norm_val:.2f}\n({actual_val:.2f})'

                        if actual_val > 100:
                            text = f'{actual_val:.0f}'
                        elif actual_val > 10:
                            text = f'{actual_val:.1f}'
                        else:
                            text = f'{actual_val:.2f}'

                        ax.text(j, i, text, ha="center", va="center",
                               color=text_color, fontsize=8, fontweight='bold')
                    elif show_missing:
                        # Add hatching for missing data
                        rect = patches.Rectangle((j-0.5, i-0.5), 1, 1,
                                               fill=False, hatch='///',
                                               edgecolor='gray', linewidth=1)
                        ax.add_patch(rect)
                        ax.text(j, i, 'N/A', ha="center", va="center",
                               color='gray', fontsize=8, style='italic')

            # Add divider lines between metric categories
            ax.axvline(x=2.5, color='black', linewidth=2, alpha=0.3)  # After propulsion metrics
            ax.axvline(x=4.5, color='black', linewidth=2, alpha=0.3)  # After flow metrics
            if add_overall_score:
                ax.axvline(x=len(metrics)-0.5, color='black', linewidth=3, alpha=0.5)  # Before overall score

            # Add performance category indicators
            self._add_performance_indicators(ax, normalized_data, len(valid_tests))

            # Create informative title
            date_range = ""
            if test_dates:
                date_range = f"\nDate Range: {min(test_dates)} to {max(test_dates)}"

            title = f'Performance Heatmap - {len(valid_tests)} Tests Comparison{date_range}'
            title += f'\nNormalization: {normalization_method.upper()}'
            if cluster_tests and len(valid_tests) > 2:
                title += ' (Clustered by Similarity)'

            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)

            # Add grid for clarity
            ax.set_xticks(np.arange(len(metric_labels)+1)-0.5, minor=True)
            ax.set_yticks(np.arange(len(valid_tests)+1)-0.5, minor=True)
            ax.grid(which="minor", color="black", linestyle='-', linewidth=0.5)
            ax.tick_params(which="minor", size=0)

            plt.tight_layout()

            # Store data for export
            self.last_heatmap_data = {
                'raw_data': data_array,
                'normalized_data': normalized_data,
                'test_names': valid_tests,
                'metrics': metrics + (['overall_score'] if add_overall_score else []),
                'missing_mask': missing_array
            }

            return fig

        except Exception as e:
            print(f"Error creating performance heatmap: {str(e)}")
            import traceback
            traceback.print_exc()
            return self._create_error_plot("Performance Heatmap Error")

    def _normalize_data(self, data: np.ndarray, method: str = 'minmax', metrics: List[str] = None) -> np.ndarray:
        """
        Normalize data using specified method.

        Args:
            data: Data array to normalize
            method: 'minmax', 'zscore', or 'percentile'
            metrics: List of metric names to determine normalization direction
        """
        normalized = np.zeros_like(data, dtype=float)

        # Define metrics where lower is better
        lower_is_better = ['chamber_pressure', 'vacuum_chamber_pressure']

        for i in range(data.shape[1]):
            col = data[:, i].astype(float)

            # Check if this metric should be inverted
            invert = False
            if metrics and i < len(metrics):
                if metrics[i] in lower_is_better:
                    invert = True

            if method == 'minmax':
                col_max, col_min = np.max(col), np.min(col)
                if col_max > col_min:
                    if invert:
                        # For chamber pressure: lower values get higher scores
                        normalized[:, i] = (col_max - col) / (col_max - col_min)
                    else:
                        # Normal: higher values get higher scores
                        normalized[:, i] = (col - col_min) / (col_max - col_min)
                else:
                    # All values are the same
                    if len(col) > 1:
                        normalized[:, i] = np.linspace(0.3, 0.7, len(col))
                    else:
                        normalized[:, i] = 0.5

            elif method == 'zscore' and HAS_SCIPY:
                if np.std(col) > 0:
                    z_scores = stats.zscore(col)
                    if invert:
                        # Invert z-scores for metrics where lower is better
                        z_scores = -z_scores
                    # Convert to 0-1 range
                    normalized[:, i] = (z_scores - np.min(z_scores)) / \
                                     (np.max(z_scores) - np.min(z_scores))
                else:
                    normalized[:, i] = 0.5

            elif method == 'percentile' and HAS_SCIPY:
                from scipy.stats import rankdata
                ranks = rankdata(col)
                if invert:
                    # Invert ranks for metrics where lower is better
                    ranks = len(col) + 1 - ranks
                normalized[:, i] = (ranks - 1) / (len(col) - 1) if len(col) > 1 else [0.5]

            else:
                # Fallback to minmax
                col_max, col_min = np.max(col), np.min(col)
                if col_max > col_min:
                    if invert:
                        normalized[:, i] = (col_max - col) / (col_max - col_min)
                    else:
                        normalized[:, i] = (col - col_min) / (col_max - col_min)
                else:
                    normalized[:, i] = 0.5

        return normalized

    def _calculate_weighted_scores(self, normalized_data: np.ndarray, metrics: List[str]) -> np.ndarray:
        """
        Calculate overall performance scores using weighted normalized metrics.

        Since the data is already normalized (with chamber pressure inverted),
        we can directly use the normalized values where 1.0 = best, 0.0 = worst.
        """
        scores = []

        for row in normalized_data:
            weighted_sum = 0
            total_weight = 0

            for i, metric in enumerate(metrics):
                # Use the normalized value (already 0-1, with inversions applied)
                norm_value = row[i]

                # Skip missing data (would be 0 or very low)
                if not np.isnan(norm_value):
                    weight = self.metric_weights.get(metric, 1.0)
                    weighted_sum += norm_value * weight
                    total_weight += weight

            score = weighted_sum / total_weight if total_weight > 0 else 0
            scores.append(score)

        return np.array(scores)

    def _cluster_tests(self, data: np.ndarray) -> List[int]:
        """Cluster tests based on performance similarity."""
        try:
            if not HAS_SCIPY:
                return list(range(len(data)))

            # Calculate distance matrix
            linkage_matrix = linkage(data, method='ward')

            # Get cluster order
            dendro = dendrogram(linkage_matrix, no_plot=True)
            return dendro['leaves']

        except Exception as e:
            print(f"Error clustering tests: {str(e)}")
            return list(range(len(data)))

    def _add_performance_indicators(self, ax, data: np.ndarray, n_tests: int):
        """Add performance category indicators to the plot."""
        # Calculate average performance for each test
        avg_performance = np.mean(data[:, :-1] if data.shape[1] > len(data) else data, axis=1)

        # Add category indicators on the right side
        for i, avg in enumerate(avg_performance):
            category = self._get_performance_category(avg)
            color = self._get_category_color(category)

            # Add colored rectangle indicator
            rect = patches.Rectangle((data.shape[1]-0.45, i-0.45), 0.4, 0.9,
                                   facecolor=color, edgecolor='black',
                                   linewidth=1, alpha=0.7)
            ax.add_patch(rect)

            # Add category text
            ax.text(data.shape[1] - 0.4, i, category[:4], ha='left', va='center',
                   fontsize=8, fontweight='bold')

    def _get_performance_category(self, score: float) -> str:
        """Get performance category based on score."""
        for category, (min_val, max_val) in self.performance_categories.items():
            if min_val <= score < max_val:
                return category
        return 'Unknown'

    def _get_category_color(self, category: str) -> str:
        """Get color for performance category."""
        colors = {
            'Excellent': '#00ff00',
            'Good': '#90ee90',
            'Average': '#ffff00',
            'Below Average': '#ffa500',
            'Poor': '#ff0000',
            'Unknown': '#808080'
        }
        return colors.get(category, '#808080')

    def export_heatmap_data(self, filepath: str = 'heatmap_data.csv'):
        """Export the last heatmap data to CSV files."""
        if hasattr(self, 'last_heatmap_data'):
            data = self.last_heatmap_data

            # Export raw data
            df_raw = pd.DataFrame(data['raw_data'],
                                index=data['test_names'],
                                columns=data['metrics'])
            df_raw.to_csv(filepath.replace('.csv', '_raw.csv'))

            # Export normalized data
            df_norm = pd.DataFrame(data['normalized_data'],
                                 index=data['test_names'],
                                 columns=data['metrics'])
            df_norm.to_csv(filepath.replace('.csv', '_normalized.csv'))

            # Export missing data mask
            df_missing = pd.DataFrame(data['missing_mask'],
                                    index=data['test_names'],
                                    columns=data['metrics'][:-1] if 'overall_score' in data['metrics'] else data['metrics'])
            df_missing.to_csv(filepath.replace('.csv', '_missing.csv'))

            print(f"Heatmap data exported to {filepath}")
            return True
        else:
            print("No heatmap data available to export")
            return False

    def create_custom_scatter_plot(self, custom_data: Dict, test_names: List[str], x_label: str,
                                   y_label: str) -> plt.Figure:
        '''Create a custom scatter plot with user-selected X and Y parameters.'''
        try:
            fig, ax = plt.subplots(figsize=(12, 8))
            fig.patch.set_alpha(0.0)
            ax.patch.set_alpha(0.0)

            if not custom_data:
                ax.text(0.5, 0.5, 'No Data Available for Custom Plot',
                        ha='center', va='center', transform=ax.transAxes,
                        fontsize=14, style='italic')
                ax.set_title(f'{x_label} vs {y_label}', fontsize=16, fontweight='bold')
                return fig

            # Extract data for plotting
            x_values = []
            y_values = []
            labels = []
            valid_tests = []

            # Color palette for different tests
            colors = plt.cm.Set3(np.linspace(0, 1, len(test_names)))

            for i, test_name in enumerate(test_names):
                if test_name in custom_data:
                    data = custom_data[test_name]
                    x_values.append(data['x'])
                    y_values.append(data['y'])
                    labels.append(test_name)
                    valid_tests.append(test_name)

            if not x_values or not y_values:
                ax.text(0.5, 0.5, 'Insufficient Data for Custom Plot',
                        ha='center', va='center', transform=ax.transAxes,
                        fontsize=14, style='italic')
                ax.set_title(f'{x_label} vs {y_label}', fontsize=16, fontweight='bold')
                return fig

            # Create scatter plot
            scatter = ax.scatter(x_values, y_values, c=colors[:len(valid_tests)],
                                 s=100, alpha=0.7, edgecolors='black', linewidth=1.5)

            # Add data labels for each point
            for i, (x, y, label) in enumerate(zip(x_values, y_values, labels)):
                ax.annotate(label, (x, y), xytext=(5, 5), textcoords='offset points',
                            fontsize=9, ha='left', va='bottom',
                            bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))

            # Calculate and display correlation if we have enough points
            if len(x_values) >= 3:
                try:
                    correlation = np.corrcoef(x_values, y_values)[0, 1]

                    # Add trend line
                    z = np.polyfit(x_values, y_values, 1)
                    p = np.poly1d(z)
                    ax.plot(x_values, p(x_values), "r--", alpha=0.8, linewidth=2,
                            label=f'Trend Line (R={correlation:.3f})')

                    # Add correlation text
                    ax.text(0.05, 1.05, f'Correlation: {correlation:.3f}',
                            transform=ax.transAxes, fontsize=11,
                            bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.6),
                            verticalalignment='top')

                    # ax.legend(loc='upper right', alpha=0.7)

                except Exception as e:
                    print(f"Error calculating correlation: {str(e)}")

            # Formatting
            ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
            ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
            ax.set_title(f'{x_label} vs {y_label}\nCustom Performance Comparison',
                         fontsize=14, fontweight='bold', pad=20)

            # Grid and styling
            ax.grid(True, alpha=0.3, linestyle='--')
            ax.set_axisbelow(True)

            # Add some padding to the axis limits
            if len(x_values) > 1:
                x_range = max(x_values) - min(x_values)
                y_range = max(y_values) - min(y_values)

                if x_range > 0:
                    ax.set_xlim(min(x_values) - 0.1 * x_range, max(x_values) + 0.1 * x_range)
                if y_range > 0:
                    ax.set_ylim(min(y_values) - 0.1 * y_range, max(y_values) + 0.1 * y_range)

            # Add statistics box
            stats_text = f'Tests: {len(valid_tests)}\n'
            if x_values:
                stats_text += f'{x_label.split("(")[0].strip()}: {min(x_values):.2f} - {max(x_values):.2f}\n'
            if y_values:
                stats_text += f'{y_label.split("(")[0].strip()}: {min(y_values):.2f} - {max(y_values):.2f}'

            ax.text(0.95, 1.01, stats_text, transform=ax.transAxes, fontsize=9,
                    bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.6),
                    verticalalignment='bottom', horizontalalignment='right')

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating custom scatter plot: {str(e)}")
            return self._create_error_plot(f"Custom Plot Error: {x_label} vs {y_label}")

    def _create_error_plot(self, error_message: str) -> plt.Figure:
        '''Create a simple error plot when data is insufficient or invalid.'''
        fig, ax = plt.subplots(figsize=(10, 6))
        fig.patch.set_alpha(0.0)
        ax.patch.set_alpha(0.0)

        ax.text(0.5, 0.5, error_message, ha='center', va='center',
                transform=ax.transAxes, fontsize=14, style='italic',
                bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))

        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.set_xticks([])
        ax.set_yticks([])
        ax.set_title('Plot Generation Error', fontsize=16, fontweight='bold')

        return fig

    def create_heater_performance_comparison(self, heater_data: Dict, test_names: List[str]) -> plt.Figure:
        """Create bar plot comparing heater performance metrics."""
        try:
            fig, ax = plt.subplots(figsize=self.figure_size)

            metrics = ['total_heater_on_time', 'average_cycle_duration', 'heater_efficiency']
            metric_labels = ['Total On Time (s)', 'Avg Cycle Duration (s)']

            x = np.arange(len(test_names))
            width = 0.25

            for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
                values = []
                for test_name in test_names:
                    if test_name in heater_data and metric in heater_data[test_name]:
                        values.append(heater_data[test_name][metric])
                    else:
                        values.append(0)

                ax.bar(x + i*width, values, width, label=label, alpha=0.8)

            ax.set_xlabel('Tests')
            ax.set_ylabel('Time (s)')
            ax.set_title('Heater Performance Comparison', fontsize=14, fontweight='bold')
            ax.set_xticks(x + width)
            ax.set_xticklabels(test_names)
            ax.legend()
            ax.grid(True, alpha=0.3)

            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating heater performance bar plot: {str(e)}")
            return self._create_error_plot("Heater Performance Bar Plot Error")

    def create_performance_correlation_matrix(self, correlation_data: Dict, test_names: List[str]) -> plt.Figure:
        '''Create performance correlation matrix plot.'''
        try:
            fig, ax = plt.subplots(figsize=(10, 8))
            fig.patch.set_alpha(0.0)
            ax.patch.set_alpha(0.0)

            if not correlation_data:
                ax.text(0.5, 0.5, 'No Correlation Data Available', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title('Performance Correlations', fontsize=16, fontweight='bold')
                return fig

            # Extract all unique metrics
            all_metrics = set()
            for test_data in correlation_data.values():
                all_metrics.update(test_data.keys())

            metrics_list = sorted(list(all_metrics))

            if len(metrics_list) < 2:
                ax.text(0.5, 0.5, 'Need at least 2 metrics for correlation', ha='center', va='center',
                        transform=ax.transAxes, fontsize=14, style='italic')
                ax.set_title('Performance Correlations', fontsize=16, fontweight='bold')
                return fig

            # Create correlation matrix
            n_metrics = len(metrics_list)
            corr_matrix = np.zeros((n_metrics, n_metrics))

            for i, metric1 in enumerate(metrics_list):
                for j, metric2 in enumerate(metrics_list):
                    if i == j:
                        corr_matrix[i, j] = 1.0
                    else:
                        # Extract values for both metrics
                        values1 = []
                        values2 = []
                        for test_data in correlation_data.values():
                            if metric1 in test_data and metric2 in test_data:
                                values1.append(test_data[metric1])
                                values2.append(test_data[metric2])

                        if len(values1) >= 2:
                            try:
                                corr = np.corrcoef(values1, values2)[0, 1]
                                corr_matrix[i, j] = corr if not np.isnan(corr) else 0
                            except:
                                corr_matrix[i, j] = 0
                        else:
                            corr_matrix[i, j] = 0

            # Create heatmap
            im = ax.imshow(corr_matrix, cmap='RdYlBu_r', aspect='auto', vmin=-1, vmax=1)

            # Set ticks and labels
            ax.set_xticks(np.arange(n_metrics))
            ax.set_yticks(np.arange(n_metrics))
            ax.set_xticklabels([m.replace('_', ' ').title() for m in metrics_list], rotation=45, ha='right')
            ax.set_yticklabels([m.replace('_', ' ').title() for m in metrics_list])

            # Add correlation values as text
            for i in range(n_metrics):
                for j in range(n_metrics):
                    text = ax.text(j, i, f'{corr_matrix[i, j]:.2f}',
                                   ha="center", va="center", color="black" if abs(corr_matrix[i, j]) < 0.5 else "white",
                                   fontsize=8)

            # Add colorbar
            cbar = plt.colorbar(im, ax=ax)
            cbar.set_label('Correlation Coefficient', rotation=270, labelpad=20)

            ax.set_title('Performance Correlation Matrix', fontsize=16, fontweight='bold', pad=20)
            plt.tight_layout()
            return fig

        except Exception as e:
            print(f"Error creating correlation matrix: {str(e)}")
            return self._create_error_plot("Correlation Analysis Error")