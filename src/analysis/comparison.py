"""
Comparison Analysis Module
-------------------------
Advanced comparison capabilities for thruster test analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from scipy import stats


class ComparisonAnalyzer:
    """
    Advanced comparison capabilities for thruster test analysis.

    Features:
    - Multi-test comparison analysis
    - Statistical comparison methods
    - Performance benchmarking
    - Trend analysis
    - Optimization recommendations
    """

    def __init__(self):
        self.comparison_results = {}

    def compare_multiple_tests(self, test_data_list: List[Dict],
                              analysis_results_list: List[Dict]) -> Dict:
        """
        Comprehensive comparison of multiple tests.

        Args:
            test_data_list: List of test data dictionaries
            analysis_results_list: List of analysis results for each test

        Returns:
            Dictionary containing comparison analysis results
        """
        try:
            if len(test_data_list) < 2:
                return {'error': 'Need at least 2 tests for comparison'}

            results = {}

            # Basic comparison metrics
            results['performance_comparison'] = self._compare_performance_metrics(
                test_data_list, analysis_results_list)
            results['heater_comparison'] = self._compare_heater_performance(
                test_data_list, analysis_results_list)
            results['thermal_comparison'] = self._compare_thermal_characteristics(
                test_data_list, analysis_results_list)
            results['efficiency_comparison'] = self._compare_efficiency_metrics(
                test_data_list, analysis_results_list)

            # Statistical analysis
            results['statistical_analysis'] = self._perform_statistical_analysis(
                test_data_list, analysis_results_list)

            # Ranking and benchmarking
            results['performance_ranking'] = self._rank_test_performance(
                test_data_list, analysis_results_list)

            # Optimization insights
            results['optimization_insights'] = self._generate_optimization_insights(
                test_data_list, analysis_results_list)

            self.comparison_results = results
            return results

        except Exception as e:
            print(f"Error in multi-test comparison: {str(e)}")
            return {'error': str(e)}

    def _compare_performance_metrics(self, test_data_list: List[Dict],
                                   analysis_results_list: List[Dict]) -> Dict:
        """Compare basic performance metrics across tests."""
        try:
            performance_metrics = {}
            test_names = []

            # Extract performance data for each test
            for i, test_data in enumerate(test_data_list):
                test_no = test_data.get('basic_info', {}).get('test_no', f'Test_{i+1}')
                test_names.append(test_no)

                perf_data = test_data.get('performance_data', {})
                performance_metrics[test_no] = {
                    'thrust': perf_data.get('thrust', 0),
                    'specific_impulse': perf_data.get('specific_impulse', 0),
                    'total_impulse': perf_data.get('total_impulse', 0),
                    'burn_time': perf_data.get('burn_time', 0),
                    'mass_flow_rate': perf_data.get('mass_flow_rate', 0),
                    'chamber_pressure': perf_data.get('chamber_pressure', 0),
                    'thrust_coefficient': perf_data.get('thrust_coefficient', 0)
                }

            # Calculate comparison statistics
            comparison_stats = {}
            for metric in ['thrust', 'specific_impulse', 'total_impulse', 'burn_time',
                          'mass_flow_rate', 'chamber_pressure', 'thrust_coefficient']:
                values = [performance_metrics[test][metric] for test in test_names]

                comparison_stats[metric] = {
                    'values': dict(zip(test_names, values)),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': min(values),
                    'max': max(values),
                    'range': max(values) - min(values),
                    'coefficient_of_variation': (np.std(values) / np.mean(values) * 100) if np.mean(values) != 0 else 0,
                    'best_test': test_names[np.argmax(values)],
                    'worst_test': test_names[np.argmin(values)]
                }

            return {
                'individual_performance': performance_metrics,
                'comparison_statistics': comparison_stats,
                'performance_consistency': self._calculate_performance_consistency(comparison_stats)
            }

        except Exception as e:
            return {'error': str(e)}

    def _compare_heater_performance(self, test_data_list: List[Dict],
                                  analysis_results_list: List[Dict]) -> Dict:
        """Compare heater performance across tests."""
        try:
            heater_comparison = {}
            test_names = []

            for i, (test_data, analysis_results) in enumerate(zip(test_data_list, analysis_results_list)):
                test_no = test_data.get('basic_info', {}).get('test_no', f'Test_{i+1}')
                test_names.append(test_no)

                # Extract heater analysis results
                heater_analysis = analysis_results.get('heater_analysis', {})

                heater_comparison[test_no] = {
                    'total_heater_on_time': heater_analysis.get('total_heater_on_time', 0),
                    'average_cycle_duration': heater_analysis.get('average_cycle_duration', 0),
                    'first_cycle_duration': heater_analysis.get('first_cycle_duration', 0),
                    'total_energy_consumed': heater_analysis.get('total_energy_consumed', 0),
                    'average_cut_off_temperature': heater_analysis.get('average_cut_off_temperature', 0),
                    'thermal_efficiency': heater_analysis.get('thermal_efficiency', 0),
                    'consistency_score': heater_analysis.get('consistency_score', 0)
                }

            # Calculate heater comparison statistics
            heater_stats = {}
            for metric in heater_comparison[test_names[0]].keys():
                values = [heater_comparison[test][metric] for test in test_names]

                heater_stats[metric] = {
                    'values': dict(zip(test_names, values)),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'best_test': test_names[np.argmax(values)] if metric != 'total_energy_consumed' else test_names[np.argmin(values)],
                    'improvement_potential': (max(values) - min(values)) / max(values) * 100 if max(values) != 0 else 0
                }

            return {
                'individual_heater_performance': heater_comparison,
                'heater_statistics': heater_stats,
                'heater_efficiency_ranking': self._rank_heater_efficiency(heater_comparison)
            }

        except Exception as e:
            return {'error': str(e)}

    def _compare_thermal_characteristics(self, test_data_list: List[Dict],
                                       analysis_results_list: List[Dict]) -> Dict:
        """Compare thermal characteristics across tests."""
        try:
            thermal_comparison = {}
            test_names = []

            for i, (test_data, analysis_results) in enumerate(zip(test_data_list, analysis_results_list)):
                test_no = test_data.get('basic_info', {}).get('test_no', f'Test_{i+1}')
                test_names.append(test_no)

                # Extract thermal analysis results
                thermal_analysis = analysis_results.get('thermal_analysis', {})

                thermal_comparison[test_no] = {
                    'max_temperature_rise_rate': thermal_analysis.get('max_temperature_rise_rate', 0),
                    'temperature_stability_score': thermal_analysis.get('temperature_stability_score', 0),
                    'spatial_consistency_score': thermal_analysis.get('spatial_consistency_score', 0),
                    'average_thermal_time_constant': thermal_analysis.get('average_thermal_time_constant', 0),
                    'max_temperature_difference': thermal_analysis.get('max_temperature_difference', 0)
                }

            # Calculate thermal comparison statistics
            thermal_stats = {}
            for metric in thermal_comparison[test_names[0]].keys():
                values = [thermal_comparison[test][metric] for test in test_names]

                thermal_stats[metric] = {
                    'values': dict(zip(test_names, values)),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'best_test': self._determine_best_thermal_test(metric, test_names, values),
                    'consistency': (1 - np.std(values) / np.mean(values)) * 100 if np.mean(values) != 0 else 0
                }

            return {
                'individual_thermal_performance': thermal_comparison,
                'thermal_statistics': thermal_stats,
                'thermal_performance_ranking': self._rank_thermal_performance(thermal_comparison)
            }

        except Exception as e:
            return {'error': str(e)}

    def _compare_efficiency_metrics(self, test_data_list: List[Dict],
                                  analysis_results_list: List[Dict]) -> Dict:
        """Compare efficiency metrics across tests."""
        try:
            efficiency_comparison = {}
            test_names = []

            for i, (test_data, analysis_results) in enumerate(zip(test_data_list, analysis_results_list)):
                test_no = test_data.get('basic_info', {}).get('test_no', f'Test_{i+1}')
                test_names.append(test_no)

                # Extract efficiency analysis results
                efficiency_analysis = analysis_results.get('efficiency_analysis', {})

                efficiency_comparison[test_no] = {
                    'energy_utilization_score': efficiency_analysis.get('energy_utilization_score', 0),
                    'propellant_utilization': efficiency_analysis.get('propellant_utilization', 0),
                    'thermal_efficiency_score': efficiency_analysis.get('thermal_efficiency_score', 0),
                    'performance_index': efficiency_analysis.get('performance_index', 0),
                    'optimization_potential': efficiency_analysis.get('optimization_potential', 0)
                }

            # Calculate efficiency comparison statistics
            efficiency_stats = {}
            for metric in efficiency_comparison[test_names[0]].keys():
                values = [efficiency_comparison[test][metric] for test in test_names]

                efficiency_stats[metric] = {
                    'values': dict(zip(test_names, values)),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'best_test': test_names[np.argmax(values)] if metric != 'optimization_potential' else test_names[np.argmin(values)],
                    'improvement_range': max(values) - min(values)
                }

            return {
                'individual_efficiency_performance': efficiency_comparison,
                'efficiency_statistics': efficiency_stats,
                'overall_efficiency_ranking': self._rank_overall_efficiency(efficiency_comparison)
            }

        except Exception as e:
            return {'error': str(e)}

    def _perform_statistical_analysis(self, test_data_list: List[Dict],
                                    analysis_results_list: List[Dict]) -> Dict:
        """Perform statistical analysis on test comparisons."""
        try:
            statistical_results = {}

            # Extract key metrics for statistical analysis
            thrust_values = []
            isp_values = []
            efficiency_values = []

            for test_data in test_data_list:
                perf_data = test_data.get('performance_data', {})
                thrust_values.append(perf_data.get('thrust', 0))
                isp_values.append(perf_data.get('specific_impulse', 0))

            for analysis_results in analysis_results_list:
                eff_data = analysis_results.get('efficiency_analysis', {})
                efficiency_values.append(eff_data.get('performance_index', 0))

            # Statistical tests
            if len(thrust_values) >= 3:
                # Normality test
                thrust_normality = stats.shapiro(thrust_values) if len(thrust_values) <= 50 else stats.kstest(thrust_values, 'norm')
                isp_normality = stats.shapiro(isp_values) if len(isp_values) <= 50 else stats.kstest(isp_values, 'norm')

                statistical_results['normality_tests'] = {
                    'thrust_normal': thrust_normality.pvalue > 0.05,
                    'isp_normal': isp_normality.pvalue > 0.05
                }

                # Correlation analysis
                if len(thrust_values) == len(isp_values):
                    correlation = stats.pearsonr(thrust_values, isp_values)
                    statistical_results['thrust_isp_correlation'] = {
                        'correlation_coefficient': correlation[0],
                        'p_value': correlation[1],
                        'significant': correlation[1] < 0.05
                    }

            # Outlier detection
            statistical_results['outlier_analysis'] = self._detect_outliers(
                thrust_values, isp_values, efficiency_values)

            return statistical_results

        except Exception as e:
            return {'error': str(e)}

    def _rank_test_performance(self, test_data_list: List[Dict],
                             analysis_results_list: List[Dict]) -> Dict:
        """Rank tests by overall performance."""
        try:
            rankings = {}
            test_scores = {}

            for i, (test_data, analysis_results) in enumerate(zip(test_data_list, analysis_results_list)):
                test_no = test_data.get('basic_info', {}).get('test_no', f'Test_{i+1}')

                # Calculate composite score
                perf_data = test_data.get('performance_data', {})
                eff_data = analysis_results.get('efficiency_analysis', {})

                # Normalize and weight different metrics
                thrust_score = min(100, perf_data.get('thrust', 0) / 10)  # Scale appropriately
                isp_score = min(100, perf_data.get('specific_impulse', 0) / 2.5)
                efficiency_score = eff_data.get('performance_index', 0)

                composite_score = (thrust_score * 0.4 + isp_score * 0.3 + efficiency_score * 0.3)
                test_scores[test_no] = composite_score

            # Sort by score
            sorted_tests = sorted(test_scores.items(), key=lambda x: x[1], reverse=True)

            rankings['overall_ranking'] = [test for test, score in sorted_tests]
            rankings['scores'] = test_scores
            rankings['best_test'] = sorted_tests[0][0] if sorted_tests else None
            rankings['worst_test'] = sorted_tests[-1][0] if sorted_tests else None

            return rankings

        except Exception as e:
            return {'error': str(e)}

    def _generate_optimization_insights(self, test_data_list: List[Dict],
                                      analysis_results_list: List[Dict]) -> Dict:
        """Generate optimization insights from comparison."""
        try:
            insights = {}

            # Find best practices from top-performing tests
            performance_ranking = self._rank_test_performance(test_data_list, analysis_results_list)
            best_test = performance_ranking.get('best_test')

            if best_test:
                # Find the best test data
                best_test_data = None
                best_analysis = None

                for test_data, analysis_results in zip(test_data_list, analysis_results_list):
                    if test_data.get('basic_info', {}).get('test_no') == best_test:
                        best_test_data = test_data
                        best_analysis = analysis_results
                        break

                if best_test_data:
                    insights['best_practices'] = self._extract_best_practices(best_test_data, best_analysis)

            # Identify common improvement areas
            insights['improvement_opportunities'] = self._identify_improvement_opportunities(
                test_data_list, analysis_results_list)

            # Configuration recommendations
            insights['configuration_recommendations'] = self._generate_configuration_recommendations(
                test_data_list, analysis_results_list)

            return insights

        except Exception as e:
            return {'error': str(e)}

    def _calculate_performance_consistency(self, comparison_stats: Dict) -> Dict:
        """Calculate performance consistency metrics."""
        try:
            consistency_scores = {}

            for metric, stats in comparison_stats.items():
                cv = stats.get('coefficient_of_variation', 0)
                # Lower CV means higher consistency
                consistency_score = max(0, 100 - cv)
                consistency_scores[metric] = consistency_score

            overall_consistency = np.mean(list(consistency_scores.values()))

            return {
                'individual_consistency': consistency_scores,
                'overall_consistency': overall_consistency,
                'most_consistent_metric': max(consistency_scores.keys(), key=consistency_scores.get),
                'least_consistent_metric': min(consistency_scores.keys(), key=consistency_scores.get)
            }

        except Exception:
            return {'error': 'Unable to calculate consistency'}

    def _rank_heater_efficiency(self, heater_comparison: Dict) -> Dict:
        """Rank tests by heater efficiency."""
        try:
            efficiency_scores = {}

            for test_name, metrics in heater_comparison.items():
                # Calculate composite heater efficiency score
                thermal_eff = metrics.get('thermal_efficiency', 0)
                consistency = metrics.get('consistency_score', 0)
                energy_eff = 100 - (metrics.get('total_energy_consumed', 1) * 10)  # Lower energy is better

                composite_score = (thermal_eff * 0.4 + consistency * 0.3 + max(0, energy_eff) * 0.3)
                efficiency_scores[test_name] = composite_score

            sorted_tests = sorted(efficiency_scores.items(), key=lambda x: x[1], reverse=True)

            return {
                'heater_efficiency_ranking': [test for test, score in sorted_tests],
                'efficiency_scores': efficiency_scores,
                'most_efficient_heater': sorted_tests[0][0] if sorted_tests else None
            }

        except Exception:
            return {'error': 'Unable to rank heater efficiency'}

    def _determine_best_thermal_test(self, metric: str, test_names: List[str], values: List[float]) -> str:
        """Determine which test is best for a given thermal metric."""
        try:
            if not values:
                return 'Unknown'

            # For some metrics, higher is better; for others, lower is better
            higher_is_better = ['temperature_stability_score', 'spatial_consistency_score']
            lower_is_better = ['max_temperature_difference', 'average_thermal_time_constant']

            if metric in higher_is_better:
                return test_names[np.argmax(values)]
            elif metric in lower_is_better:
                return test_names[np.argmin(values)]
            else:
                # Default to higher is better
                return test_names[np.argmax(values)]

        except Exception:
            return 'Unknown'

    def _rank_thermal_performance(self, thermal_comparison: Dict) -> Dict:
        """Rank tests by thermal performance."""
        try:
            thermal_scores = {}

            for test_name, metrics in thermal_comparison.items():
                # Calculate composite thermal performance score
                stability = metrics.get('temperature_stability_score', 0)
                consistency = metrics.get('spatial_consistency_score', 0)
                rise_rate = min(100, metrics.get('max_temperature_rise_rate', 0) * 10)  # Scale appropriately

                # Lower time constant is better (faster response)
                time_constant = metrics.get('average_thermal_time_constant', 1)
                response_score = min(100, 1 / time_constant * 100) if time_constant > 0 else 0

                composite_score = (stability * 0.3 + consistency * 0.3 + rise_rate * 0.2 + response_score * 0.2)
                thermal_scores[test_name] = composite_score

            sorted_tests = sorted(thermal_scores.items(), key=lambda x: x[1], reverse=True)

            return {
                'thermal_ranking': [test for test, score in sorted_tests],
                'thermal_scores': thermal_scores,
                'best_thermal_performance': sorted_tests[0][0] if sorted_tests else None
            }

        except Exception:
            return {'error': 'Unable to rank thermal performance'}

    def _rank_overall_efficiency(self, efficiency_comparison: Dict) -> Dict:
        """Rank tests by overall efficiency."""
        try:
            overall_scores = {}

            for test_name, metrics in efficiency_comparison.items():
                # Calculate weighted efficiency score
                energy_score = metrics.get('energy_utilization_score', 0)
                propellant_score = metrics.get('propellant_utilization', 0)
                thermal_score = metrics.get('thermal_efficiency_score', 0)
                performance_score = metrics.get('performance_index', 0)

                # Lower optimization potential is better (less room for improvement needed)
                opt_potential = metrics.get('optimization_potential', 100)
                optimization_score = max(0, 100 - opt_potential)

                composite_score = (energy_score * 0.25 + propellant_score * 0.25 +
                                 thermal_score * 0.2 + performance_score * 0.2 + optimization_score * 0.1)
                overall_scores[test_name] = composite_score

            sorted_tests = sorted(overall_scores.items(), key=lambda x: x[1], reverse=True)

            return {
                'efficiency_ranking': [test for test, score in sorted_tests],
                'efficiency_scores': overall_scores,
                'most_efficient_test': sorted_tests[0][0] if sorted_tests else None
            }

        except Exception:
            return {'error': 'Unable to rank overall efficiency'}

    def _detect_outliers(self, thrust_values: List[float], isp_values: List[float],
                        efficiency_values: List[float]) -> Dict:
        """Detect outliers in test data."""
        try:
            outliers = {}

            # Use IQR method for outlier detection
            def find_outliers_iqr(values, name):
                if len(values) < 4:
                    return []

                q1 = np.percentile(values, 25)
                q3 = np.percentile(values, 75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr

                outlier_indices = []
                for i, value in enumerate(values):
                    if value < lower_bound or value > upper_bound:
                        outlier_indices.append(i)

                return outlier_indices

            outliers['thrust_outliers'] = find_outliers_iqr(thrust_values, 'thrust')
            outliers['isp_outliers'] = find_outliers_iqr(isp_values, 'isp')
            outliers['efficiency_outliers'] = find_outliers_iqr(efficiency_values, 'efficiency')

            # Overall outlier assessment
            all_outliers = set(outliers['thrust_outliers'] + outliers['isp_outliers'] + outliers['efficiency_outliers'])
            outliers['tests_with_outliers'] = list(all_outliers)
            outliers['outlier_count'] = len(all_outliers)

            return outliers

        except Exception:
            return {'error': 'Unable to detect outliers'}

    def _extract_best_practices(self, best_test_data: Dict, best_analysis: Dict) -> Dict:
        """Extract best practices from the top-performing test."""
        try:
            best_practices = {}

            # System configuration
            system_specs = best_test_data.get('system_specs', {})
            best_practices['optimal_system_config'] = {
                'chamber_depth': system_specs.get('chamber depth', 'Unknown'),
                'chamber_diameter': system_specs.get('chamber diameter', 'Unknown'),
                'nozzle_throat': system_specs.get('chamber nozzle_throat', 'Unknown')
            }

            # Heater configuration
            heater_info = best_test_data.get('heater_info', {})
            best_practices['optimal_heater_config'] = {
                'heater_type': heater_info.get('heater_type', 'Unknown'),
                'cut_off_temp': heater_info.get('heater_cut_off_temp', 'Unknown'),
                'reset_temp': heater_info.get('heater_rst_temp', 'Unknown')
            }

            # Propellant configuration
            propellant_specs = best_test_data.get('propellant_specs', {})
            best_practices['optimal_propellant_config'] = {
                'propellant_type': propellant_specs.get('type', 'Unknown'),
                'initial_mass': propellant_specs.get('initial_mass', 'Unknown')
            }

            # Performance characteristics
            performance_data = best_test_data.get('performance_data', {})
            best_practices['target_performance'] = {
                'thrust': performance_data.get('thrust', 0),
                'specific_impulse': performance_data.get('specific_impulse', 0),
                'chamber_pressure': performance_data.get('chamber_pressure', 0)
            }

            return best_practices

        except Exception:
            return {'error': 'Unable to extract best practices'}

    def _identify_improvement_opportunities(self, test_data_list: List[Dict],
                                          analysis_results_list: List[Dict]) -> List[str]:
        """Identify common improvement opportunities across tests."""
        try:
            opportunities = []

            # Analyze efficiency scores across all tests
            efficiency_scores = []
            heater_scores = []
            thermal_scores = []

            for analysis_results in analysis_results_list:
                eff_data = analysis_results.get('efficiency_analysis', {})
                efficiency_scores.append(eff_data.get('performance_index', 0))

                heater_data = analysis_results.get('heater_analysis', {})
                heater_scores.append(heater_data.get('thermal_efficiency', 0))

                thermal_data = analysis_results.get('thermal_analysis', {})
                thermal_scores.append(thermal_data.get('temperature_stability_score', 0))

            # Identify areas with consistently low scores
            if efficiency_scores and np.mean(efficiency_scores) < 70:
                opportunities.append("Overall system efficiency is below optimal - consider system redesign")

            if heater_scores and np.mean(heater_scores) < 60:
                opportunities.append("Heater efficiency is consistently low - optimize heater control strategy")

            if thermal_scores and np.mean(thermal_scores) < 75:
                opportunities.append("Thermal stability needs improvement - review thermal management")

            # Check for high variability (inconsistency)
            if efficiency_scores and np.mean(efficiency_scores) != 0:
                cv = np.std(efficiency_scores) / np.mean(efficiency_scores)
                if cv > 0.2:
                    opportunities.append("High variability in performance - improve test consistency")

            if not opportunities:
                opportunities.append("System performance is generally good - focus on fine-tuning")

            return opportunities

        except Exception:
            return ["Unable to identify improvement opportunities"]

    def _generate_configuration_recommendations(self, test_data_list: List[Dict],
                                              analysis_results_list: List[Dict]) -> List[str]:
        """Generate configuration recommendations based on comparison."""
        try:
            recommendations = []

            # Analyze correlations between configuration and performance
            thrust_values = []
            chamber_pressures = []
            heater_powers = []

            for test_data in test_data_list:
                perf_data = test_data.get('performance_data', {})
                thrust_values.append(perf_data.get('thrust', 0))
                chamber_pressures.append(perf_data.get('chamber_pressure', 0))

                heater_info = test_data.get('heater_info', {})
                heater_power = self._extract_numeric_value(heater_info.get('heater_input_Wattage', '0'))
                heater_powers.append(heater_power)

            # Generate recommendations based on patterns
            if len(thrust_values) >= 3:
                max_thrust_idx = np.argmax(thrust_values)

                if chamber_pressures[max_thrust_idx] > np.mean(chamber_pressures):
                    recommendations.append("Higher chamber pressures correlate with better thrust performance")

                if heater_powers[max_thrust_idx] > np.mean(heater_powers):
                    recommendations.append("Higher heater power may improve performance")

            # Add general recommendations
            recommendations.append("Maintain consistent test procedures for reliable comparisons")
            recommendations.append("Monitor thermal stability for optimal performance")

            return recommendations

        except Exception:
            return ["Unable to generate configuration recommendations"]

    def _extract_numeric_value(self, value_str: str) -> float:
        """Extract numeric value from string."""
        try:
            if isinstance(value_str, (int, float)):
                return float(value_str)

            if isinstance(value_str, str):
                import re
                numbers = re.findall(r'\d+\.?\d*', value_str.replace(',', '.'))
                if numbers:
                    return float(numbers[0])

            return 0.0

        except Exception:
            return 0.0

    def get_comparison_summary(self) -> Dict:
        """Get summary of comparison analysis."""
        if not self.comparison_results:
            return {'error': 'No comparison results available'}

        try:
            summary = {}

            # Performance comparison summary
            if 'performance_comparison' in self.comparison_results:
                pc = self.comparison_results['performance_comparison']
                summary['best_performing_test'] = pc.get('comparison_statistics', {}).get('thrust', {}).get('best_test', 'Unknown')
                summary['performance_consistency'] = pc.get('performance_consistency', {}).get('overall_consistency', 0)

            # Efficiency comparison summary
            if 'efficiency_comparison' in self.comparison_results:
                ec = self.comparison_results['efficiency_comparison']
                summary['most_efficient_test'] = ec.get('overall_efficiency_ranking', {}).get('most_efficient_test', 'Unknown')

            # Optimization insights
            if 'optimization_insights' in self.comparison_results:
                oi = self.comparison_results['optimization_insights']
                summary['improvement_opportunities'] = oi.get('improvement_opportunities', [])
                summary['configuration_recommendations'] = oi.get('configuration_recommendations', [])

            # Statistical insights
            if 'statistical_analysis' in self.comparison_results:
                sa = self.comparison_results['statistical_analysis']
                summary['outlier_count'] = sa.get('outlier_analysis', {}).get('outlier_count', 0)

            return summary

        except Exception as e:
            return {'error': str(e)}