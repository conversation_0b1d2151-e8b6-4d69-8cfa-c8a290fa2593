"""
Efficiency Analysis Module
-------------------------
System efficiency metrics and optimization analysis for thruster tests.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple


class EfficiencyAnalyzer:
    """
    System efficiency metrics and optimization analysis.
    
    Features:
    - Energy efficiency calculations
    - Propellant utilization efficiency
    - Performance per unit energy/mass
    - Cost-benefit analysis
    - Optimization recommendations
    """
    
    def __init__(self):
        self.analysis_results = {}
        
    def analyze_system_efficiency(self, test_data: Dict, performance_data: Dict,
                                 heater_analysis: Optional[Dict] = None,
                                 thermal_analysis: Optional[Dict] = None) -> Dict:
        """
        Comprehensive system efficiency analysis.
        
        Args:
            test_data: Complete test data including specifications
            performance_data: Performance metrics (thrust, ISP, etc.)
            heater_analysis: Optional heater analysis results
            thermal_analysis: Optional thermal analysis results
            
        Returns:
            Dictionary containing efficiency analysis results
        """
        try:
            results = {}
            
            # Core efficiency metrics
            results['energy_efficiency'] = self._calculate_energy_efficiency(
                test_data, performance_data, heater_analysis)
            results['propellant_efficiency'] = self._calculate_propellant_efficiency(
                test_data, performance_data)
            results['thermal_efficiency'] = self._calculate_thermal_efficiency(
                test_data, thermal_analysis)
            results['cost_efficiency'] = self._calculate_cost_efficiency(
                test_data, performance_data, heater_analysis)
            results['performance_ratios'] = self._calculate_performance_ratios(
                test_data, performance_data)
            results['optimization_metrics'] = self._calculate_optimization_metrics(
                test_data, performance_data, heater_analysis)
            
            self.analysis_results = results
            return results
            
        except Exception as e:
            print(f"Error in efficiency analysis: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_energy_efficiency(self, test_data: Dict, performance_data: Dict,
                                   heater_analysis: Optional[Dict] = None) -> Dict:
        """Calculate energy efficiency metrics."""
        try:
            efficiency_metrics = {}
            
            # Extract performance data
            thrust = performance_data.get('thrust', 0)  # mN
            burn_time = performance_data.get('burn_time', 0)  # s
            total_impulse = performance_data.get('total_impulse', 0)  # Ns
            
            # Extract heater energy consumption
            if heater_analysis and 'power_consumption' in heater_analysis:
                heater_energy = heater_analysis['power_consumption'].get('total_energy_consumption', 0)  # kWh
                heater_power = heater_analysis['power_consumption'].get('heater_power_rating', 0)  # W
            else:
                # Estimate from heater info if available
                heater_info = test_data.get('heater_info', {})
                heater_power = self._extract_power_rating(heater_info)
                heater_energy = 0
            
            # Calculate energy efficiency metrics
            if heater_energy > 0:
                # Energy per unit thrust
                energy_per_thrust = heater_energy / thrust if thrust > 0 else 0  # kWh/mN
                
                # Energy per unit impulse
                energy_per_impulse = heater_energy / total_impulse if total_impulse > 0 else 0  # kWh/Ns
                
                # Power efficiency during operation
                power_efficiency = thrust / (heater_power / 1000) if heater_power > 0 else 0  # mN/kW
                
                efficiency_metrics = {
                    'total_energy_consumed': heater_energy,
                    'energy_per_thrust': energy_per_thrust,
                    'energy_per_impulse': energy_per_impulse,
                    'power_efficiency': power_efficiency,
                    'energy_utilization_score': self._calculate_energy_utilization_score(
                        heater_energy, thrust, burn_time)
                }
            else:
                efficiency_metrics = {'error': 'No energy consumption data available'}
            
            return efficiency_metrics
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_propellant_efficiency(self, test_data: Dict, performance_data: Dict) -> Dict:
        """Calculate propellant utilization efficiency."""
        try:
            # Extract propellant data
            propellant_specs = test_data.get('propellant_specs', {})
            initial_mass = self._extract_numeric_value(propellant_specs.get('initial_mass', '0'))
            unused_mass = self._extract_numeric_value(propellant_specs.get('unused_mass', '0'))
            
            # Extract performance data
            thrust = performance_data.get('thrust', 0)
            mass_flow_rate = performance_data.get('mass_flow_rate', 0)
            specific_impulse = performance_data.get('specific_impulse', 0)
            burn_time = performance_data.get('burn_time', 0)
            
            if initial_mass <= 0:
                return {'error': 'No valid propellant mass data'}
            
            # Calculate propellant efficiency metrics
            consumed_mass = initial_mass - unused_mass
            propellant_utilization = (consumed_mass / initial_mass) * 100
            
            # Thrust per unit propellant
            thrust_per_gram = thrust / consumed_mass if consumed_mass > 0 else 0
            
            # Impulse per unit propellant
            total_impulse = performance_data.get('total_impulse', 0)
            impulse_per_gram = total_impulse / consumed_mass if consumed_mass > 0 else 0
            
            # Propellant consumption rate efficiency
            theoretical_consumption = mass_flow_rate * burn_time / 1000  # Convert mg to g
            consumption_efficiency = (theoretical_consumption / consumed_mass * 100) if consumed_mass > 0 else 0
            
            return {
                'initial_propellant_mass': initial_mass,
                'consumed_propellant_mass': consumed_mass,
                'propellant_utilization_percentage': propellant_utilization,
                'thrust_per_gram_propellant': thrust_per_gram,
                'impulse_per_gram_propellant': impulse_per_gram,
                'consumption_efficiency': consumption_efficiency,
                'specific_impulse': specific_impulse,
                'propellant_efficiency_score': self._calculate_propellant_efficiency_score(
                    propellant_utilization, thrust_per_gram, specific_impulse)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_thermal_efficiency(self, test_data: Dict, 
                                    thermal_analysis: Optional[Dict] = None) -> Dict:
        """Calculate thermal efficiency metrics."""
        try:
            if not thermal_analysis:
                return {'error': 'No thermal analysis data available'}
            
            thermal_metrics = {}
            
            # Extract thermal performance data
            if 'rise_rate_analysis' in thermal_analysis:
                rra = thermal_analysis['rise_rate_analysis']
                max_rise_rate = rra.get('overall_max_rise_rate', 0)
                avg_rise_rate = rra.get('overall_avg_rise_rate', 0)
                
                thermal_metrics['heating_efficiency'] = max_rise_rate
                thermal_metrics['average_heating_rate'] = avg_rise_rate
            
            if 'stability_analysis' in thermal_analysis:
                sa = thermal_analysis['stability_analysis']
                stability_score = sa.get('overall_stability_score', 0)
                thermal_metrics['thermal_stability_efficiency'] = stability_score
            
            if 'spatial_distribution' in thermal_analysis:
                sd = thermal_analysis['spatial_distribution']
                uniformity = sd.get('average_uniformity_index', 0)
                thermal_metrics['thermal_uniformity_efficiency'] = uniformity * 100
            
            if 'heat_transfer_metrics' in thermal_analysis:
                htm = thermal_analysis['heat_transfer_metrics']
                avg_time_constant = htm.get('average_time_constant', 0)
                thermal_metrics['heat_transfer_efficiency'] = 1 / avg_time_constant if avg_time_constant > 0 else 0
            
            # Overall thermal efficiency score
            thermal_metrics['overall_thermal_efficiency'] = self._calculate_overall_thermal_efficiency(
                thermal_metrics)
            
            return thermal_metrics
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_cost_efficiency(self, test_data: Dict, performance_data: Dict,
                                  heater_analysis: Optional[Dict] = None) -> Dict:
        """Calculate cost efficiency metrics."""
        try:
            cost_metrics = {}
            
            # Extract cost-related data
            thrust = performance_data.get('thrust', 0)
            total_impulse = performance_data.get('total_impulse', 0)
            burn_time = performance_data.get('burn_time', 0)
            
            # Energy costs
            if heater_analysis and 'power_consumption' in heater_analysis:
                energy_cost = heater_analysis['power_consumption'].get('energy_cost_estimate', 0)
                total_energy = heater_analysis['power_consumption'].get('total_energy_consumption', 0)
            else:
                energy_cost = 0
                total_energy = 0
            
            # Propellant costs (estimated)
            propellant_specs = test_data.get('propellant_specs', {})
            consumed_mass = self._extract_numeric_value(propellant_specs.get('initial_mass', '0')) - \
                           self._extract_numeric_value(propellant_specs.get('unused_mass', '0'))
            
            # Estimate propellant cost (assuming $10/g for specialty propellants)
            propellant_cost = consumed_mass * 10 if consumed_mass > 0 else 0
            
            total_operational_cost = energy_cost + propellant_cost
            
            # Cost efficiency metrics
            if total_operational_cost > 0:
                cost_per_thrust = total_operational_cost / thrust if thrust > 0 else 0
                cost_per_impulse = total_operational_cost / total_impulse if total_impulse > 0 else 0
                cost_per_second = total_operational_cost / burn_time if burn_time > 0 else 0
                
                cost_metrics = {
                    'total_operational_cost': total_operational_cost,
                    'energy_cost': energy_cost,
                    'propellant_cost': propellant_cost,
                    'cost_per_thrust_mN': cost_per_thrust,
                    'cost_per_impulse_Ns': cost_per_impulse,
                    'cost_per_second_operation': cost_per_second,
                    'cost_efficiency_score': self._calculate_cost_efficiency_score(
                        cost_per_thrust, cost_per_impulse)
                }
            else:
                cost_metrics = {'error': 'Insufficient cost data'}
            
            return cost_metrics
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_performance_ratios(self, test_data: Dict, performance_data: Dict) -> Dict:
        """Calculate various performance ratios."""
        try:
            ratios = {}
            
            # Extract data
            thrust = performance_data.get('thrust', 0)
            specific_impulse = performance_data.get('specific_impulse', 0)
            mass_flow_rate = performance_data.get('mass_flow_rate', 0)
            chamber_pressure = performance_data.get('chamber_pressure', 0)
            
            # System specifications
            system_specs = test_data.get('system_specs', {})
            chamber_volume = self._calculate_chamber_volume(system_specs)
            
            # Performance ratios
            if chamber_volume > 0:
                ratios['thrust_per_chamber_volume'] = thrust / chamber_volume
            
            if chamber_pressure > 0:
                ratios['thrust_per_pressure'] = thrust / chamber_pressure
                ratios['specific_impulse_per_pressure'] = specific_impulse / chamber_pressure
            
            if mass_flow_rate > 0:
                ratios['thrust_to_flow_ratio'] = thrust / mass_flow_rate
            
            # Catalyst efficiency ratios
            catalyst_specs = test_data.get('catalyst_specs', {})
            catalyst_mass = self._extract_numeric_value(catalyst_specs.get('weight_before', '0'))
            
            if catalyst_mass > 0:
                ratios['thrust_per_catalyst_mass'] = thrust / catalyst_mass
                ratios['impulse_per_catalyst_mass'] = performance_data.get('total_impulse', 0) / catalyst_mass
            
            # Overall performance index
            ratios['performance_index'] = self._calculate_performance_index(performance_data)
            
            return ratios
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_optimization_metrics(self, test_data: Dict, performance_data: Dict,
                                      heater_analysis: Optional[Dict] = None) -> Dict:
        """Calculate optimization potential metrics."""
        try:
            optimization = {}
            
            # Identify optimization opportunities
            thrust = performance_data.get('thrust', 0)
            specific_impulse = performance_data.get('specific_impulse', 0)
            
            # Heater optimization potential
            if heater_analysis:
                heater_efficiency = heater_analysis.get('efficiency_metrics', {}).get('thermal_efficiency', 0)
                optimization['heater_optimization_potential'] = max(0, 100 - heater_efficiency)
            
            # Propellant optimization
            propellant_specs = test_data.get('propellant_specs', {})
            initial_mass = self._extract_numeric_value(propellant_specs.get('initial_mass', '0'))
            unused_mass = self._extract_numeric_value(propellant_specs.get('unused_mass', '0'))
            
            if initial_mass > 0:
                utilization = ((initial_mass - unused_mass) / initial_mass) * 100
                optimization['propellant_optimization_potential'] = max(0, 100 - utilization)
            
            # Performance benchmarking (against theoretical maximums)
            theoretical_max_isp = 250  # Typical for monopropellant systems
            theoretical_max_thrust = thrust * 1.5  # Assume 50% improvement potential
            
            optimization['isp_improvement_potential'] = max(0, 
                ((theoretical_max_isp - specific_impulse) / theoretical_max_isp) * 100)
            optimization['thrust_improvement_potential'] = max(0,
                ((theoretical_max_thrust - thrust) / theoretical_max_thrust) * 100)
            
            # Overall optimization score
            optimization['overall_optimization_potential'] = self._calculate_overall_optimization_potential(
                optimization)
            
            # Recommendations
            optimization['recommendations'] = self._generate_optimization_recommendations(
                test_data, performance_data, heater_analysis, optimization)
            
            return optimization
            
        except Exception as e:
            return {'error': str(e)}
    
    def _extract_power_rating(self, heater_info: Dict) -> float:
        """Extract power rating from heater info."""
        try:
            power_keys = ['Heater_input_Wattage', 'heater_input_power', 'wattage', 'power']
            
            for key in power_keys:
                if key in heater_info:
                    return self._extract_numeric_value(heater_info[key])
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def _extract_numeric_value(self, value_str: str) -> float:
        """Extract numeric value from string."""
        try:
            if isinstance(value_str, (int, float)):
                return float(value_str)
            
            if isinstance(value_str, str):
                # Remove units and extract number
                import re
                numbers = re.findall(r'\d+\.?\d*', value_str.replace(',', '.'))
                if numbers:
                    return float(numbers[0])
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_chamber_volume(self, system_specs: Dict) -> float:
        """Calculate chamber volume from specifications."""
        try:
            depth = self._extract_numeric_value(system_specs.get('chamber depth', '0'))
            diameter = self._extract_numeric_value(system_specs.get('chamber diameter', '0'))
            
            if depth > 0 and diameter > 0:
                radius = diameter / 2
                volume = np.pi * radius**2 * depth  # mm³
                return volume / 1000  # Convert to cm³
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_energy_utilization_score(self, energy: float, thrust: float, burn_time: float) -> float:
        """Calculate energy utilization efficiency score."""
        try:
            if energy <= 0 or thrust <= 0 or burn_time <= 0:
                return 0.0
            
            # Normalize based on typical values
            energy_per_thrust_per_time = energy / (thrust * burn_time)
            
            # Lower is better, so invert and scale
            if energy_per_thrust_per_time > 0:
                return min(100, 1 / energy_per_thrust_per_time * 1000)
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_propellant_efficiency_score(self, utilization: float, thrust_per_gram: float, 
                                             specific_impulse: float) -> float:
        """Calculate overall propellant efficiency score."""
        try:
            # Weighted combination of metrics
            utilization_score = min(100, utilization)
            thrust_score = min(100, thrust_per_gram * 10)  # Scale appropriately
            isp_score = min(100, specific_impulse / 2.5)  # Scale for typical ISP values
            
            # Weighted average
            overall_score = (utilization_score * 0.4 + thrust_score * 0.3 + isp_score * 0.3)
            return overall_score
            
        except Exception:
            return 0.0
    
    def _calculate_overall_thermal_efficiency(self, thermal_metrics: Dict) -> float:
        """Calculate overall thermal efficiency score."""
        try:
            scores = []
            
            if 'thermal_stability_efficiency' in thermal_metrics:
                scores.append(thermal_metrics['thermal_stability_efficiency'])
            
            if 'thermal_uniformity_efficiency' in thermal_metrics:
                scores.append(thermal_metrics['thermal_uniformity_efficiency'])
            
            if 'heat_transfer_efficiency' in thermal_metrics:
                # Scale heat transfer efficiency
                hte = min(100, thermal_metrics['heat_transfer_efficiency'] * 100)
                scores.append(hte)
            
            return np.mean(scores) if scores else 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_cost_efficiency_score(self, cost_per_thrust: float, cost_per_impulse: float) -> float:
        """Calculate cost efficiency score."""
        try:
            # Lower costs are better, so invert the scores
            if cost_per_thrust > 0 and cost_per_impulse > 0:
                thrust_score = min(100, 1 / cost_per_thrust * 100)
                impulse_score = min(100, 1 / cost_per_impulse * 1000)
                return (thrust_score + impulse_score) / 2
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_performance_index(self, performance_data: Dict) -> float:
        """Calculate overall performance index."""
        try:
            thrust = performance_data.get('thrust', 0)
            specific_impulse = performance_data.get('specific_impulse', 0)
            total_impulse = performance_data.get('total_impulse', 0)
            
            # Normalize and combine metrics
            thrust_score = min(100, thrust / 10)  # Scale for typical thrust values
            isp_score = min(100, specific_impulse / 2.5)  # Scale for typical ISP values
            impulse_score = min(100, total_impulse * 1000)  # Scale for typical total impulse
            
            return (thrust_score * 0.4 + isp_score * 0.4 + impulse_score * 0.2)
            
        except Exception:
            return 0.0
    
    def _calculate_overall_optimization_potential(self, optimization: Dict) -> float:
        """Calculate overall optimization potential."""
        try:
            potentials = []
            
            for key, value in optimization.items():
                if 'potential' in key and isinstance(value, (int, float)):
                    potentials.append(value)
            
            return np.mean(potentials) if potentials else 0.0
            
        except Exception:
            return 0.0
    
    def _generate_optimization_recommendations(self, test_data: Dict, performance_data: Dict,
                                             heater_analysis: Optional[Dict], 
                                             optimization: Dict) -> List[str]:
        """Generate optimization recommendations."""
        try:
            recommendations = []
            
            # Heater optimization
            if optimization.get('heater_optimization_potential', 0) > 20:
                recommendations.append("Consider optimizing heater control strategy for better thermal efficiency")
            
            # Propellant optimization
            if optimization.get('propellant_optimization_potential', 0) > 10:
                recommendations.append("Improve propellant utilization by optimizing injection or catalyst bed design")
            
            # Performance optimization
            if optimization.get('isp_improvement_potential', 0) > 15:
                recommendations.append("Investigate nozzle design optimization for improved specific impulse")
            
            if optimization.get('thrust_improvement_potential', 0) > 20:
                recommendations.append("Consider chamber pressure optimization for increased thrust")
            
            # System-specific recommendations
            chamber_pressure = performance_data.get('chamber_pressure', 0)
            if chamber_pressure < 1000:  # mbar
                recommendations.append("Consider increasing chamber pressure for better performance")
            
            # Add general recommendations if no specific issues found
            if not recommendations:
                recommendations.append("System performance is well-optimized. Focus on consistency and reliability.")
            
            return recommendations
            
        except Exception:
            return ["Unable to generate recommendations due to insufficient data"]
    
    def get_efficiency_summary(self) -> Dict:
        """Get summary of key efficiency metrics."""
        if not self.analysis_results:
            return {'error': 'No analysis results available'}
        
        try:
            summary = {}
            
            # Extract key efficiency metrics
            if 'energy_efficiency' in self.analysis_results:
                ee = self.analysis_results['energy_efficiency']
                summary['energy_utilization_score'] = ee.get('energy_utilization_score', 0)
                summary['power_efficiency'] = ee.get('power_efficiency', 0)
            
            if 'propellant_efficiency' in self.analysis_results:
                pe = self.analysis_results['propellant_efficiency']
                summary['propellant_utilization'] = pe.get('propellant_utilization_percentage', 0)
                summary['propellant_efficiency_score'] = pe.get('propellant_efficiency_score', 0)
            
            if 'thermal_efficiency' in self.analysis_results:
                te = self.analysis_results['thermal_efficiency']
                summary['thermal_efficiency_score'] = te.get('overall_thermal_efficiency', 0)
            
            if 'performance_ratios' in self.analysis_results:
                pr = self.analysis_results['performance_ratios']
                summary['performance_index'] = pr.get('performance_index', 0)
            
            if 'optimization_metrics' in self.analysis_results:
                om = self.analysis_results['optimization_metrics']
                summary['optimization_potential'] = om.get('overall_optimization_potential', 0)
                summary['recommendations'] = om.get('recommendations', [])
            
            return summary
            
        except Exception as e:
            return {'error': str(e)}
