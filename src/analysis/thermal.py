"""
Thermal Analysis Module
----------------------
Enhanced thermal response analysis for thruster tests.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from scipy import stats
from scipy.signal import find_peaks


class ThermalAnalyzer:
    """
    Enhanced thermal response analysis for thruster tests.
    
    Features:
    - Temperature rise rate analysis
    - Temperature stability analysis
    - Spatial temperature distribution
    - Thermal gradient analysis
    - Heat transfer characteristics
    """
    
    def __init__(self):
        self.analysis_results = {}
        
    def analyze_thermal_response(self, temperature_data: pd.DataFrame, 
                                heater_cycles_data: Optional[List[Dict]] = None,
                                test_details: Optional[Dict] = None) -> Dict:
        """
        Comprehensive thermal response analysis.
        
        Args:
            temperature_data: Temperature time series data
            heater_cycles_data: Optional heater cycle information
            test_details: Optional test configuration details
            
        Returns:
            Dictionary containing thermal analysis results
        """
        try:
            results = {}
            
            # Basic thermal characteristics
            results['rise_rate_analysis'] = self._analyze_temperature_rise_rates(temperature_data)
            results['stability_analysis'] = self._analyze_temperature_stability(temperature_data)
            results['spatial_distribution'] = self._analyze_spatial_distribution(temperature_data)
            results['thermal_gradients'] = self._calculate_thermal_gradients(temperature_data)
            results['heat_transfer_metrics'] = self._calculate_heat_transfer_metrics(temperature_data)
            
            # Advanced analysis if heater data available
            if heater_cycles_data:
                results['heater_correlation'] = self._correlate_with_heater_cycles(
                    temperature_data, heater_cycles_data)
            
            # Test-specific analysis
            if test_details:
                results['performance_correlation'] = self._correlate_with_test_performance(
                    temperature_data, test_details)
            
            self.analysis_results = results
            return results
            
        except Exception as e:
            print(f"Error in thermal response analysis: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_temperature_rise_rates(self, temperature_data: pd.DataFrame) -> Dict:
        """Analyze temperature rise rates for all sensors."""
        try:
            temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
            if not temp_cols:
                return {'error': 'No temperature columns found'}
            
            rise_rates = {}
            
            for col in temp_cols:
                # Calculate temperature derivative (rise rate)
                temp_series = temperature_data[col].dropna()
                time_series = temperature_data['time'][:len(temp_series)]
                
                if len(temp_series) < 2:
                    continue
                
                # Calculate rise rate (°C/s)
                dt = np.diff(time_series)
                dT = np.diff(temp_series)
                rise_rate = dT / dt
                
                # Find maximum rise rate and its timing
                max_rise_idx = np.argmax(rise_rate)
                max_rise_rate = rise_rate[max_rise_idx]
                max_rise_time = time_series.iloc[max_rise_idx]
                
                # Calculate average rise rate during heating phase
                heating_mask = rise_rate > 0.1  # Threshold for heating
                avg_heating_rate = np.mean(rise_rate[heating_mask]) if np.any(heating_mask) else 0
                
                rise_rates[col] = {
                    'max_rise_rate': max_rise_rate,
                    'max_rise_time': max_rise_time,
                    'average_heating_rate': avg_heating_rate,
                    'total_temperature_rise': temp_series.max() - temp_series.min(),
                    'time_to_max_temp': time_series.iloc[temp_series.idxmax()],
                    'rise_rate_std': np.std(rise_rate)
                }
            
            # Overall statistics
            all_max_rates = [data['max_rise_rate'] for data in rise_rates.values()]
            all_avg_rates = [data['average_heating_rate'] for data in rise_rates.values()]
            
            return {
                'sensor_analysis': rise_rates,
                'overall_max_rise_rate': max(all_max_rates) if all_max_rates else 0,
                'overall_avg_rise_rate': np.mean(all_avg_rates) if all_avg_rates else 0,
                'rise_rate_uniformity': np.std(all_max_rates) / np.mean(all_max_rates) * 100 if all_max_rates else 0
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_temperature_stability(self, temperature_data: pd.DataFrame) -> Dict:
        """Analyze temperature stability and oscillations."""
        try:
            temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
            stability_metrics = {}
            
            for col in temp_cols:
                temp_series = temperature_data[col].dropna()
                
                if len(temp_series) < 10:
                    continue
                
                # Calculate stability metrics
                temp_mean = temp_series.mean()
                temp_std = temp_series.std()
                temp_range = temp_series.max() - temp_series.min()
                
                # Coefficient of variation
                cv = (temp_std / temp_mean * 100) if temp_mean != 0 else 0
                
                # Find oscillations/peaks
                peaks, _ = find_peaks(temp_series, height=temp_mean + temp_std)
                valleys, _ = find_peaks(-temp_series, height=-(temp_mean - temp_std))
                
                # Calculate settling characteristics
                final_temp = temp_series.iloc[-10:].mean()  # Last 10 readings
                settling_threshold = 0.02 * temp_range  # 2% of range
                
                # Find settling time (when temperature stays within threshold)
                settling_time = self._find_settling_time(temp_series, final_temp, settling_threshold)
                
                stability_metrics[col] = {
                    'temperature_std': temp_std,
                    'coefficient_of_variation': cv,
                    'temperature_range': temp_range,
                    'oscillation_count': len(peaks) + len(valleys),
                    'settling_time': settling_time,
                    'final_stability': temp_series.iloc[-10:].std(),
                    'overshoot_percentage': ((temp_series.max() - final_temp) / final_temp * 100) if final_temp != 0 else 0
                }
            
            # Overall stability assessment
            all_cvs = [data['coefficient_of_variation'] for data in stability_metrics.values()]
            all_stds = [data['temperature_std'] for data in stability_metrics.values()]
            
            return {
                'sensor_stability': stability_metrics,
                'overall_stability_score': 100 - np.mean(all_cvs) if all_cvs else 0,
                'average_temperature_std': np.mean(all_stds) if all_stds else 0,
                'most_stable_sensor': min(stability_metrics.keys(), 
                                        key=lambda x: stability_metrics[x]['coefficient_of_variation']) if stability_metrics else None
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_spatial_distribution(self, temperature_data: pd.DataFrame) -> Dict:
        """Analyze spatial temperature distribution."""
        try:
            temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
            
            if len(temp_cols) < 2:
                return {'error': 'Need at least 2 temperature sensors for spatial analysis'}
            
            # Calculate temperature differences between sensors
            spatial_analysis = {}
            
            # Cross-correlation analysis
            correlations = {}
            for i, col1 in enumerate(temp_cols):
                for j, col2 in enumerate(temp_cols[i+1:], i+1):
                    corr = temperature_data[col1].corr(temperature_data[col2])
                    correlations[f"{col1}_vs_{col2}"] = corr
            
            # Temperature uniformity analysis
            temp_matrix = temperature_data[temp_cols].values
            uniformity_metrics = {}
            
            for i, time_point in enumerate(temperature_data['time']):
                temps_at_time = temp_matrix[i, :]
                temps_at_time = temps_at_time[~np.isnan(temps_at_time)]
                
                if len(temps_at_time) > 1:
                    uniformity_metrics[time_point] = {
                        'temperature_range': np.max(temps_at_time) - np.min(temps_at_time),
                        'temperature_std': np.std(temps_at_time),
                        'uniformity_index': 1 - (np.std(temps_at_time) / np.mean(temps_at_time)) if np.mean(temps_at_time) != 0 else 0
                    }
            
            # Overall spatial metrics
            avg_correlation = np.mean(list(correlations.values()))
            max_temp_difference = max([data['temperature_range'] for data in uniformity_metrics.values()]) if uniformity_metrics else 0
            avg_uniformity = np.mean([data['uniformity_index'] for data in uniformity_metrics.values()]) if uniformity_metrics else 0
            
            return {
                'sensor_correlations': correlations,
                'uniformity_over_time': uniformity_metrics,
                'average_sensor_correlation': avg_correlation,
                'maximum_temperature_difference': max_temp_difference,
                'average_uniformity_index': avg_uniformity,
                'spatial_consistency_score': avg_correlation * avg_uniformity * 100
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_thermal_gradients(self, temperature_data: pd.DataFrame) -> Dict:
        """Calculate thermal gradients between measurement points."""
        try:
            temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
            
            if len(temp_cols) < 2:
                return {'error': 'Need at least 2 temperature sensors for gradient analysis'}
            
            gradients = {}
            
            # Calculate gradients between adjacent sensors (assuming spatial ordering)
            for i in range(len(temp_cols) - 1):
                col1, col2 = temp_cols[i], temp_cols[i + 1]
                
                # Temperature difference over time
                temp_diff = temperature_data[col2] - temperature_data[col1]
                
                gradients[f"{col1}_to_{col2}"] = {
                    'average_gradient': temp_diff.mean(),
                    'max_gradient': temp_diff.max(),
                    'min_gradient': temp_diff.min(),
                    'gradient_std': temp_diff.std(),
                    'gradient_range': temp_diff.max() - temp_diff.min()
                }
            
            # Overall gradient statistics
            all_avg_gradients = [data['average_gradient'] for data in gradients.values()]
            all_max_gradients = [data['max_gradient'] for data in gradients.values()]
            
            return {
                'gradient_analysis': gradients,
                'overall_avg_gradient': np.mean(all_avg_gradients) if all_avg_gradients else 0,
                'overall_max_gradient': max(all_max_gradients) if all_max_gradients else 0,
                'gradient_uniformity': np.std(all_avg_gradients) / np.mean(all_avg_gradients) * 100 if all_avg_gradients else 0
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_heat_transfer_metrics(self, temperature_data: pd.DataFrame) -> Dict:
        """Calculate heat transfer characteristics."""
        try:
            temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
            
            heat_transfer_metrics = {}
            
            for col in temp_cols:
                temp_series = temperature_data[col].dropna()
                time_series = temperature_data['time'][:len(temp_series)]
                
                if len(temp_series) < 10:
                    continue
                
                # Calculate thermal time constant (simplified)
                # Find 63% of final temperature rise
                initial_temp = temp_series.iloc[0]
                final_temp = temp_series.iloc[-10:].mean()
                temp_rise = final_temp - initial_temp
                
                if temp_rise > 0:
                    target_temp = initial_temp + 0.63 * temp_rise
                    
                    # Find time to reach 63% of final value
                    time_constant_idx = np.argmax(temp_series >= target_temp)
                    time_constant = time_series.iloc[time_constant_idx] if time_constant_idx > 0 else None
                    
                    # Calculate heat transfer coefficient (simplified)
                    # This is a simplified calculation - real HTC would need more parameters
                    if time_constant and time_constant > 0:
                        thermal_diffusivity = 1 / time_constant  # Simplified
                    else:
                        thermal_diffusivity = 0
                else:
                    time_constant = None
                    thermal_diffusivity = 0
                
                heat_transfer_metrics[col] = {
                    'thermal_time_constant': time_constant,
                    'thermal_diffusivity': thermal_diffusivity,
                    'temperature_rise': temp_rise,
                    'response_speed': 1 / time_constant if time_constant and time_constant > 0 else 0
                }
            
            # Overall heat transfer assessment
            all_time_constants = [data['thermal_time_constant'] for data in heat_transfer_metrics.values() 
                                if data['thermal_time_constant'] is not None]
            all_diffusivities = [data['thermal_diffusivity'] for data in heat_transfer_metrics.values()]
            
            return {
                'sensor_heat_transfer': heat_transfer_metrics,
                'average_time_constant': np.mean(all_time_constants) if all_time_constants else 0,
                'average_thermal_diffusivity': np.mean(all_diffusivities) if all_diffusivities else 0,
                'fastest_response_sensor': min(heat_transfer_metrics.keys(), 
                                             key=lambda x: heat_transfer_metrics[x]['thermal_time_constant'] or float('inf')) if heat_transfer_metrics else None
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _correlate_with_heater_cycles(self, temperature_data: pd.DataFrame, 
                                     heater_cycles_data: List[Dict]) -> Dict:
        """Correlate temperature response with heater cycles."""
        try:
            # This would require detailed correlation analysis
            # For now, return basic correlation metrics
            
            correlations = {}
            
            # Extract heater timing information
            heater_times = []
            for cycle in heater_cycles_data:
                try:
                    on_time = self._parse_time_string(cycle.get('switch_on', ''))
                    off_time = self._parse_time_string(cycle.get('switch_off', ''))
                    if on_time is not None and off_time is not None:
                        heater_times.append((on_time, off_time))
                except:
                    continue
            
            if not heater_times:
                return {'error': 'No valid heater timing data'}
            
            # Analyze temperature response to heater cycles
            temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
            
            for col in temp_cols:
                cycle_responses = []
                
                for on_time, off_time in heater_times:
                    # Find temperature data during this cycle
                    cycle_mask = (temperature_data['time'] >= on_time) & (temperature_data['time'] <= off_time)
                    cycle_temps = temperature_data.loc[cycle_mask, col]
                    
                    if len(cycle_temps) > 1:
                        temp_rise = cycle_temps.max() - cycle_temps.min()
                        cycle_responses.append(temp_rise)
                
                if cycle_responses:
                    correlations[col] = {
                        'average_cycle_response': np.mean(cycle_responses),
                        'response_consistency': np.std(cycle_responses) / np.mean(cycle_responses) * 100,
                        'max_cycle_response': max(cycle_responses),
                        'min_cycle_response': min(cycle_responses)
                    }
            
            return {
                'heater_temperature_correlation': correlations,
                'heater_cycles_analyzed': len(heater_times)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _correlate_with_test_performance(self, temperature_data: pd.DataFrame, 
                                        test_details: Dict) -> Dict:
        """Correlate thermal behavior with test performance."""
        try:
            # Extract relevant test parameters
            cut_off_temp = test_details.get('prop_tank_heater_cut-off_temp')
            reset_temp = test_details.get('prop_tank_heater_reset_temp')
            
            correlations = {}
            
            if cut_off_temp:
                try:
                    target_temp = float(cut_off_temp)
                    
                    # Analyze how well each sensor tracks the target
                    temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
                    
                    for col in temp_cols:
                        max_temp = temperature_data[col].max()
                        temp_accuracy = abs(max_temp - target_temp) / target_temp * 100
                        
                        correlations[col] = {
                            'target_temperature': target_temp,
                            'achieved_temperature': max_temp,
                            'temperature_accuracy': 100 - temp_accuracy,
                            'overshoot': max(0, max_temp - target_temp)
                        }
                        
                except ValueError:
                    pass
            
            return {
                'performance_correlation': correlations,
                'target_cut_off_temp': cut_off_temp,
                'target_reset_temp': reset_temp
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _find_settling_time(self, temp_series: pd.Series, final_temp: float, threshold: float) -> Optional[float]:
        """Find settling time for temperature."""
        try:
            # Find the last time temperature exceeded the threshold
            deviation = abs(temp_series - final_temp)
            settling_mask = deviation <= threshold
            
            if settling_mask.any():
                # Find the first index where it stays within threshold
                for i in range(len(settling_mask) - 10):
                    if settling_mask.iloc[i:i+10].all():  # Stays within threshold for 10 consecutive points
                        return temp_series.index[i]
            
            return None
            
        except Exception:
            return None
    
    def _parse_time_string(self, time_str: str) -> Optional[float]:
        """Parse time string to seconds (same as in HeaterAnalyzer)."""
        try:
            if not time_str or time_str.strip() == '':
                return None
                
            time_str = time_str.strip()
            
            # Try parsing as seconds first
            try:
                return float(time_str)
            except ValueError:
                pass
            
            # Try parsing as HH:MM:SS
            if ':' in time_str:
                parts = time_str.split(':')
                if len(parts) == 3:
                    hours, minutes, seconds = map(float, parts)
                    return hours * 3600 + minutes * 60 + seconds
                elif len(parts) == 2:
                    minutes, seconds = map(float, parts)
                    return minutes * 60 + seconds
            
            return None
            
        except Exception:
            return None
    
    def get_thermal_summary(self) -> Dict:
        """Get summary of key thermal performance metrics."""
        if not self.analysis_results:
            return {'error': 'No analysis results available'}
        
        try:
            summary = {}
            
            # Extract key thermal metrics
            if 'rise_rate_analysis' in self.analysis_results:
                rra = self.analysis_results['rise_rate_analysis']
                summary['max_temperature_rise_rate'] = rra.get('overall_max_rise_rate', 0)
                summary['average_heating_rate'] = rra.get('overall_avg_rise_rate', 0)
            
            if 'stability_analysis' in self.analysis_results:
                sa = self.analysis_results['stability_analysis']
                summary['temperature_stability_score'] = sa.get('overall_stability_score', 0)
                summary['most_stable_sensor'] = sa.get('most_stable_sensor', 'Unknown')
            
            if 'spatial_distribution' in self.analysis_results:
                sd = self.analysis_results['spatial_distribution']
                summary['spatial_consistency_score'] = sd.get('spatial_consistency_score', 0)
                summary['max_temperature_difference'] = sd.get('maximum_temperature_difference', 0)
            
            if 'heat_transfer_metrics' in self.analysis_results:
                htm = self.analysis_results['heat_transfer_metrics']
                summary['average_thermal_time_constant'] = htm.get('average_time_constant', 0)
                summary['fastest_response_sensor'] = htm.get('fastest_response_sensor', 'Unknown')
            
            return summary
            
        except Exception as e:
            return {'error': str(e)}
