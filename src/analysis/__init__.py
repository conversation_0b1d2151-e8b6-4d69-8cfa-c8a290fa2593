"""
Analysis Package
--------------
Components for analyzing temperature and pressure data from thruster tests.

Classes:
    - TemperatureAnalyzer: Analyzes temperature data
    - PressureAnalyzer: Analyzes pressure data
    - Performance: Performance calculations
    - HeaterAnalyzer: Heater performance analysis
    - ThermalAnalyzer: Enhanced thermal response analysis
    - EfficiencyAnalyzer: System efficiency metrics
    - ComparisonAnalyzer: Advanced comparison capabilities
"""

# Import individually to avoid circular imports
from .temperature import TemperatureAnalyzer
from .pressure import PressureAnalyzer
from .performance import Performance
from .heater import HeaterAnalyzer
from .thermal import ThermalAnalyzer
from .efficiency import EfficiencyAnalyzer
from .comparison import ComparisonAnalyzer

__all__ = [
    'TemperatureAnalyzer',
    'PressureAnalyzer',
    'Performance',
    'HeaterAnalyzer',
    'ThermalAnalyzer',
    'EfficiencyAnalyzer',
    'ComparisonAnalyzer'
]
