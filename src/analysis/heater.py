"""
Heater Analysis Module
---------------------
<PERSON>les analysis of heater performance and power consumption from thruster tests.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta


class HeaterAnalyzer:
    """
    Analyzes heater performance and power consumption from thruster tests.
    
    Features:
    - Heater cut-off duration analysis
    - Power consumption calculations
    - Heater cycle efficiency analysis
    - Thermal response characteristics
    """
    
    def __init__(self):
        self.analysis_results = {}
        
    def analyze_heater_cycles(self, heater_cycles_data: List[Dict], heater_info: Dict, 
                             temperature_data: Optional[pd.DataFrame] = None) -> Dict:
        """
        Comprehensive analysis of heater cycles.
        
        Args:
            heater_cycles_data: List of heater cycle dictionaries
            heater_info: Heater specifications dictionary
            temperature_data: Optional temperature data for correlation
            
        Returns:
            Dictionary containing all heater analysis results
        """
        try:
            results = {}
            
            # Basic cycle analysis
            results['cycle_durations'] = self._calculate_cycle_durations(heater_cycles_data)
            results['cut_off_analysis'] = self._analyze_cut_off_characteristics(heater_cycles_data)
            results['power_consumption'] = self._calculate_power_consumption(heater_cycles_data, heater_info)
            results['thermal_response'] = self._analyze_thermal_response(heater_cycles_data, temperature_data)
            results['efficiency_metrics'] = self._calculate_efficiency_metrics(heater_cycles_data, heater_info)
            
            # Store results
            self.analysis_results = results
            return results
            
        except Exception as e:
            print(f"Error in heater cycle analysis: {str(e)}")
            return {}
    
    def _calculate_cycle_durations(self, heater_cycles_data: List[Dict]) -> Dict:
        """Calculate duration metrics for heater cycles."""
        try:
            durations = []
            on_times = []
            off_times = []
            
            for i, cycle in enumerate(heater_cycles_data):
                if not cycle.get('switch_on') or not cycle.get('switch_off'):
                    continue
                    
                try:
                    # Parse time strings (assuming format like "HH:MM:SS" or seconds)
                    on_time = self._parse_time_string(cycle['switch_on'])
                    off_time = self._parse_time_string(cycle['switch_off'])
                    
                    if on_time is not None and off_time is not None:
                        duration = off_time - on_time
                        durations.append(duration)
                        on_times.append(on_time)
                        off_times.append(off_time)
                        
                except (ValueError, TypeError) as e:
                    print(f"Error parsing cycle {i+1} times: {e}")
                    continue
            
            if not durations:
                return {'error': 'No valid cycle durations found'}
            
            return {
                'individual_durations': durations,
                'average_duration': np.mean(durations),
                'total_on_time': sum(durations),
                'shortest_cycle': min(durations),
                'longest_cycle': max(durations),
                'duration_std': np.std(durations),
                'first_cycle_duration': durations[0] if durations else None,
                'cycle_count': len(durations)
            }
            
        except Exception as e:
            print(f"Error calculating cycle durations: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_cut_off_characteristics(self, heater_cycles_data: List[Dict]) -> Dict:
        """Analyze heater cut-off characteristics."""
        try:
            cut_off_temps = []
            max_temps = []
            locations = []
            
            for cycle in heater_cycles_data:
                try:
                    if cycle.get('max_temp'):
                        max_temp = float(cycle['max_temp'])
                        max_temps.append(max_temp)
                        cut_off_temps.append(max_temp)  # Assuming max_temp is cut-off temp
                        
                    if cycle.get('max_temp_location'):
                        locations.append(cycle['max_temp_location'])
                        
                except (ValueError, TypeError):
                    continue
            
            if not cut_off_temps:
                return {'error': 'No valid cut-off temperatures found'}
            
            return {
                'average_cut_off_temp': np.mean(cut_off_temps),
                'cut_off_temp_std': np.std(cut_off_temps),
                'max_cut_off_temp': max(cut_off_temps),
                'min_cut_off_temp': min(cut_off_temps),
                'cut_off_consistency': np.std(cut_off_temps) / np.mean(cut_off_temps) * 100,  # CV%
                'common_locations': self._find_common_locations(locations),
                'temperature_range': max(cut_off_temps) - min(cut_off_temps)
            }
            
        except Exception as e:
            print(f"Error analyzing cut-off characteristics: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_power_consumption(self, heater_cycles_data: List[Dict], heater_info: Dict) -> Dict:
        """Calculate total power consumption and efficiency metrics."""
        try:
            # Extract power information
            power_watts = self._extract_power_rating(heater_info)
            if power_watts is None:
                return {'error': 'No valid power rating found in heater_info'}
            
            # Get cycle durations
            cycle_analysis = self._calculate_cycle_durations(heater_cycles_data)
            if 'error' in cycle_analysis:
                return cycle_analysis
            
            total_on_time = cycle_analysis['total_on_time']
            average_duration = cycle_analysis['average_duration']
            
            # Calculate power metrics
            total_energy = power_watts * (total_on_time / 3600)  # kWh
            average_power_per_cycle = power_watts * (average_duration / 3600)  # kWh per cycle
            
            return {
                'heater_power_rating': power_watts,
                'total_on_time_hours': total_on_time / 3600,
                'total_energy_consumption': total_energy,
                'average_energy_per_cycle': average_power_per_cycle,
                'power_efficiency_ratio': self._calculate_power_efficiency(heater_cycles_data, power_watts),
                'energy_cost_estimate': total_energy * 0.12,  # Assuming $0.12/kWh
                'cycles_count': cycle_analysis['cycle_count']
            }
            
        except Exception as e:
            print(f"Error calculating power consumption: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_thermal_response(self, heater_cycles_data: List[Dict], 
                                 temperature_data: Optional[pd.DataFrame] = None) -> Dict:
        """Analyze thermal response characteristics."""
        try:
            if temperature_data is None:
                return self._basic_thermal_analysis(heater_cycles_data)
            
            # Advanced analysis with temperature data
            return self._advanced_thermal_analysis(heater_cycles_data, temperature_data)
            
        except Exception as e:
            print(f"Error analyzing thermal response: {str(e)}")
            return {'error': str(e)}
    
    def _basic_thermal_analysis(self, heater_cycles_data: List[Dict]) -> Dict:
        """Basic thermal analysis using only cycle data."""
        try:
            temp_rises = []
            tank_temps = []
            
            for cycle in heater_cycles_data:
                try:
                    if cycle.get('max_temp') and cycle.get('tank_bottom_temp'):
                        max_temp = float(cycle['max_temp'])
                        tank_temp = float(cycle['tank_bottom_temp'])
                        temp_rise = max_temp - tank_temp
                        temp_rises.append(temp_rise)
                        tank_temps.append(tank_temp)
                except (ValueError, TypeError):
                    continue
            
            if not temp_rises:
                return {'error': 'No valid temperature data found'}
            
            return {
                'average_temp_rise': np.mean(temp_rises),
                'temp_rise_std': np.std(temp_rises),
                'max_temp_rise': max(temp_rises),
                'average_tank_temp': np.mean(tank_temps),
                'thermal_uniformity': np.std(tank_temps) / np.mean(tank_temps) * 100
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _advanced_thermal_analysis(self, heater_cycles_data: List[Dict], 
                                  temperature_data: pd.DataFrame) -> Dict:
        """Advanced thermal analysis using temperature time series data."""
        try:
            # This would require correlation with actual temperature time series
            # For now, return basic analysis plus some derived metrics
            basic_analysis = self._basic_thermal_analysis(heater_cycles_data)
            
            # Add temperature data statistics
            temp_cols = [col for col in temperature_data.columns if col not in ['time', 'Sample']]
            
            if temp_cols:
                temp_stats = {}
                for col in temp_cols:
                    temp_stats[col] = {
                        'max': temperature_data[col].max(),
                        'mean': temperature_data[col].mean(),
                        'std': temperature_data[col].std()
                    }
                
                basic_analysis['temperature_sensor_stats'] = temp_stats
                basic_analysis['overall_temp_range'] = (
                    temperature_data[temp_cols].max().max() - 
                    temperature_data[temp_cols].min().min()
                )
            
            return basic_analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_efficiency_metrics(self, heater_cycles_data: List[Dict], heater_info: Dict) -> Dict:
        """Calculate various efficiency metrics."""
        try:
            cycle_analysis = self._calculate_cycle_durations(heater_cycles_data)
            cut_off_analysis = self._analyze_cut_off_characteristics(heater_cycles_data)
            
            if 'error' in cycle_analysis or 'error' in cut_off_analysis:
                return {'error': 'Cannot calculate efficiency without valid cycle data'}
            
            # Calculate heater efficiency for first cycle (°C/s)
            heater_efficiency = self._calculate_heater_efficiency(heater_cycles_data)

            # Calculate efficiency metrics
            avg_duration = cycle_analysis['average_duration']
            avg_temp_rise = cut_off_analysis.get('average_cut_off_temp', 0)

            return {
                'time_efficiency': 1 / avg_duration if avg_duration > 0 else 0,  # Cycles per second
                'thermal_efficiency': avg_temp_rise / avg_duration if avg_duration > 0 else 0,  # °C per second
                'heater_efficiency': heater_efficiency,  # °C per second for first cycle
                'consistency_score': 100 - cut_off_analysis.get('cut_off_consistency', 100),  # Higher is better
                'cycle_reliability': (cycle_analysis['cycle_count'] / 4) * 100,  # Assuming 4 expected cycles
                'temperature_stability': 100 - cut_off_analysis.get('cut_off_consistency', 100)
            }
            
        except Exception as e:
            return {'error': str(e)}

    def _calculate_heater_efficiency(self, heater_cycles_data: List[Dict]) -> float:
        """
        Calculate heater efficiency as temperature change per second for the first cycle.
        Heater efficiency = (temp_at_heater_off - temp_at_heater_on) / time_duration
        """
        try:
            if not heater_cycles_data:
                return 0.0

            # Get first cycle data
            first_cycle = heater_cycles_data[0]

            # Get heater on and off times
            on_time = self._parse_time_string(first_cycle.get('switch_on', ''))
            off_time = self._parse_time_string(first_cycle.get('switch_off', ''))

            if on_time is None or off_time is None:
                return 0.0

            # Calculate time duration
            time_duration = off_time - on_time
            if time_duration <= 0:
                return 0.0

            # Get temperature at heater off (cut-off temperature)
            temp_at_heater_off = first_cycle.get('max_temp')
            if temp_at_heater_off:
                try:
                    temp_at_heater_off = float(temp_at_heater_off)
                except (ValueError, TypeError):
                    return 0.0
            else:
                return 0.0

            # Get temperature at heater on (tank bottom temperature as baseline)
            temp_at_heater_on = first_cycle.get('tank_bottom_temp')
            if temp_at_heater_on:
                try:
                    temp_at_heater_on = float(temp_at_heater_on)
                except (ValueError, TypeError):
                    return 0.0
            else:
                return 0.0

            # Calculate temperature change
            temp_change = temp_at_heater_off - temp_at_heater_on

            # Calculate heater efficiency (°C/s)
            heater_efficiency = temp_change / time_duration

            return heater_efficiency

        except Exception as e:
            print(f"Error calculating heater efficiency: {str(e)}")
            return 0.0

    def _parse_time_string(self, time_str: str) -> Optional[float]:
        """Parse time string to seconds."""
        try:
            if not time_str or time_str.strip() == '':
                return None
                
            time_str = time_str.strip()
            
            # Try parsing as seconds first
            try:
                return float(time_str)
            except ValueError:
                pass
            
            # Try parsing as HH:MM:SS
            if ':' in time_str:
                parts = time_str.split(':')
                if len(parts) == 3:
                    hours, minutes, seconds = map(float, parts)
                    return hours * 3600 + minutes * 60 + seconds
                elif len(parts) == 2:
                    hours, minutes = map(float, parts)
                    return hours * 3600 + minutes * 60
            
            return None
            
        except Exception:
            return None
    
    def _extract_power_rating(self, heater_info: Dict) -> Optional[float]:
        """Extract power rating from heater info."""
        try:
            # Try different possible keys
            power_keys = ['Heater_input_Wattage', 'heater_input_power', 'wattage', 'power']

            print(f'Heater info: {heater_info}')
            
            for key in power_keys:
                if key in heater_info:
                    power_str = str(heater_info[key]).strip()
                    return float(power_str)
                    # print(f'Power string: {power_str}')
                    # if power_str:
                    #     # Extract numeric value
                    #     import re
                    #     numbers = re.findall(r'\d+\.?\d*', power_str)
                    #     if numbers:
                    #         return float(numbers[0])
            return None
            
        except Exception:
            return None
    
    def _find_common_locations(self, locations: List[str]) -> Dict:
        """Find most common temperature measurement locations."""
        try:
            if not locations:
                return {}
            
            location_counts = {}
            for loc in locations:
                if loc:
                    location_counts[loc] = location_counts.get(loc, 0) + 1
            
            if location_counts:
                most_common = max(location_counts, key=location_counts.get)
                return {
                    'most_common_location': most_common,
                    'location_frequency': location_counts[most_common],
                    'all_locations': location_counts
                }
            
            return {}
            
        except Exception:
            return {}
    
    def _calculate_power_efficiency(self, heater_cycles_data: List[Dict], power_watts: float) -> float:
        """Calculate power efficiency ratio."""
        try:
            # Simple efficiency calculation based on temperature rise per watt
            total_temp_rise = 0
            valid_cycles = 0
            
            for cycle in heater_cycles_data:
                try:
                    if cycle.get('max_temp') and cycle.get('tank_bottom_temp'):
                        max_temp = float(cycle['max_temp'])
                        tank_temp = float(cycle['tank_bottom_temp'])
                        temp_rise = max_temp - tank_temp
                        total_temp_rise += temp_rise
                        valid_cycles += 1
                except (ValueError, TypeError):
                    continue
            
            if valid_cycles > 0 and power_watts > 0:
                avg_temp_rise = total_temp_rise / valid_cycles
                return avg_temp_rise / power_watts  # °C per Watt
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def get_summary_metrics(self) -> Dict:
        """Get summary of key heater performance metrics."""
        if not self.analysis_results:
            return {'error': 'No analysis results available'}
        
        try:
            summary = {}
            
            # Extract key metrics
            if 'cycle_durations' in self.analysis_results:
                cd = self.analysis_results['cycle_durations']
                summary['total_heater_on_time'] = cd.get('total_on_time', 0)
                summary['average_cycle_duration'] = cd.get('average_duration', 0)
                summary['first_cycle_duration'] = cd.get('first_cycle_duration', 0)

            if 'power_consumption' in self.analysis_results:
                pc = self.analysis_results['power_consumption']
                summary['total_energy_consumed'] = pc.get('total_energy_consumption', 0)
                summary['heater_power_rating'] = pc.get('heater_power_rating', 0)
            
            if 'cut_off_analysis' in self.analysis_results:
                co = self.analysis_results['cut_off_analysis']
                summary['average_cut_off_temperature'] = co.get('average_cut_off_temp', 0)
                summary['temperature_consistency'] = co.get('cut_off_consistency', 0)
            
            if 'efficiency_metrics' in self.analysis_results:
                em = self.analysis_results['efficiency_metrics']
                summary['thermal_efficiency'] = em.get('thermal_efficiency', 0)
                summary['heater_efficiency'] = em.get('heater_efficiency', 0)
                summary['consistency_score'] = em.get('consistency_score', 0)
            
            return summary
            
        except Exception as e:
            return {'error': str(e)}
