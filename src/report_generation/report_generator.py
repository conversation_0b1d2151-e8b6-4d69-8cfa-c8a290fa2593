"""
Report Generator Module
----------------------
Complete implementation for generating test reports in PDF format with standardized tables.
"""
import os
import sys
import tempfile
import traceback
from typing import Dict, Optional
from PIL import Image
from .data_collector import TestDataCollector
from .image_handler import ImageHandler
from .layout_config import LAYOUT, TEXT_CONSTANTS
from .base_pdf import DynamicPDF
def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON><PERSON><PERSON>nstalle<PERSON> creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    return os.path.join(base_path, relative_path)

class TestReportGenerator:
    """Generates detailed PDF reports for thruster test data."""

    def __init__(self, output_dir: Optional[str] = None):
        self.output_dir = output_dir or os.getcwd()
        self.pdf = DynamicPDF()
        # Set UTF-8 encoding
        self.pdf.set_auto_page_break(auto=True, margin=15)

        try:
            # Get font paths using the centralized function
            from .setup_fonts import get_fonts_dir
            fonts_dir = get_fonts_dir()

            print(f"Using fonts directory: {fonts_dir}")

            regular_font = os.path.join(fonts_dir, 'DejaVuSansCondensed.ttf')
            bold_font = os.path.join(fonts_dir, 'DejaVuSansCondensed-Bold.ttf')
            
            # Verify fonts exist before adding them
            if not os.path.exists(regular_font):
                raise FileNotFoundError(f"Regular font not found at: {regular_font}")
            if not os.path.exists(bold_font):
                raise FileNotFoundError(f"Bold font not found at: {bold_font}")

            # Add fonts
            self.pdf.add_font('DejaVu', '', regular_font, uni=True)
            self.pdf.add_font('DejaVu', 'B', bold_font, uni=True)
        except Exception as e:
            print(f"Error setting up fonts in TestReportGenerator: {str(e)}")
            raise
        
        # Initialize other components
        self.data_collector = TestDataCollector()
        self.image_handler = ImageHandler()
        self.test_data = {}
        self._image_cache = {}
        self._dimension_cache = {}
        self.layout = LAYOUT
        self.text_const = TEXT_CONSTANTS
        self.plot_paths = None

    def _replace_special_chars(self, text: str) -> str:
        """Replace special characters with their ASCII equivalents."""
        replacements = {
            '\u2103': '°C',  # Replace °C with just °C
            '\u00b0': '°',   # Replace ° with just °
            '\u2109': '°F',  # Replace °F with just °F
            # Add more replacements as needed
        }
        for char, replacement in replacements.items():
            text = text.replace(char, replacement)
        return text

    def _create_table(self, table_data: dict) -> None:
        """Create a table with proper character handling."""
        # Process headers
        headers = [self._replace_special_chars(str(h)) for h in table_data['headers']]
        
        # Process data
        processed_data = []
        for row in table_data['data']:
            processed_row = [self._replace_special_chars(str(cell)) for cell in row]
            processed_data.append(processed_row)
        
        # Update table data
        table_data['headers'] = headers
        table_data['data'] = processed_data
        
        # Create table
        self.pdf.create_table(table_data)

    def _add_temperature_table(self) -> None:
        """Add temperature table with proper character handling."""
        temp_table = {
            'title': 'Temperature Measurements',
            'headers': [
                'Cycle',
                'Maximum Temperature (C) in the system',
                'Location',
                'Corresponding temperature at Tank Bottom (C)'
            ],
            'widths': [20, 60, 50, 60],
            'data': [
                [str(i+1), 
                 self._replace_special_chars(str(cycle['max_temp'])), 
                 cycle['max_temp_location'],
                 self._replace_special_chars(str(cycle['tank_bottom_temp']))]
                for i, cycle in enumerate(self.test_data['heater_cycles'])
            ]
        }
        self._create_table(temp_table)

    def _get_base_path(self):
        """Get the correct base path whether running as script or executable."""
        if getattr(sys, 'frozen', False):
            return os.path.dirname(sys.executable)
        else:
            return os.getcwd()

    def _apply_style(self, style_key: str) -> None:
        """Apply predefined style from layout config."""
        style = self.layout['fonts'].get(style_key)
        if style:
            self.pdf.set_font(*style)

    def _add_title_page(self) -> None:
        """Add report title page with standardized format."""
        self.pdf.add_page()
        self._apply_style('title')
        self.pdf.cell(0, 20, 'VAPR-iDEX Test Prerequisite', 0, 1, 'C')

        self._apply_style('cell')
        self.pdf.ln(1)
        test_no_text = f"Test No: {self.test_data['test_no']}"
        date_text = f"Test Date: {self.test_data['test_date']}"
        self.pdf.cell(self.pdf.get_string_width(test_no_text), 10, test_no_text, 0, 0, 'L')
        self.pdf.cell(0, 10, date_text, 0, 1, 'R')
        self.pdf.ln(5)

    def _add_section_header(self, title: str) -> None:
        """Add section header with consistent styling."""
        self.pdf.ln(self.layout['spacing']['before_section'])
        self.pdf.set_font(*self.layout['fonts']['section'])
        self.pdf.cell(0, self.layout['cell_heights']['title'], title, 0, 1, 'L')
        self.pdf.ln(self.layout['spacing']['after_section'])

    def _add_specifications_table(self, title: str, specifications: Dict[str, str], table_type) -> None:
        """Add a specifications table with consistent styling."""
        self.pdf.create_specification_table(specifications, title, table_type)

    def _add_basic_info_table(self) -> None:
        """Add basic information table."""
        self._add_specifications_table('', self.test_data['basic_info'], 'MainTable')

    def _add_system_specs(self) -> None:
        """Add system specifications with specification-style table."""
        self._add_specifications_table('System Specifications', self.test_data['system_specs'], 'MainTable')

    def _add_propellant_specs(self) -> None:
        """Add propellant specifications with specification-style table."""
        self._add_specifications_table('Propellant Specifications', self.test_data['propellant_specs'], 'MainTable')

    def _add_catalyst_specs(self) -> None:
        """Add catalyst specifications with specification-style table."""
        self._add_specifications_table('Catalyst Specifications', self.test_data['catalyst_specs'], 'MainTable')

    def _add_components_details(self) -> None:
        """Add component details with specification-style table."""
        self._add_specifications_table('Pressure Sensor 1-(Vacuum Chamber)', self.test_data['component_details'][0], 'SubTable')
        self._add_specifications_table('Pressure Sensor 2-(Propellant Tank)', self.test_data['component_details'][1], 'SubTable')
        self._add_specifications_table('Pressure Sensor 3-(Thruster)', self.test_data['component_details'][2], 'SubTable')
        self.pdf.add_page()

        heater_specs = self.test_data['component_details'][3]

        # First row
        heater_type_row = {
            'Heater Type': heater_specs.get('Heater_type', '')
        }
        self.pdf.create_specification_table(heater_type_row, 'Heater Specifications', 'MainTable')

        # Second row - Heater Input
        heater_input_row = {
            'Heater-1': heater_specs.get('Heater_input_power_Htr_1 (W)', ''),
            'Heater-2': heater_specs.get('Heater_input_power_Htr_2 (W)', ''),
            'Heater-3': heater_specs.get('Heater_input_power_Htr_3 (W)', ''),
            'Heater-4': heater_specs.get('Heater_input_power_Htr_4 (W)', ''),
            'Heater-5': heater_specs.get('Heater_input_power_Htr_5 (W)', '')
        }

        # Creating nested table for Heater Input
        self.pdf.create_nested_specification_table(
            'Heater Input Power',
            heater_input_row,
            'MainTable'
        )

    def _add_test_details(self) -> None:
        """Add test details with specification-style table."""
        self._add_specifications_table('Test Details', self.test_data['test_details'], 'MainTable')

    def _add_pump_operation(self) -> None:
        """Add pump operation details."""
        self.pdf.add_page()
        pump_operation = self.test_data['pump_operation']

        table_def = {
            'title': 'Pump Operation',
            'headers': [
                'Pump Start Time [HH:MM] (24H)',
                'Corresponding Tank Bottom Temperature (°C)',
                'Corresponding Thruster Temperature (°C)',
                'Corresponding Tank Pressure (Bar)',
                'Corresponding Thruster Pressure (Bar)'
            ],
            'widths': [28, 40, 40, 40, 40],
            'data': [[
                pump_operation.get('Pump_start_time', ''),
                pump_operation.get('Corresponding_tank_bottom_temperature (°C)', ''),
                pump_operation.get('Corresponding Thruster Temperature (°C)', ''),
                pump_operation.get('Corresponding Tank Pressure (Bar)', ''),
                pump_operation.get('Corresponding Thruster Pressure (Bar)', '')
            ]]
        }
        self.pdf.create_table(table_def)

    def _add_suction_valve_operation(self) -> None:
        """Add suction valve operation details."""
        suction_valve_operation = self.test_data['suction_valve_operation']

        table_def = {
            'title': 'Suction Valve Operation',
            'headers': [
                'Suction Valve Open Time [HH:MM] (24H)',
                'Corresponding Tank Bottom Temperature (°C)',
                'Corresponding Thruster Temperature (°C)',
                'Corresponding Tank Pressure (Bar)',
                'Corresponding Thruster Pressure (Bar)'
            ],
            'widths': [28, 40, 40, 40, 40],
            'data': [[
                suction_valve_operation.get('Suction_valve_open_time', ''),
                suction_valve_operation.get('Corresponding_tank_bottom_temperature (°C)', ''),
                suction_valve_operation.get('Corresponding Thruster Temperature (°C)', ''),
                suction_valve_operation.get('Corresponding Tank Pressure (Bar)', ''),
                suction_valve_operation.get('Corresponding Thruster Pressure (Bar)', '')
            ]]
        }
        self.pdf.create_table(table_def)

    def _add_vacuum_creation_in_tank(self) -> None:
        """Add vacuum creation in tank details."""
        vacuum_creation_in_tank_valve_on = self.test_data['vacuum_creation_in_tank_valve_on']
        vacuum_creation_in_tank_valve_off = self.test_data['vacuum_creation_in_tank_valve_off']

        table_def_on = {
            'title': 'Vacuum Creation in Tank',
            'headers': [
                'Valve Switch-on Time [HH:MM] (24H)',
                'Corresponding Tank Bottom Temperature (°C)',
                'Corresponding Thruster Temperature (°C)',
                'Corresponding Tank Pressure (P0, Bar)',
                'Pressure Drop in Tank (P1, Bar)',
            ],
            'widths': [28, 40, 40, 40, 40],
            'data': [[
                vacuum_creation_in_tank_valve_on.get('Vacuum_valve_open_time', ''),
                vacuum_creation_in_tank_valve_on.get('Corresponding_tank_bottom_temperature (°C)', ''),
                vacuum_creation_in_tank_valve_on.get('Corresponding Thruster Temperature (°C)', ''),
                vacuum_creation_in_tank_valve_on.get('Corresponding Tank Pressure (Bar)', ''),
                vacuum_creation_in_tank_valve_on.get('Vacuum_valve_open_pressure_drop_in_tank (mbar)', '')

            ]]
        }
        self.pdf.create_table(table_def_on)
        self.pdf.ln(self.layout['spacing']['between_tables'])

        table_def_off = {
            'title': '',
            'headers': [
                'Valve Switch-off Time [HH:MM] (24H)',
                'Corresponding Tank Bottom Temperature (°C)',
                'Corresponding Thruster Temperature (°C)',
                'Corresponding Tank Pressure (P2, Bar)',
                'Pressure Drop in Tank (P2-P1, Bar)',
            ],
            'widths': [28, 40, 40, 40, 40],
            'data': [[
                vacuum_creation_in_tank_valve_off.get('Vacuum_valve_close_time', ''),
                vacuum_creation_in_tank_valve_off.get('Corresponding_tank_bottom_temperature (°C)', ''),
                vacuum_creation_in_tank_valve_off.get('Corresponding Thruster Temperature (°C)', ''),
                vacuum_creation_in_tank_valve_off.get('Corresponding Tank Pressure (Bar)', ''),
                vacuum_creation_in_tank_valve_off.get('Vacuum_valve_close_pressure_drop_in_tank (mbar)', '')
            ]]
        }
        self.pdf.create_table(table_def_off)

    def _add_heater_info(self) -> None:
        """Add heater information section with detailed power input breakdown."""
        self.pdf.add_page()
        self._apply_style('title')
        self.pdf.cell(0, 20, 'VAPR-iDEX Heater Operation', 0, 1, 'C')

        # # Add test date if available
        # if 'test_date' in self.test_data:
        #     self._apply_style('cell')
        #     date_text = f"DATE: {self.test_data['test_date']}"
        #     self.pdf.cell(0, 10, date_text, 0, 1, 'L')
        #     self.pdf.ln(5)

        # Create heater info table with new format
        heater_info = self.test_data['heater_info']

        # First row - Heater Type
        heater_type_table = {
            'Heater Type': heater_info.get('Heater_type', '')
        }
        self.pdf.create_specification_table(heater_type_table, '', 'MainTable')

        power_input = {
                'Voltage': heater_info.get('Heater_input_Voltage', ''),
                'Current': heater_info.get('Heater_input_Current', ''),
                'Wattage': heater_info.get('Heater_input_Wattage', '')
            }

        # Create nested table for power input
        self.pdf.create_nested_specification_table(
            'Heater Actual Power Input',
            power_input,
            'MainTable'
        )

        # Temperature rows
        temp_table = {
            'Heater cut-off temperature': f"{heater_info.get('Heater_cut_off_temp (°C)', '')}°C",
            'Heater reset temperature': f"{heater_info.get('Heater_reset_temp (°C)', '')}°C"
        }
        self.pdf.create_specification_table(temp_table, '', 'MainTable')

    def _add_heater_cycles(self) -> None:
        """Add heater cycles with standardized tables."""


        print(f"Heater cycles: {self.test_data['heater_cycles']}")
        # Switch times table
        switch_table = {
            'title': 'Heater Switch Times',
            'headers': ['Cycle',
                        'Heater Switch On Time [HH:MM] (24H)',
                        'Corresponding Tank pressure (Bar)',
                        'Corresponding Thruster pressure (Bar)',
                        'Heater Switch Off Time [HH:MM] (24H)',
                        'Corresponding Tank pressure (Bar)'],
            'widths': [18, 25, 40.67, 40.67, 25, 40.67],
            'data': [
                [str(i+1), cycle['switch_on'], cycle['switch_on_corresponding_tank_pressure'], cycle['switch_on_corresponding_thruster_pressure'], cycle['switch_off'], cycle['switch_off_corresponding_tank_pressure']]
                for i, cycle in enumerate(self.test_data['heater_cycles'])
            ]
        }
        self.pdf.create_table(switch_table)

        self.pdf.ln(self.layout['spacing']['between_tables'])

        # Temperature table
        temp_table = {
            'title': '',
            'headers': [
                'Cycle',
                'Max temperature in the system (°C)',
                'Location',
                'Corresponding Thruster pressure (Bar)',
                'Corresponding temperature at Tank Bottom (°C)',
                'Corresponding Valve Temperature (°C)'
            ],
            'widths': [18, 34.4, 34.4, 34.4, 34.4, 34.4],
            'data': [
                [str(i+1), cycle['switch_off_corresponding_thruster_pressure'], cycle['max_temp'], cycle['max_temp_location'],
                 cycle['tank_bottom_temp'], cycle['valve_temp']]
                for i, cycle in enumerate(self.test_data['heater_cycles'])
            ]
        }
        # current_y = self.pdf.get_y()
        # self.pdf.set_y(current_y - 1)
        self._create_table(temp_table)

    def _add_valve_operation(self) -> None:
        """Add valve operation details with standardized tables."""
        self.pdf.add_page()
        self._apply_style('title')
        self.pdf.cell(0, 20, 'VAPR-iDEX Valve Operation', 0, 1, 'C')

        # Valve operation table
        valve_operation = self.test_data['firing_valve_operation']

        table_def = {
            'title': 'Valve Operation',
            'headers': [
                'Valve Switch-on Time [HH:MM] (24H)',
                'Corresponding Tank Bottom Temperature (°C)',
                'Corresponding Pressure inside Tank (P0) (mbar)',
                'Corresponding Pressure Drop in Tank (P1) (mbar)',
                'Valve Switch-off Time [HH:MM] (24H)',
                'Corresponding Tank Bottom Temperature (°C)'
            ],
            'widths': [30, 35, 30, 30, 30, 35],
            'data': [[
                valve_operation.get('Firing_valve_open_time', ''),
                valve_operation.get('Valve_On_Corresponding_tank_bottom_temperature (°C)', ''),
                valve_operation.get('Valve_On_Corresponding_tank_pressure (mbar)', ''),
                valve_operation.get('Valve_On_Pressure_drop_in_tank (mbar)', ''),
                valve_operation.get('Firing_valve_close_time', ''),
                valve_operation.get('Valve_Off_Corresponding_tank_bottom_temperature (°C)', '')
            ]]
        }
        self.pdf.create_table(table_def)

    def _add_note(self) -> None:
        """Add note section."""
        self.pdf.add_page()
        self._apply_style('section')
        self.pdf.cell(0, 10, 'Observations:', 0, 1, 'L')
        self._apply_style('normal')
        self.pdf.multi_cell(0, 6, self.test_data['note'], 0, 'L')

    def _add_post_test_observ(self) -> None:
        """Add post-test observations."""
        self.pdf.add_page()
        self._apply_style('title')
        self.pdf.cell(0, 20, 'VAPR-iDEX POST Test Analysis', 0, 1, 'C')
        self._add_specifications_table('Post Testing Observations',
                                     self.test_data['post_test_observations'], 'MainTable')

    def _add_catalyst_post_analysis(self) -> None:
        """Add catalyst post-analysis with standardized table."""
        cat_data = self.test_data['catalyst_post_analysis']
        
        table_def = {
            'title': 'Catalyst Post-Analysis',
            'headers': [
                'Catalyst details/ Specification',
                'Catalyst Color Before',
                'Catalyst Color After',
                'Catalyst Weight Filled (g)',
                'Catalyst Weight Recovered (g)',
                'Catalyst Change Percentage (%)'
            ],
            'widths': [35, 31, 31, 31, 31, 31],
            'data': [[
                cat_data.get('catalyst_details/specification', ''),
                cat_data.get('catalyst_color_before', ''),
                cat_data.get('catalyst_color_after', ''),
                cat_data.get('catalyst_weight_filled', ''),
                cat_data.get('catalyst_weight_recovered', ''),
                cat_data.get('catalyst_change_percentage', '')
            ]]
        }
        self.pdf.create_table(table_def)

    def _add_propellant_post_analysis(self) -> None:
        """Add propellant post-analysis with standardized tables."""
        prop_data = self.test_data['propellant_post_analysis']

        # First table
        table_1_def = {
            'title': 'Propellant Post-Analysis',
            'headers': [
                'Propellant details/ Specification',
                'Propellant Color Before',
                'Propellant Color After',
                'Propellant weight Filled (g)',
                'Propellant weight Recovered (g)',
                'Propellant used Percentage (%)'
            ],
            'widths': [35, 31, 31, 31, 31, 31],
            'data': [[
                prop_data.get('Propellant_details/specification', ''),
                prop_data.get('Propellant_color_before', ''),
                prop_data.get('Propellant_color_after', ''),
                prop_data.get('Propellant_weight_filled (g)', ''),
                prop_data.get('Propellant_weight_recovered (g)', ''),
                prop_data.get('Propellant_used_percentage (%)', '')
            ]]
        }
        self.pdf.create_table(table_1_def)

        self.pdf.ln(self.layout['spacing']['between_tables'])

        # Second table
        table_2_def = {
            'headers': [
                'Propellant RI Before Firing',
                'Propellant RI After Firing',
                'Firing Duration (s)',
                'Mass Flow Rate (mg/s)'
            ],
            'widths': [47.5, 47.5, 47.5, 47.5],
            'data': [[
                prop_data.get('Propellant_RI_(before_firing)', ''),
                prop_data.get('Propellant_RI_(after_firing)', ''),
                prop_data.get('Firing_duration (s)', ''),
                prop_data.get('Approximate_mass_flow_rate (mg/s)', '')
            ]]
        }
        self.pdf.create_table(table_2_def)

    def _add_schematics(self) -> None:
        """Add system schematics to the report."""
        try:
            self.pdf.add_page()

            current_y = self.pdf.get_y()
            self.pdf.set_y(current_y - 10)

            schematics_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'schematics')

            if os.path.exists(schematics_dir):
                self.pdf.set_font('Arial', 'B', 16)
                self.pdf.ln(10)
                self.pdf.cell(0, 10, 'System Schematics', 0, 1, 'L')
                self.pdf.ln(5)

                schematic_files = [f for f in os.listdir(schematics_dir)
                                 if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                schematic_files.sort()

                for schematic_file in schematic_files:
                    schematic_path = os.path.join(schematics_dir, schematic_file)
                    with Image.open(schematic_path) as img:
                        if img.mode in ('RGBA', 'LA'):
                            background = Image.new('RGB', img.size, (255, 255, 255))
                            background.paste(img, mask=img.split()[-1])
                            img = background
                        elif img.mode != 'RGB':
                            img = img.convert('RGB')

                        temp_path = os.path.join(self.output_dir, f'temp_{schematic_file}')
                        img.save(temp_path, 'PNG')

                    self.pdf.image(temp_path, x=30, w=150)
                    self.pdf.ln(5)

                    if os.path.exists(temp_path):
                        os.remove(temp_path)

        except Exception as e:
            print(f"Error adding schematics: {str(e)}")
            raise

    def _add_temperature_locations(self) -> None:
        """Add temperature locations information."""
        temperatures = self.text_const['temperature_locations']

        self._apply_style('small')

        self.pdf.cell(150, 6, self.text_const['heater_note'], 0, 1, 'C')

        self.pdf.ln(self.layout['spacing']['between_tables'])

        for temp in temperatures:
            self.pdf.cell(150, 5, temp, 0, 1, 'L')

    def _add_system_performance_boundary_conditions(self) -> None:
        boundary_table = ['Chamber_pressure_lower_limit (s)', 'Chamber_pressure_upper_limit (s)', 'Vacuum_pressure_lower_limit (s)', 'Vacuum_pressure_upper_limit (s)']
        boundary_table_dict = {key: self.test_data['system_performance'][key] for key in boundary_table}
        self._add_specifications_table('System boundaries - Performance', boundary_table_dict, 'MainTable')

    def _add_system_performance(self) -> None:
        """Add System Performance with specification-style table"""
        performance_table = ['Thrust (mN)', 'Burn_time (s)', 'Total_impulse (Ns)', 'Specific_impulse (s)', 'Coefficient_of_thrust', 'Mass_flow_rate (mg/s)', 'Tank_pressure (mbar)', 'Maximum_temperature (K)', 'Characteristic_velocity (m/s)', 'Vacuum_chamber_pressure (mbar)']
        performance_table_dict = {key: self.test_data['system_performance'][key] for key in performance_table}
        self._add_specifications_table('System Performance', performance_table_dict, 'MainTable')

    def _add_RI_table(self) -> None:
        """Add RI table"""
        page_height = self.pdf.h
        current_y = self.pdf.get_y()

        # Remaining page height
        remaining_height = page_height - current_y

        RI_table = {
            'title': 'RI Concentration Matrix - Propellant',
            'headers': ['Properties', 'Propellant (Before Test)', 'Propellant (After Test)'],
            'widths': [60, 65, 65],
            'data': self.test_data['RI_table']
        }

        if remaining_height >= 50:
            pass
        else:
            self.pdf.add_page()
            self.pdf.create_table(RI_table)

    def add_photos_section(self, photo_paths: Dict[str, str]) -> None:
        """Add photos section to the report with a 2x2 grid layout."""
        try:
            self.pdf.add_page()
            self.pdf.set_font('Arial', 'B', 16)
            self.pdf.cell(0, 10, 'Test Photos', 0, 1, 'C')
            self.pdf.ln(10)

            photo_labels = {
                'prop_before': "Propellant Before Test",
                'prop_after': "Propellant After Test",
                'cat_before': "Catalyst Before Test",
                'cat_after': "Catalyst After Test"
            }

            # Define grid layout parameters
            page_width = self.pdf.w
            margin = 15
            photo_width = (page_width - (3 * margin)) / 2
            photo_height = photo_width
            label_height = 10

            # Calculate starting positions
            left_x = margin
            right_x = margin * 2 + photo_width

            photo_items = list(photo_paths.items())
            for row in range(0, len(photo_items), 2):
                row_y = self.pdf.get_y()

                # Process up to 2 photos per row
                for col in range(2):
                    if row + col < len(photo_items):
                        photo_id, photo_path = photo_items[row + col]

                        if not os.path.exists(photo_path):
                            continue

                        try:
                            # First convert the image to RGB JPEG format
                            with Image.open(photo_path) as img:
                                # Convert to RGB if necessary
                                if img.mode in ('RGBA', 'LA'):
                                    background = Image.new('RGB', img.size, (255, 255, 255))
                                    background.paste(img, mask=img.split()[-1])
                                    img = background
                                elif img.mode != 'RGB':
                                    img = img.convert('RGB')

                                # Create temporary JPEG file
                                temp_path = os.path.join(
                                    os.path.dirname(photo_path),
                                    f'temp_{os.path.basename(photo_path)}.jpg'
                                )
                                img.save(temp_path, 'JPEG', quality=85, optimize=True)

                            # Set x position based on column
                            x_pos = left_x if col == 0 else right_x

                            # Add label
                            self.pdf.set_xy(x_pos, row_y)
                            self.pdf.set_font('Arial', 'B', 12)
                            label = photo_labels.get(photo_id, photo_id)
                            label_width = photo_width
                            self.pdf.cell(label_width, label_height, label, 0, 0, 'C')

                            # Add image below label
                            self.pdf.image(
                                temp_path,
                                x=x_pos,
                                y=row_y + label_height,
                                w=photo_width,
                                h=photo_height
                            )

                            # Clean up temporary file
                            if os.path.exists(temp_path):
                                os.remove(temp_path)

                        except Exception as e:
                            print(f"Error processing photo {photo_id}: {str(e)}")
                            traceback.print_exc()

                # Move to next row
                self.pdf.set_y(row_y + label_height + photo_height + margin)

        except Exception as e:
            print(f"Error in photos section: {str(e)}")
            traceback.print_exc()

    def _add_plots(self):
        """Add analysis plots to the report with optimal placement and grouping"""
        try:
            print("Starting _add_plots method")

            if not self.plot_paths:
                print("No plot paths available")
                return

            # Calculate optimal plot dimensions based on page size
            page_width = self.pdf.w
            page_height = self.pdf.h
            margin = 15
            plot_width = min(170, page_width - (2 * margin))  # Adaptive width
            
            # Track added plots to prevent duplicates
            added_plots = set()
            
            # Process plots by type
            plot_types = {
                'temperature': 'Temperature Analysis',
                'pressure': 'Pressure Analysis'
            }
            
            for plot_type, section_title in plot_types.items():
                if plot_type in self.plot_paths:
                    plot_dir = self.plot_paths[plot_type]
                    if os.path.exists(plot_dir) and os.listdir(plot_dir):
                        print(f"Processing {plot_type} plots from {plot_dir}")
                        
                        # Start a new page for each plot type
                        self.pdf.add_page()
                        self.pdf.set_font('Arial', 'B', 16)
                        self.pdf.cell(0, 10, section_title, 0, 1, 'C')
                        self.pdf.ln(5)
                        
                        # Get all plot files and sort them
                        plot_files = [f for f in os.listdir(plot_dir) 
                                     if f.endswith(('.png', '.jpg', '.jpeg'))]
                        plot_files.sort()
                        
                        for plot_file in plot_files:
                            plot_path = os.path.join(plot_dir, plot_file)
                            
                            # Skip if this plot has already been added
                            plot_basename = os.path.basename(plot_path)
                            if plot_basename in added_plots:
                                print(f"Skipping duplicate plot: {plot_basename}")
                                continue
                            
                            # Skip max temperature plot if not in temperature section
                            if "max_temperature" in plot_basename.lower() and plot_type != 'temperature':
                                print(f"Skipping misplaced temperature plot: {plot_basename}")
                                continue
                                
                            # Add to tracking set
                            added_plots.add(plot_basename)
                            
                            # Get actual image dimensions to calculate required space
                            with Image.open(plot_path) as img:
                                img_width, img_height = img.size
                                DPI = 300
                                img_width_mm = img_width * 25.4 / DPI
                                img_height_mm = img_height * 25.4 / DPI

                                if img_width_mm > plot_width:
                                    scale = plot_width / img_width_mm
                                    img_width_mm *= scale * 1.1
                                    img_height_mm *= scale * 1.1  # Maintain aspect ratio with slight height increase

                                # Add padding for title and spacing
                                required_height = img_height_mm + 20  # Add padding
                        
                            # Check if we need a new page
                            current_y = self.pdf.get_y()
                            remaining_height = page_height - current_y - margin
                            
                            if required_height > remaining_height:
                                self.pdf.add_page()
                        
                            # Process and add the image
                            temp_path = self._prepare_plot_image(plot_path)
                            if temp_path:
                                # Center the image
                                x_centered = (page_width - img_width_mm) / 2
                                self.pdf.image(temp_path, x=x_centered, w=img_width_mm, h=img_height_mm)
                                self.pdf.ln(20)
                                
                                # Clean up temp file
                                if os.path.exists(temp_path):
                                    os.remove(temp_path)

        except Exception as e:
            print(f"Error in _add_plots: {str(e)}")
            traceback.print_exc()
            raise

    def _prepare_plot_image(self, plot_path):
        """Prepare plot image for inclusion in PDF."""
        try:
            with Image.open(plot_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Create temporary file
                temp_path = os.path.join(self.output_dir, f'temp_{os.path.basename(plot_path)}')
                img.save(temp_path, 'PNG')
                return temp_path
        except Exception as e:
            print(f"Error preparing plot image {plot_path}: {str(e)}")
            return None

    def _add_test_authorization(self):
        """Add signature section for test authorization with proper formatting."""
        try:
            self.pdf.add_page()
            self._apply_style('title')
            self.pdf.cell(0, 20, 'Test Authorization', 0, 1, 'C')
            self.pdf.ln(20)

            # Layout parameters
            page_width = self.pdf.w
            margin = 15
            signature_width = (page_width - (2 * margin)) / 3
            line_height = 8
            name_spacing = 15
            label_y_offset = 110  # Fixed Y position for labels

            # Starting positions
            sections = ['Test Conducted by', 'Report Generated by', 'Report Authorized by']
            test_authorization = self.test_data.get('test_authorization', {})

            # Add names with underlines
            current_y = self.pdf.get_y()
            max_y = current_y

            for i, section in enumerate(sections):
                x = margin + (i * signature_width)
                names = [n.strip() for n in test_authorization.get(section, '').split(',')]

                # Add each name with underline
                local_y = current_y
                for name in names:
                    if name:
                        self.pdf.set_xy(x, local_y)
                        self._apply_style('normal')
                        self.pdf.cell(signature_width, line_height, name, 0, 0, 'C')

                        # Add underline
                        self.pdf.set_xy(x + signature_width / 4, local_y + line_height)
                        self.pdf.cell(signature_width / 2, 0, '', 'B', 0, 'C')

                        local_y += name_spacing
                        max_y = max(max_y, local_y)

            # Add section labels at fixed position
            for i, section in enumerate(sections):
                x = margin + (i * signature_width)
                self.pdf.set_xy(x, label_y_offset)
                self._apply_style('header')
                self.pdf.cell(signature_width, line_height, section, 0, 0, 'C')

            # Add date section
            self.pdf.ln(50)
            current_y = self.pdf.get_y()
            date_width = 50
            date_x = page_width - margin - date_width

            self._apply_style('normal')
            self.pdf.set_xy(date_x, current_y)
            self.pdf.cell(date_width, line_height, 'Date:', 0, 0, 'L')

            self.pdf.set_xy(date_x, current_y + line_height)
            self.pdf.cell(date_width, 0, '', 'B', 0, 'L')

        except Exception as e:
            print(f"Error adding test authorization section: {str(e)}")
            raise

    def generate_pdf(self, plot_paths: Dict[str, str], photo_paths: Dict[str, str] = None) -> str:
        """Generate the complete PDF report."""
        try:
            # Create temp directory if it doesn't exist
            os.makedirs(self.output_dir, exist_ok=True)

            # Store plot_paths for use in _add_plots
            self.plot_paths = plot_paths

            # Generate report sections
            self._add_title_page()
            self._add_basic_info_table()
            self._add_system_specs()
            self._add_propellant_specs()
            self._add_schematics()
            self._add_temperature_locations()
            self._add_catalyst_specs()
            self.pdf.add_table_title('Component Details')
            # Store starting position for later reference
            x_start = self.pdf.get_x()
            y_start = self.pdf.get_y()
            self.pdf.set_xy(x_start, y_start - 15)
            self._add_components_details()
            self._add_test_details()
            self._add_pump_operation()
            self._add_suction_valve_operation()
            self._add_vacuum_creation_in_tank()
            self._add_heater_info()
            self._add_heater_cycles()
            self._add_valve_operation()
            self._add_post_test_observ()
            self._add_catalyst_post_analysis()
            self._add_propellant_post_analysis()
            self._add_RI_table()

            # Add photos if provided
            if photo_paths:
                self.add_photos_section(photo_paths)

            # Add plots if available
            if self.plot_paths and any(os.path.exists(path) and os.listdir(path)
                                       for path in self.plot_paths.values()):
                self._add_plots()

            self._add_note()
            self._add_system_performance_boundary_conditions()
            self._add_system_performance()
            self._add_test_authorization()

            # Generate output filename
            output_filename = f"Test_Report_{self.test_data['test_no']}.pdf"
            output_path = os.path.join(self.output_dir, output_filename)

            # Ensure directory exists and is writable
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Try to save the PDF
            try:
                self.pdf.output(output_path)
                print(f"PDF saved successfully to: {output_path}")

                # Verify file was created
                if not os.path.exists(output_path):
                    raise FileNotFoundError(f"PDF file not created at {output_path}")

                # Verify file size
                if os.path.getsize(output_path) == 0:
                    raise ValueError(f"PDF file was created but is empty: {output_path}")

                return output_path

            except Exception as e:
                print(f"Error saving PDF to {output_path}: {str(e)}")
                # Try alternative location
                alt_output_path = os.path.join(tempfile.gettempdir(), output_filename)
                print(f"Attempting to save PDF to alternative location: {alt_output_path}")
                self.pdf.output(alt_output_path)
                return alt_output_path

        except Exception as e:
            print(f"Error generating PDF: {str(e)}")
            traceback.print_exc()
            raise