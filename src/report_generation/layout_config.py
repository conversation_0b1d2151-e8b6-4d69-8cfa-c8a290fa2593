"""
Layout Configuration Module
-------------------------
Defines layout settings and styles for the PDF report.
"""

LAYOUT = {
    # Page dimensions (A4)
    'page_width': 210,
    'page_height': 297,

    # Margins
    'margins': {
        'left': 10,
        'right': 10,
        'top': 10,
        'bottom': 15
    },

    # Effective area
    'effective_page_width': 210 - 2 * 10,
    'effective_page_height': 297 - 2 * 10,

    # Font configurations
    'fonts': {
        'title': ('Arial', 'B', 24),
        'section': ('Arial', 'B', 18),
        'subsection': ('Arial', 'B', 14),
        'normal': ('Arial', '', 12),
        'small': ('Arial', '', 10),
        'footer': ('Arial', 'B', 10),
        'header': ('Arial', 'B', 12),
        'cell': ('Arial', '', 12)
    },

    # Table styles
    'table': {
        'header': {
            'font': ('Arial', 'B', 12),
            'align': 'C',
            'fill': True,
            'text_color': (0, 0, 0),  # Black text
            'fill_color': (240, 240, 240)  # Light gray background
        },
        'cell': {
            'font': ('Arial', '', 12),
            'align': 'C',
            'fill': False,
            'text_color': (0, 0, 0),
            'fill_color': (255, 255, 255)
        }
    },

    # Standard cell heights
    'cell_heights': {
        'normal': 8,
        'header': 10,
        'title': 20,
        'multi': 6
    },

    # Padding on top and bottom of each cell
    'cell_padding': 3,

    # Image settings
    'max_image_width': 190,
    'image_margin': 10,

    # Spacing
    'spacing': {
        'before_section': 10,
        'after_section': 3,
        'between_tables': 8,
        'paragraph': 5
    },

    # Colors (RGB)
    'colors': {
        'text': {
            'normal': (0, 0, 0),
            'header': (0, 0, 0),
            'title': (0, 0, 0)
        },
        'fill': {
            'header': (240, 240, 240),
            'alternate': (245, 245, 245)
        },
        'lines': {
            'grid': (0, 0, 0),
            'border': (0, 0, 0)
        }
    },

    # Table column widths
    'column_widths': {
        'heater_cycles': {
            'cycle': 20,
            'switch_time': 65,
            'temperature': 45,
            'location': 40
        },
        'specifications': {
            'label': 70,
            'value': 'remaining'  # Will be calculated as remaining width
        }
    },

    # Section titles
    'titles': {
        'main': 'VAPR-iDEX Test Prerequisite',
        'sections': {
            'system_specs': 'System Specifications',
            'heater_operation': 'Heater Operation',
            'test_details': 'Test Details',
            'results': 'Test Results'
        }
    }
}

# Constants for common strings
TEXT_CONSTANTS = {
    'heater_note': '                5 patch heaters are attached to the lateral surface of the tank.',
    'temperature_locations': [
        'TC-1: Temperature at the bottom of the tank',
        'TC-2: Temperature at the tank lid',
        'TC-3: Temperature at the valve',
        'TC-4: Temperature at the thruster flange',
        'TC-5: Temperature at the catalyst chamber',
        'TC-6: Temperature at the nozzle exit'
    ]
}