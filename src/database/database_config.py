"""database configuration settings."""
import psycopg2
from PySide6.QtWidgets import QMessageBox

# EM-1 Server configuration
EM1_SERVER_CONFIG = {
    "dbname": "vapr_idex_db",
    "user": "postgres",
    "password": None,  # Will be set at runtime
    "host": "localhost",
    "port": "5432"
}

# EM-1 Client configuration
EM1_CLIENT_CONFIG = {
    "dbname": "vapr_idex_db",
    "user": "postgres",
    "password": None,  # Will be set at runtime
    "host": "************",
    "port": "5432"
}

# EM-2 Server configuration
EM2_SERVER_CONFIG = {
    "dbname": "vapr_idex_db_EM2",
    "user": "postgres",
    "password": None,  # Will be set at runtime
    "host": "localhost",
    "port": "5432"
}

# EM-2 Client configuration
EM2_CLIENT_CONFIG = {
    "dbname": "vapr_idex_db_EM2",
    "user": "postgres",
    "password": None,  # Will be set at runtime
    "host": "************",
    "port": "5432"
}

# Legacy configurations for backward compatibility
SERVER_CONFIG = EM2_SERVER_CONFIG
CLIENT_CONFIG = EM2_CLIENT_CONFIG

class DatabaseConfig:
    @staticmethod
    def set_database_password(password: str):
        """Set the database password for all configurations."""
        EM1_SERVER_CONFIG["password"] = password
        EM1_CLIENT_CONFIG["password"] = password
        EM2_SERVER_CONFIG["password"] = password
        EM2_CLIENT_CONFIG["password"] = password
        # Update legacy configs for backward compatibility
        SERVER_CONFIG["password"] = password
        CLIENT_CONFIG["password"] = password

    @staticmethod
    def get_connection_params(is_server=True, database_version="EM2"):
        """Get database connection parameters based on role and database version."""
        try:
            if database_version.upper() == "EM1":
                if is_server:
                    return EM1_SERVER_CONFIG
                return EM1_CLIENT_CONFIG
            else:  # Default to EM2
                if is_server:
                    return EM2_SERVER_CONFIG
                return EM2_CLIENT_CONFIG
        except Exception as e:
            print(f"Error getting database configuration: {str(e)}")
            raise

    @staticmethod
    def get_all_connection_params(is_server=True):
        """Get connection parameters for both EM1 and EM2 databases."""
        try:
            if is_server:
                return {
                    "EM1": EM1_SERVER_CONFIG,
                    "EM2": EM2_SERVER_CONFIG
                }
            else:
                return {
                    "EM1": EM1_CLIENT_CONFIG,
                    "EM2": EM2_CLIENT_CONFIG
                }
        except Exception as e:
            print(f"Error getting database configurations: {str(e)}")
            raise

    @staticmethod
    def test_connection(params):
        """Test database connection with given parameters."""
        try:
            conn = psycopg2.connect(**params)
            conn.close()
            return True, "Connection successful"
        except Exception as e:
            return False, f"Connection failed: {str(e)}"