"""Multi-database handler for accessing both EM-1 and EM-2 databases."""
import logging
from contextlib import contextmanager
from typing import Dict, List, Optional, Any, Union
import psycopg2
from psycopg2.pool import SimpleConnectionPool
import pandas as pd

from .database_config import DatabaseConfig

logger = logging.getLogger(__name__)


class MultiDatabaseHandler:
    """Handles operations across both EM-1 and EM-2 databases."""

    def __init__(self, is_server: bool = True):
        """
        Initialize connections to both databases.

        Args:
            is_server (bool): Whether to initialize as a server.
        """
        self.is_server = is_server
        self.pools = {}
        self.connection_params = {}
        
        # Initialize connection pools for both databases
        try:
            all_params = DatabaseConfig.get_all_connection_params(is_server)
            for db_version, params in all_params.items():
                self.connection_params[db_version] = params
                self.pools[db_version] = SimpleConnectionPool(
                    minconn=1, maxconn=10, **params
                )
            logger.info("Multi-database handler initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize multi-database handler: {str(e)}")
            raise

    @contextmanager
    def _get_connection(self, database_version: str):
        """Get a connection from the specified database pool."""
        if database_version not in self.pools:
            raise ValueError(f"Unknown database version: {database_version}")
        
        conn = self.pools[database_version].getconn()
        try:
            yield conn
        finally:
            self.pools[database_version].putconn(conn)

    def test_connections(self) -> Dict[str, tuple]:
        """Test connections to both databases."""
        results = {}
        for db_version, params in self.connection_params.items():
            results[db_version] = DatabaseConfig.test_connection(params)
        return results

    def get_test_data_from_database(self, test_no: int, database_version: str) -> Optional[Dict]:
        """Get test data from a specific database."""
        try:
            logger.info(f"Attempting to retrieve test {test_no} from {database_version} database")
            with self._get_connection(database_version) as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT * FROM test_data WHERE test_no = %s", (test_no,))
                    result = cur.fetchone()
                    if result:
                        columns = [desc[0] for desc in cur.description]
                        test_data = dict(zip(columns, result))
                        # Add database source information
                        test_data['database_source'] = database_version
                        logger.info(f"Successfully retrieved test {test_no} from {database_version}")
                        return test_data
                    else:
                        logger.info(f"Test {test_no} not found in {database_version} database")
                        return None
        except psycopg2.Error as e:
            logger.error(f"Error retrieving test data from {database_version}: {str(e)}")
            return None

    def get_test_data_from_both_databases(self, test_no: int) -> Dict[str, Optional[Dict]]:
        """Get test data from both databases for comparison."""
        results = {}
        for db_version in self.pools.keys():
            results[db_version] = self.get_test_data_from_database(test_no, db_version)
        return results

    def filter_tests_from_database(self, params: Dict, database_version: str) -> List[Dict]:
        """Filter tests from a specific database."""
        try:
            logger.info(f"Filtering tests from {database_version} with params: {params}")
            with self._get_connection(database_version) as conn:
                with conn.cursor() as cur:
                    query = """
                    SELECT test_no, test_date, basic_info, propellant_specs, heater_info
                    FROM test_data
                    """
                    conditions = []

                    if params.get('propellantConc'):
                        value = float(params.get('propellantConc', 0))
                        conditions.append(
                            f"CAST(propellant_specs->>'Concentration before testing (%)' AS float) BETWEEN {value - 1} AND {value + 1}")

                    if params.get('testNo'):
                        conditions.append(f"test_no = {int(params['testNo'])}")

                    if params.get('catalystName'):
                        catalyst_name = params['catalystName'].replace("'", "''")
                        conditions.append(f"basic_info->>'Catalyst' ILIKE '%{catalyst_name}%'")
                        logger.info(f"Added catalyst filter: basic_info->>'Catalyst' ILIKE '%{catalyst_name}%'")

                    if params.get('tankTemp'):
                        tank_temp = float(params['tankTemp'])
                        conditions.append(
                            f"CAST(heater_info->>'Heater_cut_off_temp (°C)' AS float) BETWEEN {tank_temp - 1} AND {tank_temp + 1}"
                        )

                    if conditions:
                        query += " WHERE " + " AND ".join(conditions)

                    query += " ORDER BY test_no"

                    logger.info(f"Executing query: {query}")
                    cur.execute(query)
                    results = cur.fetchall()
                    logger.info(f"Found {len(results)} raw results from {database_version}")
                    formatted_results = []

                    for row in results:
                        test_no, test_date, basic_info, propellant_specs, heater_info= row
                        try:
                            propellant_conc = float(propellant_specs.get('Concentration before testing (%)', 0))
                        except (ValueError, TypeError):
                            propellant_conc = 0

                        formatted_results.append({
                            'test_no': test_no,
                            'test_date': test_date.strftime('%Y-%m-%d') if test_date else '',
                            'catalyst_name': basic_info.get('Catalyst', ''),
                            'propellant_conc': propellant_conc,
                            'database_source': database_version,
                            'tank_temp': heater_info.get('Heater_cut_off_temp (°C)', ''),
                        })

                    logger.info(f"Returning {len(formatted_results)} formatted results from {database_version}")
                    return formatted_results

        except Exception as e:
            logger.error(f"Error filtering tests from {database_version}: {str(e)}")
            return []

    def filter_tests_from_both_databases(self, params: Dict) -> List[Dict]:
        """Filter tests from both databases and combine results."""
        all_results = []

        for db_version in self.pools.keys():
            results = self.filter_tests_from_database(params, db_version)
            all_results.extend(results)
        
        # Sort by test number
        all_results.sort(key=lambda x: x['test_no'])
        return all_results

    def get_temperature_data_from_database(self, test_id: int, database_version: str) -> Optional[pd.DataFrame]:
        """Get temperature data from a specific database."""
        try:
            with self._get_connection(database_version) as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT time_data, temperature_data FROM temperature_data WHERE test_id = %s", (test_id,))
                    result = cur.fetchone()
                    if result:
                        time_data, temp_data = result
                        df = pd.DataFrame({'time': time_data})
                        for col, values in temp_data.items():
                            df[col] = values
                        return df
                    return None
        except psycopg2.Error as e:
            logger.error(f"Error retrieving temperature data from {database_version}: {str(e)}")
            return None

    def get_pressure_data_from_database(self, test_id: int, database_version: str) -> Optional[pd.DataFrame]:
        """Get pressure data from a specific database."""
        try:
            with self._get_connection(database_version) as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT time_data, pressure_data FROM pressure_data WHERE test_id = %s", (test_id,))
                    result = cur.fetchone()
                    if result:
                        time_data, pressure_data = result
                        df = pd.DataFrame({'time': time_data})
                        for col, values in pressure_data.items():
                            df[col] = values
                        return df
                    return None
        except psycopg2.Error as e:
            logger.error(f"Error retrieving pressure data from {database_version}: {str(e)}")
            return None

    def get_unique_values_from_database(self, column_name: str, database_version: str) -> List:
        """Get unique values for a column from a specific database."""
        try:
            with self._get_connection(database_version) as conn:
                with conn.cursor() as cur:
                    if column_name == 'catalyst_name':
                        query = "SELECT DISTINCT basic_info->>'Catalyst' FROM test_data WHERE basic_info->>'Catalyst' IS NOT NULL"
                        cur.execute(query)
                        results = [row[0] for row in cur.fetchall() if row[0] and row[0].strip()]
                        return results
                    elif column_name == 'propellant_conc':
                        query = "SELECT DISTINCT CAST(propellant_specs->>'Concentration before testing (%)' AS float) FROM test_data WHERE propellant_specs->>'Concentration before testing (%)' IS NOT NULL"
                        cur.execute(query)
                        return [row[0] for row in cur.fetchall()]
                    else:
                        query = f"SELECT DISTINCT {column_name} FROM test_data WHERE {column_name} IS NOT NULL"
                        cur.execute(query)
                        return [row[0] for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Error getting unique values from {database_version}: {str(e)}")
            return []

    def get_unique_values_from_both_databases(self, column_name: str) -> Dict[str, List]:
        """Get unique values for a column from both databases."""
        results = {}
        for db_version in self.pools.keys():
            results[db_version] = self.get_unique_values_from_database(column_name, db_version)
        return results

    def close_connections(self):
        """Close all database connection pools."""
        for db_version, pool in self.pools.items():
            try:
                pool.closeall()
                logger.info(f"Closed connection pool for {db_version}")
            except Exception as e:
                logger.error(f"Error closing connection pool for {db_version}: {str(e)}")
