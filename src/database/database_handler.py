import json
import os
import shutil
import tempfile
import logging
import traceback
from typing import Dict, Optional, List, Union
import pandas as pd
import psycopg2
from psycopg2.extras import <PERSON>son
from psycopg2.pool import SimpleConnectionPool
from contextlib import contextmanager

from src.database.database_config import DatabaseConfig

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseHandler:
    """Handles all database operations for the VAPR-iDEX test data."""

    def __init__(self, is_server: bool = True):
        """
        Initialize database connection pool.

        Args:
            is_server (bool): Whether to initialize as a server (creates database and tables).

        Raises:
            psycopg2.Error: If connection pool initialization fails.
        """
        self.connection_params = DatabaseConfig.get_connection_params(is_server)
        self.pool = SimpleConnectionPool(minconn=1, maxconn=20, **self.connection_params)
        if is_server:
            self.initialize_database()

    @contextmanager
    def _get_connection(self):
        """Get a connection from the pool and ensure it's returned."""
        conn = self.pool.getconn()
        try:
            yield conn
        finally:
            self.pool.putconn(conn)

    def initialize_database(self) -> None:
        """
        Create database and tables if they don't exist, and add new columns if missing.

        Raises:
            psycopg2.Error: If database initialization fails.
        """
        try:
            # Connect to 'postgres' database to create the target database
            temp_params = self.connection_params.copy()
            temp_params['dbname'] = 'postgres'
            with psycopg2.connect(**temp_params) as conn:
                conn.autocommit = True
                with conn.cursor() as cur:
                    cur.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                                (self.connection_params['dbname'],))
                    if not cur.fetchone():
                        cur.execute(f"CREATE DATABASE {self.connection_params['dbname']}")

            # Connect to the target database and create tables
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    # Create test_data table
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS test_data (
                            test_id SERIAL PRIMARY KEY,
                            test_no INTEGER NOT NULL UNIQUE,
                            test_date DATE,
                            basic_info JSONB,
                            system_specs JSONB,
                            propellant_specs JSONB,
                            catalyst_specs JSONB,
                            component_details JSONB,
                            test_details JSONB,
                            pump_operation JSONB,
                            suction_valve_operation JSONB,
                            vacuum_creation_in_tank_valve_on JSONB,
                            vacuum_creation_in_tank_valve_off JSONB,
                            heater_info JSONB,
                            heater_cycles JSONB,
                            firing_valve_operation JSONB,
                            note TEXT,
                            post_test_observations JSONB,
                            catalyst_post_analysis JSONB,
                            propellant_post_analysis JSONB,
                            performance_data JSONB,
                            ri_table JSONB,
                            test_authorization JSONB,
                            pressure_relations JSONB,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    # Create indexes for performance
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_test_no ON test_data(test_no)")
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_test_id ON test_data(test_id)")

                    # Create other tables
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS temperature_data (
                            id SERIAL PRIMARY KEY,
                            test_id INTEGER REFERENCES test_data(test_id),
                            time_data FLOAT[],
                            temperature_data JSONB,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS pressure_data (
                            id SERIAL PRIMARY KEY,
                            test_id INTEGER REFERENCES test_data(test_id),
                            time_data FLOAT[],
                            pressure_data JSONB,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS test_plots (
                            id SERIAL PRIMARY KEY,
                            test_id INTEGER REFERENCES test_data(test_id),
                            plot_type VARCHAR(50),
                            plot_title VARCHAR(255),
                            plot_data BYTEA,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS test_photos (
                            id SERIAL PRIMARY KEY,
                            test_id INTEGER REFERENCES test_data(test_id),
                            photo_type VARCHAR(50),
                            photo_data BYTEA,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS test_reports (
                            id SERIAL PRIMARY KEY,
                            test_id INTEGER REFERENCES test_data(test_id),
                            report_data BYTEA,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    cur.execute("""
                        CREATE TABLE IF NOT EXISTS temperature_analysis (
                            id SERIAL PRIMARY KEY,
                            test_id INTEGER UNIQUE REFERENCES test_data(test_id) ON DELETE CASCADE,
                            analysis_data JSONB,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    # Add missing columns if they don't exist
                    cur.execute("""
                        DO $$
                        BEGIN
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='ri_table') THEN
                                ALTER TABLE test_data ADD COLUMN ri_table JSONB;
                            END IF;
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='test_authorization') THEN
                                ALTER TABLE test_data ADD COLUMN test_authorization JSONB;
                            END IF;
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                        WHERE table_name='test_data' AND column_name='pressure_relations') THEN
                                ALTER TABLE test_data ADD COLUMN pressure_relations JSONB;
                            END IF;
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='pump_operation') THEN
                                ALTER TABLE test_data ADD COLUMN pump_operation JSONB;
                            END IF;
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='suction_valve_operation') THEN
                                ALTER TABLE test_data ADD COLUMN suction_valve_operation JSONB;
                            END IF;
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='vacuum_creation_in_tank_valve_on') THEN
                                ALTER TABLE test_data ADD COLUMN vacuum_creation_in_tank_valve_on JSONB;
                            END IF;
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                         WHERE table_name='test_data' AND column_name='vacuum_creation_in_tank_valve_off') THEN
                                ALTER TABLE test_data ADD COLUMN vacuum_creation_in_tank_valve_off JSONB;
                            END IF;
                            IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                                            WHERE table_name='test_data' AND column_name='firing_valve_operation') THEN
                                ALTER TABLE test_data ADD COLUMN firing_valve_operation JSONB;
                            END IF;
                        END $$;
                    """)
                conn.commit()
        except psycopg2.Error as e:
            logger.error(f"Error initializing database: {str(e)}", exc_info=True)
            raise

    def save_test_data(self, test_data: Dict, plot_paths: Optional[Dict] = None, photo_paths: Optional[Dict] = None) -> int:
        """
        Save test data and associated plots/photos to the database.

        Args:
            test_data (Dict): Test data dictionary containing test details.
            plot_paths (Optional[Dict]): Dictionary of plot types to plot file paths.
            photo_paths (Optional[Dict]): Dictionary of photo types to photo file paths.

        Returns:
            int: The test_id of the saved or updated test.

        Raises:
            ValueError: If test_data is invalid or missing required fields.
            psycopg2.Error: If a database error occurs.
        """
        if not isinstance(test_data, dict) or 'test_no' not in test_data:
            raise ValueError("test_data must be a dictionary with a 'test_no' key")

        with self._get_connection() as conn:
            with conn.cursor() as cur:
                try:
                    cur.execute("SET datestyle = 'DMY';")
                    # Process firing_duration
                    basic_info = test_data.get('basic_info', {}).copy()
                    firing_duration = test_data.get('firing_duration')
                    if firing_duration is not None:
                        if isinstance(firing_duration, str) and 'Firing Duration:' in firing_duration:
                            try:
                                firing_duration = float(firing_duration.split(':')[1].strip().replace('s', ''))
                            except (IndexError, ValueError):
                                raise ValueError("Invalid firing_duration format")
                        basic_info['firing_duration'] = firing_duration

                    # Check if test_no exists
                    test_no = test_data['test_no']
                    cur.execute("SELECT test_id FROM test_data WHERE test_no = %s", (test_no,))
                    result = cur.fetchone()
                    test_id = result[0] if result else None

                    if test_id:
                        # Update existing record
                        fields = []
                        values = []
                        for key, value in [
                            ('test_date', test_data.get('test_date')),
                            ('basic_info', basic_info),
                            ('system_specs', test_data.get('system_specs', {})),
                            ('propellant_specs', test_data.get('propellant_specs', {})),
                            ('catalyst_specs', test_data.get('catalyst_specs', {})),
                            ('component_details', test_data.get('component_details', {})),
                            ('test_details', test_data.get('test_details', {})),
                            ('pump_operation', test_data.get('pump_operation', {})),
                            ('suction_valve_operation', test_data.get('suction_valve_operation', {})),
                            ('vacuum_creation_in_tank_valve_on', test_data.get('vacuum_creation_in_tank_valve_on', {})),
                            ('vacuum_creation_in_tank_valve_off', test_data.get('vacuum_creation_in_tank_valve_off', {})),
                            ('heater_info', test_data.get('heater_info', {})),
                            ('heater_cycles', test_data.get('heater_cycles', [])),
                            ('firing_valve_operation', test_data.get('firing_valve_operation', {})),
                            ('note', test_data.get('note', '')),
                            ('post_test_observations', test_data.get('post_test_observations', {})),
                            ('catalyst_post_analysis', test_data.get('catalyst_post_analysis', {})),
                            ('propellant_post_analysis', test_data.get('propellant_post_analysis', {})),
                            ('performance_data', test_data.get('system_performance', {})),
                            ('ri_table', test_data.get('ri_table', {})),
                            ('test_authorization', test_data.get('test_authorization', {})),
                            ('pressure_relations', test_data.get('pressure_relations', {}))
                        ]:
                            if value is not None:
                                fields.append(f"{key} = %s")
                                values.append(Json(value) if isinstance(value, (dict, list)) else value)
                        if fields:
                            query = f"UPDATE test_data SET {', '.join(fields)} WHERE test_id = %s"
                            values.append(test_id)
                            cur.execute(query, values)
                    else:
                        # Insert new record
                        query = """
                            INSERT INTO test_data (
                                test_no, test_date, basic_info, system_specs,
                                propellant_specs, catalyst_specs, component_details,
                                test_details, pump_operation, suction_valve_operation,
                                vacuum_creation_in_tank_valve_on,
                                vacuum_creation_in_tank_valve_off, heater_info, heater_cycles, firing_valve_operation,
                                note, post_test_observations, catalyst_post_analysis,
                                propellant_post_analysis, performance_data,
                                ri_table, test_authorization, pressure_relations
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            RETURNING test_id
                        """
                        values = (
                            test_no,
                            test_data.get('test_date'),
                            Json(basic_info),
                            Json(test_data.get('system_specs', {})),
                            Json(test_data.get('propellant_specs', {})),
                            Json(test_data.get('catalyst_specs', {})),
                            Json(test_data.get('component_details', {})),
                            Json(test_data.get('test_details', {})),
                            Json(test_data.get('pump_operation', {})),
                            Json(test_data.get('suction_valve_operation', {})),
                            Json(test_data.get('vacuum_creation_in_tank_valve_on', {})),
                            Json(test_data.get('vacuum_creation_in_tank_valve_off', {})),
                            Json(test_data.get('heater_info', {})),
                            Json(test_data.get('heater_cycles', [])),
                            Json(test_data.get('firing_valve_operation', {})),
                            test_data.get('note', ''),
                            Json(test_data.get('post_test_observations', {})),
                            Json(test_data.get('catalyst_post_analysis', {})),
                            Json(test_data.get('propellant_post_analysis', {})),
                            Json(test_data.get('system_performance', {})),
                            Json(test_data.get('ri_table', {})),
                            Json(test_data.get('test_authorization', {})),
                            Json(test_data.get('pressure_relations', {}))
                        )
                        cur.execute(query, values)
                        result = cur.fetchone()
                        if result:
                            test_id = result[0]
                        else:
                            raise psycopg2.Error("Failed to retrieve test_id after insert")

                    # Commit test_data before saving related data
                    conn.commit()

                    # Save temperature data if available
                    if 'temperature_data' in test_data and test_data['temperature_data']:
                        df = pd.DataFrame({
                            'time': test_data['temperature_data']['time'],
                            **test_data['temperature_data']['temperatures']
                        })
                        self.save_temperature_data(test_id, df)

                    # Save pressure data if available
                    if 'pressure_data' in test_data and test_data['pressure_data']:
                        df = pd.DataFrame({
                            'time': test_data['pressure_data']['time'],
                            **test_data['pressure_data']['pressures']
                        })
                        self.save_pressure_data(test_id, df)

                    # Save plots
                    if plot_paths and isinstance(plot_paths, dict):
                        cur.execute("DELETE FROM test_plots WHERE test_id = %s", (test_id,))
                        plot_values = []
                        for plot_type, plot_dir in plot_paths.items():
                            if os.path.exists(plot_dir):
                                for plot_file in os.listdir(plot_dir):
                                    if plot_file.endswith(('.png', '.jpg', '.jpeg')):
                                        plot_path = os.path.join(plot_dir, plot_file)
                                        with open(plot_path, 'rb') as f:
                                            plot_data = f.read()
                                            plot_title = os.path.splitext(plot_file)[0]
                                            plot_values.append((test_id, plot_type, plot_title, psycopg2.Binary(plot_data)))
                        if plot_values:
                            cur.executemany("""
                                INSERT INTO test_plots (test_id, plot_type, plot_title, plot_data)
                                VALUES (%s, %s, %s, %s)
                            """, plot_values)

                    # Save photos
                    if photo_paths and isinstance(photo_paths, dict):
                        cur.execute("DELETE FROM test_photos WHERE test_id = %s", (test_id,))
                        photo_values = []
                        for photo_type, photo_info in photo_paths.items():
                            photo_path = photo_info.get('path') if isinstance(photo_info, dict) else photo_info
                            if photo_path and os.path.exists(photo_path):
                                with open(photo_path, 'rb') as f:
                                    photo_data = f.read()
                                    photo_values.append((test_id, photo_type, psycopg2.Binary(photo_data)))
                        if plot_values:
                            cur.executemany("""
                                INSERT INTO test_photos (test_id, photo_type, photo_data)
                                VALUES (%s, %s, %s)
                            """, photo_values)
                        logger.info(f"Successfully saved photos for test_id: {test_id}")

                    # Save temperature analysis if available
                    if 'temperature_analysis' in test_data and isinstance(test_data['temperature_analysis'], pd.DataFrame):
                        # Verify test_id exists
                        cur.execute("SELECT test_id FROM test_data WHERE test_id = %s", (test_id,))
                        if not cur.fetchone():
                            raise ValueError(f"Test ID {test_id} does not exist in test_data")
                        self.save_temperature_analysis(test_id, test_data['temperature_analysis'])

                    # Handle filtered temperature data
                    performance_data = test_data.get('performance_data', {})
                    if 'filtered_temp_data' in test_data or 'filtered_temperature_data' in test_data:
                        filtered_data = test_data.get('filtered_temp_data', test_data.get('filtered_temperature_data'))
                        if isinstance(filtered_data, dict):
                            if 'selected_columns' in filtered_data and 'selected_ranges' in filtered_data:
                                if 'temperature_data' in test_data:
                                    temp_data = pd.DataFrame({
                                        'time': test_data['temperature_data']['time']
                                    })
                                    for col, values in test_data['temperature_data']['temperatures'].items():
                                        temp_data[col] = values
                                    mask = pd.Series(False, index=temp_data.index)
                                    for start, end in filtered_data['selected_ranges']:
                                        mask |= ((temp_data['time'] >= start) & (temp_data['time'] <= end))
                                    filtered_df = temp_data[mask]
                                    columns_to_use = ['time'] + filtered_data['selected_columns']
                                    filtered_df = filtered_df[columns_to_use]
                                    filtered_data['time_data'] = filtered_df['time'].tolist()
                                    filtered_data['temperature_data'] = {
                                        col: filtered_df[col].tolist()
                                        for col in filtered_df.columns if col != 'time'
                                    }
                            performance_data['filtered_temp_data'] = filtered_data
                            if 'filtered_temp_data' in test_data:
                                del test_data['filtered_temp_data']
                            if 'filtered_temperature_data' in test_data:
                                del test_data['filtered_temperature_data']
                            cur.execute("UPDATE test_data SET performance_data = %s WHERE test_id = %s",
                                        (Json(performance_data), test_id))

                    conn.commit()
                    logger.info(f"Successfully saved test data for test_id: {test_id}")
                    return test_id
                except (psycopg2.Error, ValueError) as e:
                    conn.rollback()
                    logger.error(f"Error saving test data: {str(e)}", exc_info=True)
                    raise
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Unexpected error saving test data: {str(e)}", exc_info=True)
                    raise

    def save_temperature_data(self, test_id: int, temp_data: pd.DataFrame) -> None:
        """
        Save temperature data to the database.

        Args:
            test_id (int): The ID of the test.
            temp_data (pd.DataFrame): DataFrame containing time and temperature data.

        Raises:
            ValueError: If temp_data is not a DataFrame or lacks required columns.
            psycopg2.Error: If a database error occurs.
        """
        if not isinstance(temp_data, pd.DataFrame) or 'time' not in temp_data.columns:
            raise ValueError("temp_data must be a DataFrame with a 'time' column")
        with self._get_connection() as conn:
            with conn.cursor() as cur:
                try:
                    time_data = temp_data['time'].tolist()
                    temp_cols = [col for col in temp_data.columns if col != 'time']
                    temperature_data = {col: temp_data[col].tolist() for col in temp_cols}
                    cur.execute("""
                        INSERT INTO temperature_data (test_id, time_data, temperature_data)
                        VALUES (%s, %s, %s)
                    """, (test_id, time_data, Json(temperature_data)))
                    conn.commit()
                    logger.info(f"Successfully saved temperature data for test_id: {test_id}")
                except psycopg2.Error as e:
                    conn.rollback()
                    logger.error(f"Error saving temperature data for test_id {test_id}: {str(e)}", exc_info=True)
                    raise

    def save_pressure_data(self, test_id: int, pressure_data: pd.DataFrame) -> None:
        """
        Save pressure data to the database.

        Args:
            test_id (int): The ID of the test.
            pressure_data (pd.DataFrame): DataFrame containing time and pressure data.

        Raises:
            ValueError: If pressure_data is not a DataFrame or lacks a time column.
            psycopg2.Error: If a database error occurs.
        """
        if not isinstance(pressure_data, pd.DataFrame):
            raise ValueError("pressure_data must be a DataFrame")
        time_col = next((col for col in pressure_data.columns if 'time' in col.lower()), None)
        if not time_col:
            raise ValueError("pressure_data must have a column containing 'time' in its name")
        with self._get_connection() as conn:
            with conn.cursor() as cur:
                try:
                    time_data = pressure_data[time_col].tolist()
                    pressure_cols = [col for col in pressure_data.columns if col != time_col]
                    pressure_dict = {col: pressure_data[col].tolist() for col in pressure_cols}
                    cur.execute("""
                        INSERT INTO pressure_data (test_id, time_data, pressure_data)
                        VALUES (%s, %s, %s)
                    """, (test_id, time_data, Json(pressure_dict)))
                    conn.commit()
                    logger.info(f"Successfully saved pressure data for test_id: {test_id}")
                except psycopg2.Error as e:
                    conn.rollback()
                    logger.error(f"Error saving pressure data for test_id {test_id}: {str(e)}", exc_info=True)
                    raise

    def save_plot(self, test_id: int, plot_type: str, plot_title: str, plot_path: str) -> None:
        """
        Save a plot to the database.

        Args:
            test_id (int): The ID of the test.
            plot_type (str): Type of the plot.
            plot_title (str): Title of the plot.
            plot_path (str): Path to the plot file.

        Raises:
            FileNotFoundError: If plot_path does not exist.
            psycopg2.Error: If a database error occurs.
        """
        if not os.path.exists(plot_path):
            raise FileNotFoundError(f"Plot file not found: {plot_path}")
        with self._get_connection() as conn:
            with conn.cursor() as cur:
                try:
                    with open(plot_path, 'rb') as file:
                        plot_data = file.read()
                    cur.execute("""
                        INSERT INTO test_plots (test_id, plot_type, plot_title, plot_data)
                        VALUES (%s, %s, %s, %s)
                    """, (test_id, plot_type, plot_title, psycopg2.Binary(plot_data)))
                    conn.commit()
                    logger.info(f"Successfully saved plot '{plot_title}' for test_id: {test_id}")
                except psycopg2.Error as e:
                    conn.rollback()
                    logger.error(f"Error saving plot for test_id {test_id}: {str(e)}", exc_info=True)
                    raise

    def save_temperature_analysis(self, test_id: int, analysis_results: pd.DataFrame) -> None:
        """
        Save temperature analysis results to the database.

        Args:
            test_id (int): The ID of the test.
            analysis_results (pd.DataFrame): DataFrame containing analysis results.

        Raises:
            ValueError: If analysis_results is not a DataFrame or test_id is invalid.
            psycopg2.Error: If a database error occurs.
        """
        if not isinstance(analysis_results, pd.DataFrame):
            raise ValueError("analysis_results must be a DataFrame")
        with self._get_connection() as conn:
            with conn.cursor() as cur:
                try:
                    # Verify test_id exists
                    cur.execute("SELECT test_id FROM test_data WHERE test_id = %s", (test_id,))
                    if not cur.fetchone():
                        raise ValueError(f"Test ID {test_id} does not exist in test_data")
                    analysis_data = analysis_results.to_dict(orient='index')
                    cur.execute("""
                        INSERT INTO temperature_analysis (test_id, analysis_data)
                        VALUES (%s, %s)
                        ON CONFLICT (test_id) DO UPDATE
                        SET analysis_data = EXCLUDED.analysis_data,
                            created_at = CURRENT_TIMESTAMP
                    """, (test_id, Json(analysis_data)))
                    conn.commit()
                    logger.info(f"Successfully saved temperature analysis for test_id: {test_id}")
                except (psycopg2.Error, ValueError) as e:
                    conn.rollback()
                    logger.error(f"Error saving temperature analysis for test_id {test_id}: {str(e)}", exc_info=True)
                    raise

    def save_report(self, test_id: int, pdf_path: str) -> bool:
        try:
            with open(pdf_path, 'rb') as file:
                pdf_data = file.read()

            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            cur.execute("""
                INSERT INTO test_reports (test_id, report_data)
                VALUES (%s, %s)
            """, (test_id, psycopg2.Binary(pdf_data)))

            conn.commit()
            return True
        except Exception as e:
            print(f"Error saving report: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

    def get_test_data(self, test_no: str) -> Optional[Dict]:
        """
        Retrieve test data by test number.

        Args:
            test_no (str): The test number.

        Returns:
            Optional[Dict]: Test data dictionary or None if not found.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT * FROM test_data WHERE test_no = %s", (test_no,))
                    result = cur.fetchone()
                    if result:
                        columns = [desc[0] for desc in cur.description]
                        test_data = dict(zip(columns, result))
                        # Standardize JSONB fields
                        for key in ['pressure_relations', 'basic_info', 'system_specs', 'performance_data',
                                    'propellant_specs', 'catalyst_specs', 'component_details', 'test_details',
                                    'pump_operation', 'suction_valve_operation', 'vacuum_creation_in_tank_valve_on',
                                    'vacuum_creation_in_tank_valve_off', 'heater_info', 'heater_cycles', 'firing_valve_operation',
                                    'post_test_observations', 'catalyst_post_analysis', 'propellant_post_analysis',
                                    'ri_table', 'test_authorization']:
                            if key in test_data and test_data[key]:
                                if isinstance(test_data[key], str):
                                    try:
                                        test_data[key] = json.loads(test_data[key])
                                    except json.JSONDecodeError:
                                        test_data[key] = {}
                                elif not isinstance(test_data[key], (dict, list)):
                                    test_data[key] = {}
                            else:
                                test_data[key] = {} if key != 'heater_cycles' else []
                        return test_data
                    logger.debug(f"No test data found for test_no: {test_no}")
                    return None
        except psycopg2.Error as e:
            logger.error(f"Error retrieving test data for test_no {test_no}: {str(e)}", exc_info=True)
            raise

    def get_temperature_data(self, test_id: int) -> Optional[pd.DataFrame]:
        """
        Retrieve temperature data for a test.

        Args:
            test_id (int): The ID of the test.

        Returns:
            Optional[pd.DataFrame]: Temperature data DataFrame or None if not found.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT time_data, temperature_data FROM temperature_data WHERE test_id = %s", (test_id,))
                    result = cur.fetchone()
                    if result:
                        time_data, temp_data = result
                        df = pd.DataFrame({'time': time_data})
                        for col, values in temp_data.items():
                            df[col] = values
                        return df
                    logger.debug(f"No temperature data found for test_id: {test_id}")
                    return None
        except psycopg2.Error as e:
            logger.error(f"Error retrieving temperature data for test_id {test_id}: {str(e)}", exc_info=True)
            raise

    def get_pressure_data(self, test_id: int) -> Optional[pd.DataFrame]:
        """
        Retrieve pressure data for a test.

        Args:
            test_id (int): The ID of the test.

        Returns:
            Optional[pd.DataFrame]: Pressure data DataFrame or None if not found.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT time_data, pressure_data FROM pressure_data WHERE test_id = %s", (test_id,))
                    result = cur.fetchone()
                    if result:
                        time_data, pressure_data = result
                        df = pd.DataFrame({'time': time_data})
                        for col, values in pressure_data.items():
                            df[col] = values
                        return df
                    logger.debug(f"No pressure data found for test_id: {test_id}")
                    return None
        except psycopg2.Error as e:
            logger.error(f"Error retrieving pressure data for test_id {test_id}: {str(e)}", exc_info=True)
            raise

    def get_all_test_numbers(self) -> List[int]:
        """
        Get all test numbers from the database.

        Returns:
            List[int]: List of test numbers, empty if none found.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT test_no FROM test_data ORDER BY test_no")
                    results = cur.fetchall()
                    test_numbers = [r[0] for r in results]
                    if not test_numbers:
                        logger.debug("No test numbers found in test_data table")
                    return test_numbers
        except psycopg2.Error as e:
            logger.error(f"Error retrieving test numbers: {str(e)}", exc_info=True)
            raise

    # def get_test_plots(self, test_id: int, preview_callback: Optional[callable] = None) -> List[Dict]:
    #     """
    #     Retrieve plots for a test and save them to temporary files.
    #
    #     Args:
    #         test_id (int): The ID of the test.
    #         preview_callback (Optional[callable]): Callback function to update plot preview in GUI.
    #
    #     Returns:
    #         List[Dict]: List of plot info dictionaries with type, title, and path, empty if none found.
    #
    #     Raises:
    #         psycopg2.Error: If a database error occurs.
    #     """
    #     try:
    #         with tempfile.TemporaryDirectory() as temp_dir:
    #             with self._get_connection() as conn:
    #                 with conn.cursor() as cur:
    #                     cur.execute("""
    #                         SELECT plot_type, plot_title, plot_data
    #                         FROM test_plots
    #                         WHERE test_id = %s
    #                         ORDER BY created_at
    #                     """, (test_id,))
    #                     results = cur.fetchall()
    #                     plots = []
    #                     for plot_type, plot_title, plot_data in results:
    #                         temp_path = os.path.join(temp_dir, f"{plot_title}.png")
    #                         with open(temp_path, 'wb') as f:
    #                             f.write(plot_data)
    #                         plot_info = {'type': plot_type, 'title': plot_title, 'path': temp_path}
    #                         plots.append(plot_info)
    #                         if preview_callback:
    #                             try:
    #                                 preview_callback(plot_info)
    #                             except Exception as e:
    #                                 logger.error(f"Error updating preview for plot {plot_title}: {str(e)}")
    #                     if not plots:
    #                         logger.debug(f"No plots found for test_id: {test_id}")
    #                     return plots
    #     except psycopg2.Error as e:
    #         logger.error(f"Error retrieving plots for test_id {test_id}: {str(e)}", exc_info=True)
    #         raise

    def get_test_plots(self, test_id: int, preview_callback=None) -> List[Dict]:
        """
        Retrieve plots for a test and save them to temporary files.

        Args:
            test_id: The ID of the test
            preview_callback: Optional callback function to update plot preview in GUI
        """
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = """
                SELECT plot_type, plot_title, plot_data
                FROM test_plots
                WHERE test_id = %s
                ORDER BY created_at
            """
            cur.execute(query, (test_id,))
            results = cur.fetchall()

            plots = []
            # Create temporary directory for plots if needed
            self.temp_dir = os.path.join(os.getcwd(), 'temp_plots')
            os.makedirs(self.temp_dir, exist_ok=True)

            for plot_type, plot_title, plot_data in results:
                # Create a temporary file for the plot
                temp_filename = f"{plot_title}.png"
                temp_path = os.path.join(self.temp_dir, temp_filename)

                # Write binary data to file
                with open(temp_path, 'wb') as f:
                    f.write(plot_data)

                # Create a standardized plot info dictionary
                plot_info = {
                    'type': plot_type,
                    'title': plot_title,
                    'path': temp_path
                }
                plots.append(plot_info)

                # If preview callback is provided, update the preview
                if preview_callback:
                    try:
                        preview_callback(plot_info)
                    except Exception as e:
                        print(f"Error updating preview for plot {plot_title}: {str(e)}")

            return plots

        except Exception as e:
            print(f"Error retrieving plots from database: {str(e)}")
            traceback.print_exc()
            return []
        finally:
            if conn:
                conn.close()

    def get_test_photos(self, test_id: int) -> Dict[str, str]:
        """Retrieve photos for a test and save them to temporary files."""
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = """
                SELECT photo_type, photo_data
                FROM test_photos
                WHERE test_id = %s
            """
            cur.execute(query, (test_id,))
            results = cur.fetchall()

            photos = {}
            # Create temporary directory for photos if needed
            temp_dir = os.path.join(os.getcwd(), 'temp_photos')
            os.makedirs(temp_dir, exist_ok=True)

            for photo_type, photo_data in results:
                if photo_data:
                    # Create a temporary file for the photo
                    temp_filename = f"{photo_type}_{test_id}.png"
                    temp_path = os.path.join(temp_dir, temp_filename)

                    # Write binary data to file
                    with open(temp_path, 'wb') as f:
                        f.write(photo_data)

                    photos[photo_type] = temp_path
                    print(f"Successfully retrieved photo {photo_type} for test_id: {test_id}")

            return photos

        except Exception as e:
            print(f"Error retrieving photos from database: {str(e)}")
            traceback.print_exc()
            return {}
        finally:
            if conn:
                conn.close()

    def get_test_report(self, test_id: int, database_source) -> Optional[str]:
        try:
            conn = psycopg2.connect(**DatabaseConfig.get_connection_params(database_version=database_source))
            cur = conn.cursor()

            cur.execute("SELECT report_data FROM test_reports WHERE test_id = %s ORDER BY created_at DESC LIMIT 1",
                        (test_id,))
            result = cur.fetchone()

            if result:
                temp_dir = tempfile.mkdtemp()
                pdf_path = os.path.join(temp_dir, f"test_{test_id}_report.pdf")

                with open(pdf_path, 'wb') as file:
                    file.write(result[0])

                return pdf_path
            return None
        finally:
            if conn:
                conn.close()

    def get_temperature_analysis(self, test_id: int) -> Optional[pd.DataFrame]:
        """
        Retrieve temperature analysis data for a test.

        Args:
            test_id (int): The ID of the test.

        Returns:
            Optional[pd.DataFrame]: Analysis data DataFrame or None if not found.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT analysis_data FROM temperature_analysis WHERE test_id = %s", (test_id,))
                    result = cur.fetchone()
                    if result:
                        return pd.DataFrame.from_dict(result[0], orient='index')
                    logger.debug(f"No temperature analysis found for test_id: {test_id}")
                    return None
        except psycopg2.Error as e:
            logger.error(f"Error retrieving temperature analysis for test_id {test_id}: {str(e)}", exc_info=True)
            raise

    def get_filtered_temp_data_from_performance(self, test_id: int) -> Optional[pd.DataFrame]:
        """
        Extract filtered temperature data from performance_data.

        Args:
            test_id (int): The ID of the test.

        Returns:
            Optional[pd.DataFrame]: Filtered temperature data DataFrame or full data as fallback.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            temp_data = self.get_temperature_data(test_id)
            if temp_data is None:
                logger.warning(f"No temperature data found for test_id {test_id}")
                return None

            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT performance_data FROM test_data WHERE test_id = %s", (test_id,))
                    result = cur.fetchone()
                    if result and result[0] and 'filtered_temp_data' in result[0]:
                        filtered_data = result[0]['filtered_temp_data']
                        if isinstance(filtered_data, dict) and 'time_data' in filtered_data and 'temperature_data' in filtered_data:
                            try:
                                df = pd.DataFrame({'time': filtered_data['time_data']})
                                for col, values in filtered_data['temperature_data'].items():
                                    df[col] = values
                                return df
                            except Exception as e:
                                logger.error(f"Failed to process filtered data for test_id {test_id}: {str(e)}")
                        elif isinstance(filtered_data, dict) and 'selected_columns' in filtered_data and 'selected_ranges' in filtered_data:
                            try:
                                selected_columns = filtered_data['selected_columns']
                                selected_ranges = filtered_data['selected_ranges']
                                mask = pd.Series(False, index=temp_data.index)
                                for start, end in selected_ranges:
                                    mask |= ((temp_data['time'] >= start) & (temp_data['time'] <= end))
                                filtered_df = temp_data[mask]
                                columns_to_use = ['time'] + selected_columns
                                available_columns = [col for col in columns_to_use if col in filtered_df.columns]
                                return filtered_df[available_columns]
                            except Exception as e:
                                logger.error(f"Error reconstructing filtered data for test_id {test_id}: {str(e)}")
                    logger.info(f"Returning full temperature data for test_id {test_id}")
                    return temp_data
        except psycopg2.Error as e:
            logger.error(f"Error retrieving filtered temperature data for test_id {test_id}: {str(e)}", exc_info=True)
            raise

    def compare_tests(self, test_no1: str, test_no2: str, fields_to_compare: Optional[List[str]] = None) -> Dict:
        """
        Compare two tests and return differences.

        Args:
            test_no1 (str): First test number.
            test_no2 (str): Second test number.
            fields_to_compare (Optional[List[str]]): Specific fields to compare (default: all except excluded).

        Returns:
            Dict: Dictionary of differences between the tests.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            test1 = self.get_test_data(test_no1)
            test2 = self.get_test_data(test_no2)
            if not test1 or not test2:
                logger.error(f"Could not retrieve test data for test_no1: {test_no1} or test_no2: {test_no2}")
                return {}

            differences = {}
            fields = fields_to_compare or [
                k for k in test1.keys() if k not in ['test_id', 'created_at', 'test_no', 'test_date']
            ]
            for key in fields:
                value1 = test1.get(key)
                value2 = test2.get(key)
                if isinstance(value1, dict) and isinstance(value2, dict):
                    diff = {k: {'test1': value1.get(k), 'test2': value2.get(k)}
                            for k in set(value1) | set(value2) if value1.get(k) != value2.get(k)}
                    if diff:
                        differences[key] = diff
                elif value1 != value2:
                    differences[key] = {'test1': value1, 'test2': value2}

            # Compare plots
            try:
                test1_plots = self.get_test_plots(test1['test_id'])
                test2_plots = self.get_test_plots(test2['test_id'])
                plots_summary1 = [{'title': p['title'], 'type': p['type']} for p in test1_plots]
                plots_summary2 = [{'title': p['title'], 'type': p['type']} for p in test2_plots]
                if plots_summary1 != plots_summary2:
                    differences['plots'] = {'test1': plots_summary1, 'test2': plots_summary2}
            except Exception as e:
                logger.error(f"Error comparing plots: {str(e)}")

            return differences
        except psycopg2.Error as e:
            logger.error(f"Error comparing tests {test_no1} and {test_no2}: {str(e)}", exc_info=True)
            raise

    def filter_tests(self, params):
        try:
            conn = psycopg2.connect(**self.connection_params)
            cur = conn.cursor()

            query = """
            SELECT test_no, test_date, basic_info, propellant_specs
            FROM test_data
            """
            conditions = []

            if params.get('propellantConc'):
                value = float(params.get('propellantConc', 0))
                conditions.append(
                    f"CAST(propellant_specs->>'Concentration before testing (%)' AS float) BETWEEN {value - 1} AND {value + 1}")

            if params.get('testNo'):
                conditions.append(f"test_no = {int(params['testNo'])}")

            if params.get('catalystName'):
                catalyst_name = params['catalystName'].replace("'", "''")  # Escape single quotes
                conditions.append(f"basic_info->>'Catalyst' ILIKE '%{catalyst_name}%'")

            if params.get('tankTemp'):
                temp_value = float(params.get('tankTemp', 0))
                conditions.append(f"""
                    EXISTS (
                        SELECT 1 FROM jsonb_array_elements(heater_cycles) AS hc
                        WHERE CAST(hc->>'tank_bottom_temp' AS float) BETWEEN {temp_value - 1} AND {temp_value + 1}
                    )
                """)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            query += " ORDER BY test_no"

            cur.execute(query)
            results = cur.fetchall()
            formatted_results = []

            for row in results:
                test_no, test_date, basic_info, propellant_specs = row
                try:
                    propellant_conc = float(propellant_specs.get('Concentration before testing (%)', 0))
                except (ValueError, TypeError):
                    propellant_conc = 0

                formatted_results.append({
                    'test_no': test_no,
                    'test_date': test_date.strftime('%Y-%m-%d') if test_date else '',
                    'catalyst_name': basic_info.get('Catalyst', ''),
                    'propellant_conc': propellant_conc
                })

            return formatted_results

        except Exception as e:
            print(f"Error in filter_tests: {str(e)}")
            print("Traceback:", traceback.format_exc())
            return []
        finally:
            if conn:
                conn.close()

    def delete_temp_plots_folder(self):
        # Cleanup temp directory
        try:
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception as e:
            print(f"Error cleaning up temp directory: {str(e)}")

    def load_test_data(self, test_id: int) -> Optional[Dict]:
        """
        Load test data and associated photos from the database.

        Args:
            test_id (int): The ID of the test.

        Returns:
            Optional[Dict]: Test data dictionary or None if not found.

        Raises:
            psycopg2.Error: If a database error occurs.
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT * FROM test_data WHERE test_id = %s", (test_id,))
                    result = cur.fetchone()
                    if result:
                        columns = [desc[0] for desc in cur.description]
                        test_data = dict(zip(columns, result))
                        print(f"The test data for test_id {test_id} is: {test_data}")
                        # Standardize JSONB fields
                        for key in ['pressure_relations', 'basic_info', 'system_specs', 'performance_data',
                                    'propellant_specs', 'catalyst_specs', 'component_details', 'test_details',
                                    'pump_operation', 'suction_valve_operation', 'vacuum_creation_in_tank_valve_on',
                                    'vacuum_creation_in_tank_valve_off', 'heater_info', 'heater_cycles', 'firing_valve_operation',
                                    'post_test_observations', 'catalyst_post_analysis', 'propellant_post_analysis',
                                    'ri_table', 'test_authorization']:
                            if key in test_data and test_data[key]:
                                if isinstance(test_data[key], str):
                                    try:
                                        test_data[key] = json.loads(test_data[key])

                                        print(f"Successfully parsed JSON for key '{key}' in test_id {test_id}")
                                        print(f"Parsed data: {test_data[key]}")
                                    except json.JSONDecodeError:
                                        test_data[key] = {}
                                elif not isinstance(test_data[key], (dict, list)):
                                    test_data[key] = {}
                            else:
                                test_data[key] = {} if key != 'heater_cycles' else []

                        # Extract performance data
                        performance_data = test_data.get('performance_data', {})
                        system_performance = {
                            'vacuum_pressure_lower_limit': performance_data.get('vacuum_pressure_lower_limit'),
                            'vacuum_pressure_upper_limit': performance_data.get('vacuum_pressure_upper_limit'),
                            'chamber_pressure_lower_limit': performance_data.get('chamber_pressure_lower_limit'),
                            'chamber_pressure_upper_limit': performance_data.get('chamber_pressure_upper_limit')
                        }
                        test_data['system_performance'] = system_performance

                        # Get photos
                        photos = self.get_test_photos(test_id)
                        if photos:
                            test_data['photos'] = photos

                        # Get temperature analysis
                        temp_analysis = self.get_temperature_analysis(test_id)
                        if temp_analysis is not None:
                            test_data['temperature_analysis'] = temp_analysis

                        return test_data
                    logger.debug(f"No test data found for test_id: {test_id}")
                    return None
        except psycopg2.Error as e:
            logger.error(f"Error loading test data for test_id {test_id}: {str(e)}", exc_info=True)
            raise

    def get_unique_values(self, column_name):
        """
        Get unique values for a specific column from the database.
        
        Args:
            column_name (str): The column name to get unique values for
            
        Returns:
            list: List of unique values
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cur:
                    if column_name == 'catalyst_name':
                        # Looking at how filter_tests() queries catalyst names
                        query = "SELECT DISTINCT basic_info->>'Catalyst' FROM test_data WHERE basic_info->>'Catalyst' IS NOT NULL AND basic_info->>'Catalyst' != ''"
                        cur.execute(query)
                        results = [row[0] for row in cur.fetchall()]
                        
                        # Log the results for debugging
                        logger.debug(f"Found {len(results)} unique catalyst names: {results}")
                        return results
                    elif column_name == 'propellant_conc':
                        # Looking at how filter_tests() queries propellant concentration
                        query = "SELECT DISTINCT CAST(propellant_specs->>'Concentration before testing (%)' AS float) FROM test_data WHERE propellant_specs->>'Concentration before testing (%)' IS NOT NULL"
                        cur.execute(query)
                        return [row[0] for row in cur.fetchall()]
                    else:
                        query = f"SELECT DISTINCT {column_name} FROM test_data WHERE {column_name} IS NOT NULL"
                        cur.execute(query)
                        return [row[0] for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Error getting unique values for {column_name}: {str(e)}", exc_info=True)
            return []