from PySide6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit,
                              QPushButton, QMessageBox, QApplication)
from PySide6.QtCore import Qt
import sys
import psycopg2
from .database_config import DatabaseConfig

class DatabaseLoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.password = None
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("Database Login")
        self.setModal(True)
        self.setMinimumWidth(300)
        
        layout = QVBoxLayout(self)
        
        # Info label
        info_label = QLabel("Enter PostgreSQL Database Password")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.returnPressed.connect(self.accept_login)  # Allow Enter key to trigger login
        layout.addWidget(self.password_edit)
        
        # Login button
        self.login_button = QPushButton("Connect")
        self.login_button.clicked.connect(self.accept_login)
        layout.addWidget(self.login_button)
        
        self.setLayout(layout)

    def accept_login(self):
        """Validate the password by testing actual database connection."""
        self.password = self.password_edit.text().strip()

        if not self.password:
            QMessageBox.warning(self, "Error", "Please enter the password")
            return

        # Disable the button and show loading state
        self.login_button.setEnabled(False)
        self.login_button.setText("Connecting...")

        # Test database connection with the entered password
        try:
            # Set the password in the configuration
            DatabaseConfig.set_database_password(self.password)

            # Test connection to at least one database
            connection_successful = False
            error_messages = []

            # Try to connect to both EM1 and EM2 databases
            for db_version in ["EM1", "EM2"]:
                try:
                    # Test server configuration first
                    params = DatabaseConfig.get_connection_params(is_server=True, database_version=db_version)
                    success, message = DatabaseConfig.test_connection(params)

                    if success:
                        connection_successful = True
                        break
                    else:
                        error_messages.append(f"{db_version} Server: {message}")

                        # If server fails, try client configuration
                        params = DatabaseConfig.get_connection_params(is_server=False, database_version=db_version)
                        success, message = DatabaseConfig.test_connection(params)

                        if success:
                            connection_successful = True
                            break
                        else:
                            error_messages.append(f"{db_version} Client: {message}")

                except Exception as e:
                    error_messages.append(f"{db_version}: {str(e)}")

            if connection_successful:
                # Password is correct, accept the login
                self.accept()
            else:
                # Show detailed error message
                error_text = "Database connection failed. Please check your password.\n\nDetails:\n" + "\n".join(error_messages)
                QMessageBox.warning(self, "Connection Error", error_text)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to test database connection: {str(e)}")
            print(f"Database connection test error: {str(e)}")

        finally:
            # Re-enable the button and restore original text
            self.login_button.setEnabled(True)
            self.login_button.setText("Connect")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = DatabaseLoginDialog()
    dialog.exec()
    print(dialog.password)