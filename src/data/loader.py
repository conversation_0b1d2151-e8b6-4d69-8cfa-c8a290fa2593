import os
import pandas as pd
from typing import Optional
from ..utils.helpers import clean_file_path

from sklearn.linear_model import LinearRegression




class DataLoader:
    """
    Handles loading and initial processing of temperature and pressure data files.

    Attributes:
        temp_data (pd.DataFrame): Loaded temperature data
        pressure_data (pd.DataFrame): Loaded pressure data
    """

    def __init__(self, mainwindow):
        super().__init__()
        self.ui = mainwindow.ui
        self.temp_data: Optional[pd.DataFrame] = None
        self.pressure_data: Optional[pd.DataFrame] = None
        self.latest_file_path: str = ""
        self.atmospheric_pressure = 1013  # Local atmospheric pressure during the test

    @staticmethod
    def preprocess_temperature_data(file_path: str) -> Optional[pd.DataFrame]:
        """
        Preprocess temperature data from CSV file with single time column at leftmost position.
        Removes duplicate time columns.

        Args:
            file_path (str): Path to original CSV file

        Returns:
            Optional[pd.DataFrame]: Processed DataFrame with clean column structure
        """
        try:
            # Clean file path
            clean_path = clean_file_path(file_path)

            if not os.path.exists(clean_path):
                print(f"Error: File not found at {clean_path}")
                return None

            # Read the data portion of CSV, skipping metadata
            df = pd.read_csv(clean_path, skiprows=6) # Excluding Metadata

            # Clean column names
            df.columns = df.columns.str.strip()

            # Ensure Sample column exists and is numeric
            if 'Sample' not in df.columns:
                df = df.rename(columns={df.columns[0]: 'Sample'})
            df['Sample'] = pd.to_numeric(df['Sample'], errors='coerce')

            # First read metadata separately
            metadata = {}
            scan_rate = 2 # Default Scan rate
            with open(clean_path, 'r') as file:
                for i in range(6):  # Read first 6 lines as metadata
                    line = file.readline().strip()
                    if ':' in line:
                        key, value = line.split(':', 1)
                        metadata[key.strip()] = value.strip()
                        if 'Scan Rate' in key:
                            try:
                                scan_rate = int(value.split(',')[0].split('"')[0].strip())
                            except ValueError:
                                print(f"Warning: Invalid scan rate value: {value}")
                                scan_rate = 2

            df['time'] = df['Sample'] / scan_rate

            # Get temperature columns - explicitly exclude all time-related columns
            temp_cols = [col for col in df.columns if col not in ['Sample', 'time', 'Time (s)', 'Time', 'Date/Time', 'Time/Date']]

            # Process temperature columns
            for col in temp_cols:
                # Handle both comma and period decimal separators
                if df[col].dtype == object:
                    df[col] = df[col].astype(str).str.replace(',', '.')
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # Reorder columns: single time column first, then temperature columns
            # This removes Sample and any other time-related columns
            df = df[['time'] + temp_cols]

            # Save processed data
            output_dir = os.path.dirname(clean_path)
            base_name = os.path.splitext(os.path.basename(clean_path))[0]
            processed_path = os.path.join(output_dir, f"{base_name}_processed.csv")
            df.to_csv(processed_path, index=False)

            return df

        except Exception as e:
            print(f"Error preprocessing temperature data: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def load_temperature_data(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        Load and process temperature data from CSV file
        """
        try:
            # First preprocess the data
            processed_df = self.preprocess_temperature_data(file_path)
            if processed_df is None:
                return None

            # Store the processed data
            self.temp_data = processed_df

            # Process succeeded
            print("Temperature data loaded successfully")
            return processed_df

        except Exception as e:
            print(f"Error loading temperature file: {str(e)}")
            return None

    def load_pressure_data(self, file_path: str, is_absolute: bool = False) -> Optional[pd.DataFrame]:
        """
        Load pressure data from DAT file and perform initial calculations.

        Args:
            dat_file_path (str): Path to the DAT file
            output_dir (str): Directory to save processed data

        Returns:
            Optional[pd.DataFrame]: Processed pressure data or None if error occurs
        """
        # Remove quotes and clean paths
        clean_path = clean_file_path(file_path)

        clean_path_extension = os.path.splitext(clean_path)[1]

        output_directory = os.path.dirname(file_path)

        if clean_path_extension == '.dat':

            # Read the DAT file
            with open(clean_path, 'r') as file:
                lines = file.readlines()

            # Find data start
            data_start = 0
            for i, line in enumerate(lines):
                if 'Time' in line:
                    data_start = i
                    header_line: str = line.strip()
                    break

            # Process the data
            # column_names = [name.strip() for name in header_line.split() if name.strip()]
            column_names = [name.strip().lower() if name.strip().lower() == "time" else name.strip() for name in
                            header_line.split() if name.strip()]
            data = []
            for line in lines[data_start + 1:]:
                try:
                    values = [float(val) for val in line.strip().split() if val.strip()]
                    if values:
                        data.append(values)
                except ValueError as e:
                    return None

            # Creating DataFrame
            self.pressure_data = pd.DataFrame(data, columns=column_names)

            if ('y0' and 'y1' and 'y2') in self.pressure_data.columns:
                if is_absolute:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data['y0'] * 1000

                else:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data[
                                                                               'y0'] * 1000 + self.atmospheric_pressure

                self.pressure_data['Tank Pressure'] = self.pressure_data['y1'] * 1000
                self.pressure_data['Thruster Chamber Pressure'] = self.pressure_data['y2'] * 1000

                # Setting up linear regression equation for y0 = mv0 + b
                y0_column = self.pressure_data[['y0']]
                v0_column = self.pressure_data[['v0']]

                y0_column = y0_column.values.ravel()
                v0_column = v0_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v0_column, y0_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 - {abs(b)}")
                else:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 + {b}")

                # Setting up linear regression equation for y1 = mv1 + b
                y1_column = self.pressure_data[['y1']]
                v1_column = self.pressure_data[['v1']]

                y1_column = y1_column.values.ravel()
                v1_column = v1_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v1_column, y1_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 - {abs(b)}")
                else:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 + {b}")

                # Setting up linear regression equation for y2 = mv2 + b
                y2_column = self.pressure_data[['y2']]
                v2_column = self.pressure_data[['v2']]

                y2_column = y2_column.values.ravel()
                v2_column = v2_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v2_column, y2_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY2PressureRelation.setText(f"y2 = {m}v2 - {abs(b)}")
                else:
                    self.ui.lnEdtY2PressureRelation.setText(f"y2 = {m}v2 + {b}")

            elif 'y2' not in self.pressure_data.columns:
                if is_absolute:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data['y0'] * 1000

                else:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data[
                                                                      'y0'] * 1000 + self.atmospheric_pressure

                self.pressure_data['Tank Pressure'] = self.pressure_data['y1'] * 1000

                # Setting up linear regression equation for y0 = mv0 + b
                y0_column = self.pressure_data[['y0']]
                v0_column = self.pressure_data[['v0']]

                y0_column = y0_column.values.ravel()
                v0_column = v0_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v0_column, y0_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 - {abs(b)}")
                else:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 + {b}")

                # Setting up linear regression equation for y1 = mv1 + b
                y1_column = self.pressure_data[['y1']]
                v1_column = self.pressure_data[['v1']]

                y1_column = y1_column.values.ravel()
                v1_column = v1_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v1_column, y1_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 - {abs(b)}")
                else:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 + {b}")

            # Saving processed data as CSV
            csv_filename = os.path.basename(clean_path).replace('.dat', '_processed.csv')
            csv_path = os.path.join(output_directory, csv_filename)
            self.pressure_data.to_csv(csv_path, index=False)

        # elif clean_path_extension == '.xlsx':
        #
        #     # Reading the Excel file
        #     data_file = pd.ExcelFile(clean_path, engine='openpyxl')
        #
        #     # Initializing an empty DataFrame to store the combined data
        #     combined_df = pd.DataFrame()
        #
        #     # Iterate through the sheets (if there are multiple)
        #     for sheet_name in data_file.sheet_names:
        #         df = pd.read_excel(clean_path, sheet_name=sheet_name, header=None, engine='openpyxl')
        #
        #         # Find the row where the header 'Time' is located
        #         for row_number in range(len(df)):
        #             if 'Time' in df.iloc[row_number].values:
        #                 header_row = row_number
        #                 break
        #
        #         # Read the Excel file from the header row onwards
        #         df = pd.read_excel(clean_path, sheet_name=sheet_name, skiprows=header_row, engine='openpyxl')
        #
        #         # Clean column names
        #         df.columns = df.columns.str.strip()
        #
        #         # Process temperature columns
        #         for col in df.columns:
        #             # Handle both comma and period decimal separators
        #             if df[col].dtype == object:
        #                 df[col] = df[col].astype(str).str.replace(',', '.')
        #             df[col] = pd.to_numeric(df[col], errors='coerce')
        #
        #         # Append the processed DataFrame to the combined DataFrame
        #         combined_df = pd.concat([combined_df, df], ignore_index=True)
        #
        #     self.pressure_data = combined_df

        # For Excel files, process the pressure data with is_absolute parameter
        if clean_path_extension == '.xlsx':
            if ('y0' and 'y1' and 'y2') in self.pressure_data.columns:
                if is_absolute:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data['y0'] * 1000
                else:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data[
                                                                               'y0'] * 1000 + self.atmospheric_pressure

                self.pressure_data['Tank Pressure'] = self.pressure_data['y1'] * 1000
                self.pressure_data['Thruster Chamber Pressure'] = self.pressure_data['y2'] * 1000

                # Setting up linear regression equation for y0 = mv0 + b
                y0_column = self.pressure_data[['y0']]
                v0_column = self.pressure_data[['v0']]

                y0_column = y0_column.values.ravel()
                v0_column = v0_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v0_column, y0_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 - {abs(b)}")
                else:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 + {b}")

                # Setting up linear regression equation for y1 = mv1 + b
                y1_column = self.pressure_data[['y1']]
                v1_column = self.pressure_data[['v1']]

                y1_column = y1_column.values.ravel()
                v1_column = v1_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v1_column, y1_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 - {abs(b)}")
                else:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 + {b}")

                # Setting up linear regression equation for y2 = mv2 + b
                y2_column = self.pressure_data[['y2']]
                v2_column = self.pressure_data[['v2']]

                y2_column = y2_column.values.ravel()
                v2_column = v2_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v2_column, y2_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY2PressureRelation.setText(f"y2 = {m}v2 - {abs(b)}")
                else:
                    self.ui.lnEdtY2PressureRelation.setText(f"y2 = {m}v2 + {b}")

            elif 'y2' not in self.pressure_data.columns:
                if is_absolute:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data['y0'] * 1000
                else:
                    self.pressure_data['Vac. Chamber Pressure'] = self.pressure_data[
                                                                               'y0'] * 1000 + self.atmospheric_pressure
                self.pressure_data['Tank Pressure'] = self.pressure_data['y1'] * 1000

                # Setting up linear regression equation for y0 = mv0 + b
                y0_column = self.pressure_data[['y0']]
                v0_column = self.pressure_data[['v0']]

                y0_column = y0_column.values.ravel()
                v0_column = v0_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v0_column, y0_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 - {abs(b)}")
                else:
                    self.ui.lnEdtY0PressureRelation.setText(f"y0 = {m}v0 + {b}")

                # Setting up linear regression equation for y1 = mv1 + b
                y1_column = self.pressure_data[['y1']]
                v1_column = self.pressure_data[['v1']]

                y1_column = y1_column.values.ravel()
                v1_column = v1_column.values.reshape(-1, 1)

                model = LinearRegression()
                model.fit(v1_column, y1_column)

                m = model.coef_[0].round(4)
                b = model.intercept_.round(4)

                if b < 0:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 - {abs(b)}")
                else:
                    self.ui.lnEdtY1PressureRelation.setText(f"y1 = {m}v1 + {b}")

        return self.pressure_data


