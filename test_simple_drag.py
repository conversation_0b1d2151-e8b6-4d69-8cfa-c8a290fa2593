#!/usr/bin/env python3
"""
Simple test for draggable annotations using the same approach as the main application.
"""

import matplotlib.pyplot as plt
import numpy as np

# Test the draggable annotation functionality
def test_draggable():
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Create some sample data
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    ax.plot(x, y, 'b-', linewidth=2, label='Sample Data')
    
    # Create an annotation with xytext (like valve annotations)
    annotation = ax.annotate('DRAGGABLE\nTEST\n5.0s',
                           xy=(5, 0),
                           xytext=(3, 0.5),
                           arrowprops=dict(arrowstyle='->', color='green', lw=2),
                           fontsize=10, fontweight='bold', color='green',
                           ha='center', va='bottom',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))
    
    # Make it pickable
    annotation.set_picker(True)
    
    # Simple drag functionality
    press = None
    
    def on_pick(event):
        nonlocal press
        if event.artist == annotation:
            print("Annotation picked!")
            current_pos = annotation.xytext
            press = (current_pos, event.mouseevent.x, event.mouseevent.y)
    
    def on_motion(event):
        nonlocal press
        if press is None:
            return
        
        try:
            (x0, y0), xpress, ypress = press
            dx = event.x - xpress
            dy = event.y - ypress
            
            # Convert canvas pixels to data coordinates
            trans = ax.transData.inverted()
            dxa, dya = trans.transform((dx, dy)) - trans.transform((0, 0))
            
            # Update annotation position
            new_x = x0 + dxa
            new_y = y0 + dya
            annotation.xytext = (new_x, new_y)
            
            fig.canvas.draw_idle()
            print(f"Moving to: ({new_x:.2f}, {new_y:.2f})")
        except Exception as e:
            print(f"Error in motion: {e}")
    
    def on_release(event):
        nonlocal press
        if press is not None:
            print("Released!")
        press = None
        fig.canvas.draw_idle()
    
    # Connect events
    fig.canvas.mpl_connect('pick_event', on_pick)
    fig.canvas.mpl_connect('motion_notify_event', on_motion)
    fig.canvas.mpl_connect('button_release_event', on_release)
    
    ax.set_xlabel('Time (s)')
    ax.set_ylabel('Value')
    ax.set_title('Draggable Annotation Test - Click and drag the green box')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    print("Test created. Click and drag the green annotation box.")
    plt.show()

if __name__ == "__main__":
    test_draggable()
