#!/usr/bin/env python3
"""
Test script to verify the draggable annotations functionality.
This script creates a simple matplotlib plot with draggable annotations
to test the implementation before integrating with the main application.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PySide6.QtCore import Qt


class DraggableAnnotation:
    """
    A class to make matplotlib annotations draggable.
    """
    def __init__(self, annotation, canvas, main_window=None):
        self.annotation = annotation
        self.canvas = canvas
        self.main_window = main_window
        self.press = None
        self.original_position = annotation.get_position()
        self.position_key = None
        self.connect()

    def connect(self):
        """Connect to matplotlib events"""
        self.cid_press = self.canvas.mpl_connect('button_press_event', self.on_press)
        self.cid_release = self.canvas.mpl_connect('button_release_event', self.on_release)
        self.cid_motion = self.canvas.mpl_connect('motion_notify_event', self.on_motion)

    def disconnect(self):
        """Disconnect from matplotlib events"""
        for cid in (self.cid_press, self.cid_release, self.cid_motion):
            self.canvas.mpl_disconnect(cid)

    def on_press(self, event):
        """Handle mouse press events"""
        if (event.inaxes != self.annotation.axes or event.button != 1):
            return
        contains, _ = self.annotation.contains(event)
        if contains:
            self.press = (self.annotation.get_position(), event.x, event.y)

    def on_motion(self, event):
        """Handle mouse motion events"""
        if self.press is None or event.inaxes != self.annotation.axes:
            return
        (x0, y0), xpress, ypress = self.press
        dx = event.x - xpress
        dy = event.y - ypress
        
        # Convert canvas pixels to axes fraction
        trans = self.annotation.axes.transAxes.inverted()
        dxa, dya = trans.transform((dx, dy)) - trans.transform((0, 0))
        
        # Update annotation position
        new_x = max(0, min(1, x0 + dxa))
        new_y = max(0, min(1, y0 + dya))
        self.annotation.set_position((new_x, new_y))
        self.canvas.draw_idle()

    def on_release(self, event):
        """Handle mouse release events"""
        if self.press is not None and self.position_key and self.main_window:
            current_pos = self.annotation.get_position()
            if hasattr(self.main_window, 'annotation_positions'):
                self.main_window.annotation_positions[self.position_key] = current_pos
        
        self.press = None
        self.canvas.draw_idle()

    def reset_position(self):
        """Reset annotation to its original position"""
        self.annotation.set_position(self.original_position)
        self.canvas.draw_idle()


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Draggable Annotations Test")
        self.setGeometry(100, 100, 800, 600)
        
        # Initialize annotation tracking
        self.draggable_annotations = []
        self.annotation_positions = {}
        self.annotation_drag_mode = True
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create control buttons
        controls_layout = QHBoxLayout()
        
        self.toggle_button = QPushButton("Disable Dragging")
        self.toggle_button.clicked.connect(self.toggle_drag_mode)
        controls_layout.addWidget(self.toggle_button)
        
        self.reset_button = QPushButton("Reset Positions")
        self.reset_button.clicked.connect(self.reset_positions)
        controls_layout.addWidget(self.reset_button)
        
        self.status_label = QLabel("Drag mode: ON - Click and drag the annotation boxes")
        controls_layout.addWidget(self.status_label)
        
        layout.addLayout(controls_layout)
        
        # Create matplotlib figure and canvas
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Create the test plot
        self.create_test_plot()
    
    def create_test_plot(self):
        """Create a test plot with draggable annotations"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        # Create some sample data
        x = [0, 1, 2, 3, 4, 5]
        y = [0, 1, 4, 9, 16, 25]
        ax.plot(x, y, 'b-', linewidth=2, label='Sample Data')
        
        # Add some vertical lines to simulate valve events
        ax.axvline(x=1.5, color='green', linestyle='--', alpha=0.7, label='Event Start')
        ax.axvline(x=3.5, color='red', linestyle='--', alpha=0.7, label='Event End')
        
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('Value')
        ax.set_title('Test Plot with Draggable Annotations')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Clear existing draggable annotations
        for draggable in self.draggable_annotations:
            draggable.disconnect()
        self.draggable_annotations.clear()
        
        # Create draggable annotations
        self.create_draggable_annotations(ax)
        
        self.canvas.draw()
    
    def create_draggable_annotations(self, ax):
        """Create draggable annotations for the test plot"""
        # Annotation 1: Event Start
        start_key = 'event_start'
        start_pos = self.annotation_positions.get(start_key, (0.2, 0.8))
        
        start_annotation = ax.annotate('EVENT\nSTART\n1.5s',
                                     xy=(1.5, 2.25),
                                     xytext=start_pos,
                                     textcoords='axes fraction',
                                     arrowprops=dict(arrowstyle='->', color='green', lw=2),
                                     fontsize=10, fontweight='bold', color='green',
                                     ha='center', va='center',
                                     bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))
        
        # Annotation 2: Event End
        end_key = 'event_end'
        end_pos = self.annotation_positions.get(end_key, (0.8, 0.8))
        
        end_annotation = ax.annotate('EVENT\nEND\n3.5s',
                                   xy=(3.5, 12.25),
                                   xytext=end_pos,
                                   textcoords='axes fraction',
                                   arrowprops=dict(arrowstyle='->', color='red', lw=2),
                                   fontsize=10, fontweight='bold', color='red',
                                   ha='center', va='center',
                                   bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.7))
        
        # Annotation 3: Duration Info
        duration_key = 'duration_info'
        duration_pos = self.annotation_positions.get(duration_key, (0.5, 0.2))
        
        duration_annotation = ax.annotate('Duration: 2.0 sec',
                                        xy=(2.5, 6.25),
                                        xytext=duration_pos,
                                        textcoords='axes fraction',
                                        fontsize=9, fontweight='bold', color='blue',
                                        ha='center', va='center',
                                        bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
        
        # Create draggable objects
        draggable_start = DraggableAnnotation(start_annotation, self.canvas, self)
        draggable_end = DraggableAnnotation(end_annotation, self.canvas, self)
        draggable_duration = DraggableAnnotation(duration_annotation, self.canvas, self)
        
        # Set position keys
        draggable_start.position_key = start_key
        draggable_end.position_key = end_key
        draggable_duration.position_key = duration_key
        
        # Store draggable annotations
        self.draggable_annotations.extend([draggable_start, draggable_end, draggable_duration])
        
        # Enable/disable based on current mode
        if not self.annotation_drag_mode:
            for draggable in self.draggable_annotations:
                draggable.disconnect()
    
    def toggle_drag_mode(self):
        """Toggle drag mode on/off"""
        self.annotation_drag_mode = not self.annotation_drag_mode
        
        if self.annotation_drag_mode:
            for draggable in self.draggable_annotations:
                draggable.connect()
            self.toggle_button.setText("Disable Dragging")
            self.status_label.setText("Drag mode: ON - Click and drag the annotation boxes")
        else:
            for draggable in self.draggable_annotations:
                draggable.disconnect()
            self.toggle_button.setText("Enable Dragging")
            self.status_label.setText("Drag mode: OFF - Annotations are fixed")
    
    def reset_positions(self):
        """Reset all annotations to their original positions"""
        for draggable in self.draggable_annotations:
            draggable.reset_position()
        self.annotation_positions.clear()
        self.status_label.setText("All positions reset to default")


def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()

    print("Test window created. Try the following:")
    print("1. Click 'Disable Dragging' to toggle drag mode")
    print("2. When drag mode is ON, click and drag the colored annotation boxes")
    print("3. Use 'Reset Positions' to restore original positions")

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
