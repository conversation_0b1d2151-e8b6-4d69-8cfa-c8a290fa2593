import json
import shutil
import sys
import tempfile
import traceback

import numpy as np
import psycopg2
import os

from numpy import astype

# Import our color utility functions
from color_utils import assign_color
from color_manager import color_manager
from typing import Dict, List
import pandas as pd
from PySide6.QtCore import Qt, QSize, QTime, QPoint, QTimer, QEasingCurve, QDate, QEvent
from PySide6.QtGui import QColor, QPixmap, QIcon, QAction, QKeySequence, QCursor
from PySide6.QtWidgets import QApplication, QWidget, QMainWindow, QMessageBox, QFileDialog, QVBoxLayout, \
    QTableWidgetItem, QHBoxLayout, QPushButton, QDialog, QLabel, QScrollArea, QTabWidget, QInputDialog, QSizePolicy, \
    QToolBar, QFrame, QTimeEdit, QSpacerItem
from matplotlib import pyplot as plt
from matplotlib.patches import Rectangle
from gui import Ui_VaprIdexMainWindow, MultiStateToggleSwitch, CustomSplashScreen, HoverButton
from gui.dialog import CustomNavigationToolbar, DataVisualizationWidget, ReportPreviewDialog, \
    TemperatureDataConfigDialog, ExistingDataLoadDialog, PhotoPreviewDialog, Ui_Form
from src.database import DatabaseHandler, DatabaseConfig, MultiDatabaseHandler
from src.analysis import (TemperatureAnalyzer, PressureAnalyzer, Performance,
                         HeaterAnalyzer, ThermalAnalyzer, EfficiencyAnalyzer, ComparisonAnalyzer)
from src.database.database_login import DatabaseLoginDialog
from src.report_generation import TestReportGenerator, ImageHandler
from src.report_generation.setup_fonts import setup_fonts
from src.visualization import PlotManager, ComparisonPlotManager
from src.data import DataLoader
from gui.widgets import TemperatureSelectionWidget, PlotCanvas
from src.utils import get_resource_path, safe_float, InitThread
from user_authentication import UserAuth, VerificationDialog
from src.animation import SectionAnimation
from color_manager import color_manager
from user_authentication.auth import LoginDialog, UserManagementDialog, PasswordResetDialog, logger
from data_recovery import AutoSaver
from src.features import DraggableAnnotation
from src.features.annotation_manager import AnnotationManager
from gui.dialog.annotation_dialog import AnnotationEditDialog


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # Set up main UI components
        self._initialize_ui()

        # Initializing the Existing test data Dialog FIRST
        self.existing_data_dialog = ExistingDataLoadDialog()

        # Handle Section Animation
        self._initialize_section_animation()

        # Initialize core components for Data loading and Plotting
        self._initialize_core_components()

        # Set up data structures for data storage and state tracking
        self._initialize_data_structures()

        # Setting up connection for the section names in the left panel and the associated sub-section
        self._initialize_section_connections()

        self.ui.lblCurentSection.setText('Load Data')

        # Defining photo widgets mapping for small preview
        self._initialize_photo_widgets_mapping()

        # Connecting photo selection buttons
        self.setup_photo_connections()

        # Setting up all the icons from the resources folder
        self.setup_icons()

        # Setting up scroll area for the plot preview in plots-included window
        self._initialize_plot_preview()

        # Setting up the plot initialization
        self._initialize_plot()

        # Database initialization for Data Visualization
        self._initialize_Database()

        # Initializing Auto Data saver
        self.auto_saver = AutoSaver(self)

        # Add a timer to force enable the plots button after the application starts
        QTimer.singleShot(2000, self.force_enable_plots_button)

        # Initializing required properties or connections
        self.setup_connections()

        # Initialize the valve n heater annotation toggle
        self.create_valve_annotation_toggle()
        self.create_heater_annotation_toggle()

        # Setting the Minimum value of field - Propellant RI after test
        self.ui.subLnEdtPropRIBefFirg_2.setSpecialValueText("NA")
        self.ui.subLnEdtPropRIAftFirg_2.setSpecialValueText("NA")
        self.ui.subLnEdtPropRI.setSpecialValueText("NA")

        # Setting the Minimum value of field - NA
        self.ui.subLnEdtChmbrDept.setSpecialValueText("NA")
        self.ui.subLnEdtInternalChmbrDia.setSpecialValueText("NA")
        self.ui.subLnEdtExternalChmbrDia.setSpecialValueText("NA")
        self.ui.subLnEdtNozlThrtDime.setSpecialValueText("NA")
        self.ui.subLnEdtRetainerPltOrfcDia.setSpecialValueText("NA")
        self.ui.subLnEdtInjectorOrificeDia.setSpecialValueText("NA")
        self.ui.subLnEdtConcBefTest.setSpecialValueText("NA")
        self.ui.subLnEdtWghtOfPropBefTest.setSpecialValueText("NA")
        self.ui.subLnEdtWghtOfPropAftTest.setSpecialValueText("NA")
        self.ui.subLnEdtCatWghtBefTest.setSpecialValueText("NA")
        self.ui.subLnEdtHtrInpPowerHtr_1.setSpecialValueText("NA")
        self.ui.subLnEdtHtrInpPowerHtr_2.setSpecialValueText("NA")
        self.ui.subLnEdtHtrInpPowerHtr_3.setSpecialValueText("NA")
        self.ui.subLnEdtHtrInpPowerHtr_4.setSpecialValueText("NA")
        self.ui.subLnEdtHtrInpPowerHtr_5.setSpecialValueText("NA")
        self.ui.subLnEdtPropTnkHtrCtOfTemp.setSpecialValueText("NA")
        self.ui.subLnEdtPropTnkHtrRstTemp.setSpecialValueText("NA")
        self.ui.subLnEdtCorrTkBtmTemp.setSpecialValueText("NA")
        self.ui.subLnEdtCorrThrstrTemp.setSpecialValueText("NA")
        self.ui.subLnEdtCorrTkPressure.setSpecialValueText("NA")
        self.ui.subLnEdtCorrThrstrPressure.setSpecialValueText("NA")
        self.ui.subLnEdtSuctnCorrTkBtmTemp.setSpecialValueText("NA")
        self.ui.subLnEdtSuctnCorrThrstrTemp.setSpecialValueText("NA")
        self.ui.subLnEdtSuctnCorrTkPressure.setSpecialValueText("NA")
        self.ui.subLnEdtSuctnCorrThrstrPressure.setSpecialValueText("NA")
        self.ui.subLnEdtVacValveOffCorrTkBtmTemp.setSpecialValueText("NA")
        self.ui.subLnEdtVacValveOffCorrThrstrTemp.setSpecialValueText("NA")
        self.ui.subLnEdtVacValveOffCorrTkPressure.setSpecialValueText("NA")
        self.ui.subLnEdtVacValveOnCorrTkBtmTemp.setSpecialValueText("NA")
        self.ui.subLnEdtVacValveOnCorrThrstrTemp.setSpecialValueText("NA")
        self.ui.subLnEdtVacValveOnCorrTkPressure.setSpecialValueText("NA")
        self.ui.subLnEdtVacValveOnPressureDropInTank.setSpecialValueText("NA")
        self.ui.subLnEdtHtrInpVoltage.setSpecialValueText('NA')
        self.ui.subLnEdtHtrInpCurrent.setSpecialValueText('NA')
        self.ui.subLnEdtHtrInpWattage.setSpecialValueText('NA')
        self.ui.subLnEdtHtrCtOfTemp.setSpecialValueText('NA')
        self.ui.subLnEdtHtrRstTemp.setSpecialValueText('NA')
        for i in range(1, 5):
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtONCorspgTankPressure').setSpecialValueText('NA')
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtONCorspgThrusterPressure').setSpecialValueText('NA')
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOFFCorspgTankPressure').setSpecialValueText('NA')
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOFFCorspgThrusterPressure').setSpecialValueText('NA')
            getattr(self.ui, f'cyc{i}SubLnEdtMaxTemp').setSpecialValueText('NA')
            getattr(self.ui, f'cyc{i}SubLnEdtCorrespgTankBottomTemp').setSpecialValueText('NA')
            getattr(self.ui, f'cyc{i}SubLnEdtCorrespgValveTemp').setSpecialValueText('NA')

        self.ui.subLnEdtChmbrLen_2.setSpecialValueText('NA')
        self.ui.subLnEdtChmbrIntDia_2.setSpecialValueText('NA')
        self.ui.subLnEdtChmbrExtDia_2.setSpecialValueText('NA')
        self.ui.subLnEdtCatWghtFild.setSpecialValueText('NA')
        self.ui.subLnEdtCatWghtRecvrd.setSpecialValueText('NA')
        self.ui.subLnEdtCatLosPerc.setSpecialValueText('NA')
        self.ui.subLnEdtPropWghtFild_2.setSpecialValueText('NA')
        self.ui.subLnEdtPropWghtRecvrd_2.setSpecialValueText('NA')
        self.ui.subLnEdtPropUsedPerc_2.setSpecialValueText('NA')
        self.ui.subLnEdtPropRIBefFirg_2.setSpecialValueText('NA')
        self.ui.subLnEdtPropRIAftFirg_2.setSpecialValueText('NA')
        self.ui.subLnEdtFirgDur_2.setSpecialValueText('NA')
        self.ui.subLnEdtApproxMassFlowRate_2.setSpecialValueText('NA')

        # Blocking the plots section initially
        self.ui.btnPlots.setEnabled(False)

        # Set place holder for the search bar tab for database table
        self.ui.search_tab.setPlaceholderText("Filter tests...")

    def _initialize_Database(self):
        """Initialize database-related UI elements without connecting to database."""
        self.db_handler = None
        self.multi_db_handler = None

    def initialize_database_connection(self):
        login_dialog = DatabaseLoginDialog(self)
        if login_dialog.exec() == QDialog.Accepted:
            try:
                DatabaseConfig.set_database_password(login_dialog.password)
                # Initialize both single and multi-database handlers
                self.db_handler = DatabaseHandler()
                self.multi_db_handler = MultiDatabaseHandler()

                # Test connections to both databases
                connection_results = self.multi_db_handler.test_connections()

                # Check if at least one database is accessible
                successful_connections = [db for db, (success, _) in connection_results.items() if success]

                if successful_connections:
                    self.mode_toggle.update_modes(["New Test", "Existing Test", "Database"])
                    self.load_all_records()
                    self.database_button_action.setVisible(False)

                    # Show connection status
                    status_messages = []
                    for db_version, (success, message) in connection_results.items():
                        status = "✓" if success else "✗"
                        status_messages.append(f"{status} {db_version}: {message}")

                    QMessageBox.information(self, "Database Connection Status",
                                          "Database connections:\n" + "\n".join(status_messages))
                    print("Database connections established")
                else:
                    error_messages = []
                    for db_version, (success, message) in connection_results.items():
                        if not success:
                            error_messages.append(f"{db_version}: {message}")

                    QMessageBox.critical(self, "Connection Error",
                                       "Failed to connect to any database:\n" + "\n".join(error_messages))
                    print("All database connections failed")

            except psycopg2.Error as e:
                QMessageBox.critical(self, "Error", f"Database connection error: {str(e)}")
                print(f"Database connection error: {str(e)}", exc_info=True)
                self.db_handler = None
                self.multi_db_handler = None
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to initialize database: {str(e)}")
                print(f"Database initialization failed: {str(e)}", exc_info=True)
                self.db_handler = None
                self.multi_db_handler = None
        else:
            print("Login dialog cancelled")

    def _initialize_plot(self):
        # Add temperature selection widget to left panel
        self.temp_selection_widget = TemperatureSelectionWidget()
        self.ui.tempMatrixDataSelection.layout().addWidget(self.temp_selection_widget)

        # Connect selection change signal
        self.temp_selection_widget.dataSelectionChanged.connect(
            self.update_temperature_plot
        )

        # Connect order change signal
        self.temp_selection_widget.orderChanged.connect(
            self.handle_temperature_order_changed
        )

        # Linking the back to Plot button in the temperature matrix window
        self.ui.btnBckToPlot.clicked.connect(lambda: self.ui.contentStack.setCurrentWidget(self.ui.plots))

        # Use centralized color manager's color dictionary for random color assignment
        self.color = color_manager.color  # Direct reference to centralized color dictionary

        # Add a method to get color for a column using centralized manager
        self.get_column_color = lambda column: color_manager.get_color(column)

        self.ui.pressurePlotBtnFrame.hide()

        # Initially hide the pressure relation display
        self.ui.lnEdtY0PressureRelation.hide()
        self.ui.lnEdtY1PressureRelation.hide()
        self.ui.lnEdtY2PressureRelation.hide()

        # Initializing tab widget for pressure plot
        self.tab_widget = None
        self.current_tab_name = ""

        # Linking the Plot title change to plot refresh
        self.ui.lnEditPlotTitle.editingFinished.connect(self.plot_with_title)

        # Initialize figure and axes
        self.figure = None
        self.axes = None
        self.canvas = None

        # Initialize figure and axes for selected range pressure plot
        self.figure_selected = None
        self.axes_selected = None
        self.canvas_selected = None
        self.axhline = None

    def _initialize_plot_preview(self):
        # Setting up scroll area for plot previews
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(10)
        scroll_layout.setContentsMargins(10, 10, 10, 10)
        scroll_layout.setAlignment(Qt.AlignTop)
        self.ui.scrollAreaWidgetContents.setLayout(scroll_layout)

        # Setting minimum width for scroll area content to prevent horizontal scrolling
        self.ui.scrollAreaWidgetContents.setMinimumWidth(200)

        # Enabling vertical scrolling
        self.ui.scrlAreaReportPreview.setWidgetResizable(True)
        self.ui.scrlAreaReportPreview.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.ui.scrlAreaReportPreview.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

    def _initialize_photo_widgets_mapping(self):
        self.photo_widgets = {
            'prop_before': {
                'widget': self.ui.widgetPropPhotoBef_2,
                'line_edit': self.ui.subLnEdtPropPhtoBfr_2,
                'button': self.ui.btnPropPhotoBef_2,
                'preview': self.ui.lblPropPhotoPrevBef_2,
                'label': "Photo of Propellant (before test)"
            },
            'prop_after': {
                'widget': self.ui.widgetPropPhotoAft_2,
                'line_edit': self.ui.subLnEdtPropPhtoAft_2,
                'button': self.ui.btnPropPhotoAft_2,
                'preview': self.ui.lblPropPhotoPrevAft_2,
                'label': "Photo of Propellant (after test)"
            },
            'cat_before': {
                'widget': self.ui.widgetCatPhotoBef_2,
                'line_edit': self.ui.subLnEdtCatPhtoBfr_2,
                'button': self.ui.btnCatPhotoBef_2,
                'preview': self.ui.lblCatPhotoPrevBef_2,
                'label': "Photo of Catalyst (before test)"
            },
            'cat_after': {
                'widget': self.ui.widgetCatPhotoAft_2,
                'line_edit': self.ui.subLnEdtCatPhtoAft_2,
                'button': self.ui.btnCatPhotoAft_2,
                'preview': self.ui.lblCatPhotoPrevAft_2,
                'label': "Photo of Catalyst (after test)"
            }
        }

    def _initialize_section_connections(self):
        # Mapping for contentStack pages to left panel buttons
        self.page_button_mapping = {
            'basicInformation': (self.ui.btnTestPrereq, self.ui.btnBasicInfo),
            'systemSpecification': (self.ui.btnTestPrereq, self.ui.btnSysSpec),
            'propellantSpecification': (self.ui.btnTestPrereq, self.ui.btnPropSpec),
            'catalystSpecification': (self.ui.btnTestPrereq, self.ui.btnCatSpec),
            'componentDetails': (self.ui.btnTestPrereq, self.ui.btnCompDet),
            'testDetails': (self.ui.btnTestPrereq, self.ui.btnTestDet),
            'pumpOperation': (self.ui.btnTestPrereq, self.ui.btnPumpOperation),
            'suctionValveOperation': (self.ui.btnTestPrereq, self.ui.btnSuctValveOperation),
            'vacuumCreationInTank': (self.ui.btnTestPrereq, self.ui.btnVacCretnInTank),
            'heaterInformation': (self.ui.btnHtrOp, self.ui.btnHtrInfo),
            'heaterCycles': (self.ui.btnHtrOp, self.ui.btnHtrCyc),
            'valveOperation': (self.ui.btnValveOperation, self.ui.btnValve),
            'postTestingObs': (self.ui.btnPstTestAn, self.ui.btnPstTestObs),
            'catalystPostAna': (self.ui.btnPstTestAn, self.ui.btnCatPostAn),
            'propellantPostAn': (self.ui.btnPstTestAn, self.ui.btnPropPostAn),
            'temperaturematrix': (self.ui.btnPerformance, self.ui.btnTempMatrix),
            'performance': (self.ui.btnPerformance, None),
            'testAuthorization': (self.ui.btnTestAuthorization, None)
        }

        # Mapping for section headers
        self.section_headers = {
            'basicInformation': "Test Prerequisite",
            'systemSpecification': "Test Prerequisite",
            'propellantSpecification': "Test Prerequisite",
            'catalystSpecification': "Test Prerequisite",
            'componentDetails': "Test Prerequisite",
            'testDetails': "Test Prerequisite",
            'pumpOperation': "Test Prerequisite",
            'suctionValveOperation': "Test Prerequisite",
            'vacuumCreationInTank': "Test Prerequisite",
            'heaterInformation': "Heater Operation",
            'heaterCycles': "Heater Operation",
            'valveOperation': "Valve Operation",
            'note': "Heater Operation",
            'postTestingObs': "Post Test Analysis",
            'catalystPostAna': "Post Test Analysis",
            'propellantPostAn': "Post Test Analysis",
            'plots': "Plots",
            'performance': "Performance",
        }

        # Store the default button style
        self.default_button_style = """
                            QPushButton{
	                            background-color: #1e293c;
	                            border-radius: 17px;
	                            border: 1px solid #303030;
	                            padding:5px;
	                            font-size: 19px;
	                            font-family: Helvetica;
	                            font-weight: bold;
                            }

                            QPushButton:hover{
                            	background-color:#47a08e;
                            }

                            QPushButton:pressed{
                            	background-color:black;
                            }
                        """

        self.default_button_style_subsections = """
                            QPushButton{
	                            background-color: #1e1e1e;
	                            color: white;
	                            padding:5px;
	                            font-size:17px;
	                            border:1px solid #446699;
	                            font-family: Arial;
                            }

                            QPushButton:hover{
                            	background-color:#47a08e;
                            }

                            QPushButton:pressed{
                            	background-color:#5C5C5C;
                            }
                        """

        # Store the selected button style
        self.selected_button_style = """
                            QPushButton {
                                background-color: #B03781;
                                border-radius: 10px;
                                padding: 5px;
                                font-size: 17px;
                            }
                            """

    def _initialize_data_structures(self):
        """Initialize data storage and state tracking variables."""
        self.temperature_data = None
        self.pressure_data = None
        self.test_data = {}
        self.current_figure = None
        self.current_canvas = None
        self.current_toolbar = None
        self.plot_container = None
        self.current_plot_type = None
        self.filtered_temp_data = None
        self.selecting = False
        self.start_x = None
        self.rect = None
        self.current_rect = None
        self.selected_ranges = []
        self.selected_cols = []
        self.temp_dir = None

        # Initialize plot tracking
        self.report_plots = {
            'default': [],  # For default temperature plots
            'custom': []    # For custom plots
        }

        # Define a list of bright, visually distinct colors for plotting
        # This is the MASTER color palette that should be used everywhere
        self.color_palette = [
            '#511D43',      # Purple
            '#901E3E',      # Maroon
            '#DC2525',      # Red
            '#9BC09C',      # Light Green
            '#0B1D51',      # Dark Blue
            '#FFC107',      # Yellow
            '#FF7601',      # Orange
            '#4DA8DA',      # Light Blue
            '#16610E',      # Dark Green
            '#A5158C',      # Violet
        ]

        # Use centralized color manager's color dictionary for random color assignment
        self.color = color_manager.color  # Direct reference to centralized color dictionary

        # Store figures for report
        self.current_figures = {}

        # Annotation management
        self.draggable_annotations = []  # List to store all draggable annotations
        self.annotation_drag_mode = True  # Toggle for enabling/disabling drag mode (start enabled)
        self.annotation_positions = {}  # Store custom positions for annotations
        self.annotation_event_handlers = []  # Centralized event handler IDs

        # User annotation manager
        self.annotation_manager = AnnotationManager(self)

    def assign_color(self, column: str) -> str:
        """Assign color using centralized color manager"""
        return color_manager.get_color(column)

    def _initialize_core_components(self):
        """Initialize backend analysis and processing components."""
        self.data_loader = DataLoader(self)
        self.temp_analyzer = TemperatureAnalyzer()
        self.pressure_analyzer = PressureAnalyzer()
        self.heater_analyzer = HeaterAnalyzer()
        self.thermal_analyzer = ThermalAnalyzer()
        self.efficiency_analyzer = EfficiencyAnalyzer()
        self.comparison_analyzer = ComparisonAnalyzer()
        self.plot_manager = PlotManager()
        self.comparison_plot_manager = ComparisonPlotManager()
        self.image_handler = ImageHandler()

        # Setup fonts first
        if not setup_fonts():
            QMessageBox.critical(self, "Error",
                                 "Failed to setup required fonts. Please check your internet connection and try again.")
            sys.exit(1)

        # Then initialize report generator
        try:
            self.report_generator = TestReportGenerator()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to initialize report generator: {str(e)}")
            sys.exit(1)

    def _initialize_ui(self):
        """Initialize and setup the main UI components."""
        self.ui = Ui_VaprIdexMainWindow()
        self.ui.setupUi(self)

        # Setup plot layout
        self.plot_layout = QVBoxLayout()
        self.plot_layout.setContentsMargins(0, 10, 0, 0)
        self.ui.plotFrame.setLayout(self.plot_layout)

        # Adding database to menubar
        self.setup_menu()

        # Adding an app icon
        self.setWindowIcon(QIcon("assets/icon.ico"))

        # Removing the tab bar from the main tabWidget
        self.ui.tabWidget.tabBar().hide()

        # Removing the tab bar from the plot settings in the left panel
        self.ui.tabWidgetPlotSettings.tabBar().hide()

        # Hiding the database viewer initially
        self.ui.databaseViewer.hide()
        self.ui.widget_9.hide()

        # Hiding the test number label in the top bar
        self.ui.lblTestNumber_DataAnalysis_Compare.hide()

    def _initialize_authentication(self):
        """Handle user authentication and authorization."""
        self.auth = UserAuth()
        self.current_user = None

        if not self.show_login():
            return False

        # Update menu to include user management for admin
        if self.current_user and self.current_user.get("role") == "admin":
            self.setup_admin_menu()
        return True

    def _initialize_section_animation(self):
        # Find the scroll area
        self.left_scroll_area = None
        for child in self.ui.leftPanel.children():
            if isinstance(child, QScrollArea):
                self.left_scroll_area = child
                break

        if not self.left_scroll_area:
            print("Warning: Could not find scroll area in left panel")

        # Initializing animations with found scroll area
        self.section_animations = {
            'test_prereq': SectionAnimation(self.ui.testPrereqSubSections, self.left_scroll_area),
            'heater_op': SectionAnimation(self.ui.htrOpSubSections, self.left_scroll_area),
            'valve_op': SectionAnimation(self.ui.valveOpSubSections, self.left_scroll_area),
            'post_test': SectionAnimation(self.ui.pstTestAnSubSections, self.left_scroll_area),
            'plot_controls': SectionAnimation(self.ui.plotControlsFrame, self.left_scroll_area),
            'performance': SectionAnimation(self.ui.perforSubSections, self.left_scroll_area),
        }

        # Setup advanced animation settings
        self.setupAdvancedAnimation()

    def setup_menu(self):
        """Set up menu bar with database operations and floating toggle switch."""
        # Create toolbar for the toggle switch
        self.main_toolbar = QToolBar()
        self.main_toolbar.setMovable(False)
        self.main_toolbar.setFloatable(False)
        self.main_toolbar.setStyleSheet("""
            QToolBar {
                spacing: 0px;
                    border: none;
                    background-color: transparent;
                    padding: 5px;
            }
        """)
        self.addToolBar(Qt.TopToolBarArea, self.main_toolbar)
        self.main_toolbar.hide()  # Hide initially

        # Add spacer to push toggle switch to center
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.main_toolbar.addWidget(spacer)

        # Add toggle switch with two modes only (remove Database mode)
        self.mode_toggle = MultiStateToggleSwitch(["New Test"])
        self.mode_toggle.background_color = QColor(232, 249, 253)
        self.mode_toggle.handle_color = QColor(255, 95, 95)
        self.mode_toggle.text_color = QColor(0, 0, 0)
        self.mode_toggle.modeChanged.connect(self.handle_mode_change)
        self.main_toolbar.addWidget(self.mode_toggle)

        # Add database button to toolbar
        self.database_button = HoverButton(self)
        self.database_button.setIcon(QIcon(get_resource_path("assets/database_icon.png")))
        self.database_button.setToolTip("Connect to Database")
        self.database_button.clicked.connect(self.initialize_database_connection)
        self.database_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }
        """)
        self.database_button_action = self.main_toolbar.addWidget(self.database_button)

        # Add spacer after the toggle switch
        spacer2 = QWidget()
        spacer2.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.main_toolbar.addWidget(spacer2)

        # Setup cursor proximity timer
        self.proximity_timer = QTimer(self)
        self.proximity_timer.setInterval(100)  # Check every 100ms
        self.proximity_timer.timeout.connect(self.check_cursor_proximity)
        self.proximity_timer.start()

        # Setup animation timer for smooth drop-down
        self.animation_timer = QTimer(self)
        self.animation_timer.setInterval(10)
        self.animation_timer.timeout.connect(self.animate_toolbar)

        self.toolbar_y = -self.main_toolbar.height()
        self.target_y = 0
        self.current_y = self.toolbar_y
        self.animating = False

        # Setting up a tab switch slider for plot setting mode change in left panel
        self.mode_plot_settings = MultiStateToggleSwitch(['Temp', 'Presr', 'Both', 'Thrust'], height = 30, width = 60)
        self.mode_plot_settings.modeChanged.connect(self.handle_mode_change)
        self.mode_plot_settings.background_color = QColor(0, 0, 0)
        self.mode_plot_settings.handle_color = QColor(216, 30, 91)

        plot_settings_layout = QHBoxLayout(self.ui.plotSettingsTabSlider)
        plot_settings_layout.addWidget(self.mode_plot_settings)

        self.mode_plot_settings.modeChanged.connect(self.handle_plots_mode_change)

    def check_cursor_proximity(self):
        """Check if the cursor is near the top bar and trigger toolbar visibility."""
        cursor_pos = self.mapFromGlobal(QCursor.pos())
        top_bar_height = 50  # Adjust based on your top bar height
        if cursor_pos.y() <= top_bar_height and not self.animating:
            if self.main_toolbar.isHidden():
                self.target_y = 0
                self.animation_timer.start()
        elif cursor_pos.y() > top_bar_height and not self.animating:
            if self.main_toolbar.isVisible():
                self.target_y = -self.main_toolbar.height()
                self.animation_timer.start()

    def animate_toolbar(self):
        """Animate the toolbar and adjust neighbor elements smoothly without resizing window."""
        if not hasattr(self, 'animation_frame') or self.animation_frame is None:
            self.animation_frame = 0
            self.total_frames = 20  # Number of frames for smooth animation
            self.start_y = self.current_y
            self.animating = True
            # Store initial positions/sizes of neighbor elements
            self.store_neighbor_initial_states()

        if self.animation_frame >= self.total_frames:
            # Animation complete - finalize positions
            self.current_y = self.target_y
            self.animation_timer.stop()
            self.animating = False

            # Final state handling
            if self.current_y <= -self.main_toolbar.height():
                # Completely hidden
                self.main_toolbar.hide()
            else:
                # Visible (fully or partially)
                self.main_toolbar.show()

            # Finalize neighbor element positions
            self.update_neighbor_elements(final=True)
            self.animation_frame = None
            return

        # Linear interpolation for smooth movement
        progress = self.animation_frame / self.total_frames
        self.current_y = self.start_y + (self.target_y - self.start_y) * progress

        # Move toolbar
        self.main_toolbar.move(self.main_toolbar.x(), int(self.current_y))

        # Update neighbor elements to blend smoothly
        self.update_neighbor_elements(progress=progress)

        self.animation_frame += 1

    def store_neighbor_initial_states(self):
        """Store initial positions and sizes of elements that need to adjust."""
        # Example: storing main content area initial state
        if hasattr(self, 'main_content'):
            self.initial_content_y = self.main_content.y()
            self.initial_content_height = self.main_content.height()

        # Store other neighbor elements as needed
        # if hasattr(self, 'status_bar'):
        #     self.initial_status_y = self.status_bar.y()

    def update_neighbor_elements(self, progress=None, final=False):
        """Update neighbor elements to smoothly adjust for toolbar animation."""
        # Apply easing function for smoother animation
        if progress is not None:
            # Use ease-in-out cubic for smoother transitions
            eased_progress = self.ease_in_out_cubic(progress)
        else:
            eased_progress = 1.0

        if final:
            # Final positioning
            if self.current_y <= -self.main_toolbar.height():
                # Toolbar hidden - expand content to fill space
                target_content_y = 0
                target_content_height = self.height() - (self.status_bar.height() if hasattr(self, 'status_bar') else 0)
            else:
                # Toolbar visible - adjust content below toolbar
                toolbar_bottom = self.current_y + self.main_toolbar.height()
                target_content_y = max(0, toolbar_bottom)
                remaining_height = self.height() - target_content_y - (
                    self.status_bar.height() if hasattr(self, 'status_bar') else 0)
                target_content_height = max(0, remaining_height)

            # Apply final positioning with smooth transition
            if hasattr(self, 'main_content'):
                self.smooth_resize_element(self.main_content,
                                           target_content_y, target_content_height, 1.0)
        else:
            # Progressive animation with smooth interpolation
            toolbar_visible_height = max(0, self.main_toolbar.height() + self.current_y)

            # Calculate target positions for smooth interpolation
            if hasattr(self, 'main_content'):
                # Target position when toolbar is fully visible/hidden
                target_y_visible = self.main_toolbar.height()
                target_y_hidden = 0
                target_height_visible = self.height() - self.main_toolbar.height() - (
                    self.status_bar.height() if hasattr(self, 'status_bar') else 0)
                target_height_hidden = self.height() - (self.status_bar.height() if hasattr(self, 'status_bar') else 0)

                # Interpolate between hidden and visible states
                visibility_factor = (self.current_y + self.main_toolbar.height()) / self.main_toolbar.height()
                visibility_factor = max(0, min(1, visibility_factor))

                # Apply easing to visibility factor for smoother resizing
                eased_visibility = self.ease_in_out_cubic(visibility_factor)

                new_content_y = target_y_hidden + (target_y_visible - target_y_hidden) * eased_visibility
                new_content_height = target_height_hidden + (
                            target_height_visible - target_height_hidden) * eased_visibility

                self.smooth_resize_element(self.main_content, new_content_y, new_content_height, eased_progress)

    def ease_in_out_cubic(self, t):
        """Cubic ease-in-out function for smoother animations."""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2

    def smooth_resize_element(self, element, target_y, target_height, progress):
        """Apply smooth resizing with optional momentum for extra smoothness."""
        current_y = element.y()
        current_height = element.height()

        # Interpolate with the provided progress (already eased)
        new_y = current_y + (target_y - current_y) * progress * 0.5  # Damping factor for smoother motion
        new_height = current_height + (target_height - current_height) * progress * 0.5

        element.setGeometry(
            element.x(),
            int(new_y),
            element.width(),
            int(new_height)
        )

############################################################### Initialization End ################################################################
    def handle_temperature_order_changed(self, new_order: List[str]):
        """Handle temperature column reordering"""
        # Update your plots or data structures based on the new order
        self.update_temperature_plot()

    ############ User Authentication ##########################
    def show_login(self) -> bool:
        """Show login dialog and handle authentication"""
        login_dialog = LoginDialog(self.auth, self)
        if login_dialog.exec() == QDialog.Accepted and login_dialog.user_data:
            self.current_user = login_dialog.user_data
            # animation = AnimationDialog(self)
            # animation.play_animation(r"assets/Server Connection.gif")
            return True
        return False

    def setup_admin_menu(self):
        """Admin-specific menu items"""
        admin_menu = self.menuBar().addMenu('Admin')

        # User management action
        manage_users_action = QAction('Manage Users', self)
        manage_users_action.triggered.connect(self.show_user_management)
        admin_menu.addAction(manage_users_action)

        # Change password action
        change_password_action = QAction("Change Password", self)
        change_password_action.triggered.connect(self.show_admin_password_change)
        admin_menu.addAction(change_password_action)

    def show_admin_password_change(self):
        """Show Password reset dialog for admin"""
        if self.current_user and self.current_user.get("email"):
            dialog = PasswordResetDialog(self.auth, self)
            dialog.email_edit.setText(self.current_user["email"])
            dialog.email_edit.setEnabled(False) # Lock email field for admin
            dialog.exec()

    def show_user_management(self):
        """Show user management dialog"""
        if self.current_user["role"] == "admin":
            dialog = UserManagementDialog(self.auth, self)
            if dialog.exec() == QDialog.Accepted:
                # Show verification dialog after adding new user
                self.show_verification_dialog()

    def show_verification_dialog(self):
        """Show dialog for verifying user email"""
        dialog = VerificationDialog(self.auth, self)
        dialog.exec()

    def logout(self):
        """Handle user logout"""
        reply = QMessageBox.question(
            self,
            'Logout',
            'Are you sure you want to logout?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.current_user = None
            self.close()
            # Restart application
            QApplication.instance().exit()

    #####################################################################

    def filter_table(self):
        """Filter the table based on search text."""
        search_text = self.ui.search_tab.text().lower()
        table = self.ui.results_table_2

        for row in range(table.rowCount()):
            match_found = False
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break

            table.setRowHidden(row, not match_found)

    def load_all_records(self):
        """Load all records into the table from both databases"""
        if self.multi_db_handler is None:
            QMessageBox.critical(self, "Error", "Multi-database handler not initialized")
            print("Multi-database handler is None")
            return
        try:
            # Load records from both databases
            results = self.multi_db_handler.filter_tests_from_both_databases({})
            print("Results from both databases:", results)
            self.update_table_2(results)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading records: {str(e)}")
            print(f"Error loading records: {str(e)}")



    def update_table_2(self, results):
        try:
            self.ui.results_table_2.setRowCount(len(results))
            print(f"The results contain; {results}")
            for i, result in enumerate(results):
                logger.debug(f"Processing row {i}: {result}")
                test_no = str(result.get('test_no', ''))
                test_date = str(result.get('test_date', ''))
                catalyst_name = str(result.get('catalyst_name', ''))
                prop_conc = f"{result.get('propellant_conc', 0):.2f}" if result.get(
                    'propellant_conc') is not None else ""
                database_source = str(result.get('database_source', 'EM2'))  # Default to EM2 for backward compatibility
                heater_cut_off_temp = str(result.get('tank_temp', ''))

                # Debug logging for first few rows
                if i < 3:
                    print(f"Row {i}: Test {test_no}, Database source: '{database_source}'")

                self.ui.results_table_2.setItem(i, 0, QTableWidgetItem(test_no))
                self.ui.results_table_2.setItem(i, 1, QTableWidgetItem(test_date))
                self.ui.results_table_2.setItem(i, 2, QTableWidgetItem(catalyst_name))
                self.ui.results_table_2.setItem(i, 3, QTableWidgetItem(prop_conc))
                self.ui.results_table_2.setItem(i, 4, QTableWidgetItem(database_source))
                self.ui.results_table_2.setItem(i, 5, QTableWidgetItem(heater_cut_off_temp))
            self.ui.results_table_2.resizeColumnsToContents()
            logger.info(f"Updated table with {len(results)} records")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error updating table: {str(e)}")
            logger.error(f"Error updating table: {str(e)}", exc_info=True)
            traceback.print_exc()

    def load_selected_test(self):
        """Load the selected test data into the databaseViewer frame."""
        # Check if a row is selected
        selected_rows = self.ui.results_table_2.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select a test to load.")
            return

        # Get the test number and database source from the selected row
        try:
            row_index = selected_rows[0].row()
            test_no = self.ui.results_table_2.item(row_index, 0).text()

            # Get database source from column 4 (safely)
            database_item = self.ui.results_table_2.item(row_index, 4)
            print(f"Database item: {database_item.text() if database_item else 'None'}")
            database_source = database_item.text() if database_item else "EM2"

            # Handle empty database source
            if not database_source or database_source.strip() == "":
                database_source = "EM2"  # Default fallback

            print(f"Loading test {test_no} from row {row_index}")
            print(f"Database item: {database_item}")
            print(f"Database source: '{database_source}'")

        except (IndexError, AttributeError) as e:
            QMessageBox.warning(self, "Error", "Failed to retrieve test information.")
            return

        # Check if databaseViewer already has a layout, reuse it or create a new one
        if self.ui.databaseViewer.layout() is None:
            databaseViewer_layout = QVBoxLayout(self.ui.databaseViewer)
        else:
            databaseViewer_layout = self.ui.databaseViewer.layout()

        # Clear existing widgets in the layout
        while databaseViewer_layout.count():
            item = databaseViewer_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()  # Properly delete the old widget

        # Create and add the new DataVisualizationWidget with database source info
        data_visualization = DataVisualizationWidget(test_no, self, self.ui.databaseViewer, database_source)
        databaseViewer_layout.addWidget(data_visualization)

        # Show the databaseViewer frame
        self.ui.databaseViewer.show()

############################ Filter Window ########################################

    def plot_with_title(self):
        current_plot = self.ui.lblCurentSection.text()

        if current_plot == 'Pressure Plot':
            if self.tab_widget.count() > 1:
                if self.tab_widget.currentIndex() == 0:
                    self.axes_selected.set_title(self.ui.lnEditPlotTitle.text(),
                         color='#09090b',
                         fontsize=12,
                         fontweight='bold')
                    self.canvas_selected.draw()
                elif self.tab_widget.currentIndex() == 1:
                    self.axes.set_title(self.ui.lnEditPlotTitle.text(),
                         color='#09090b',
                         fontsize=12,
                         fontweight='bold')
                    self.canvas.draw()
            else:
                self.axes.set_title(self.ui.lnEditPlotTitle.text(),
                          color='#09090b',
                          fontsize=12,
                          fontweight='bold')
                self.canvas.draw()
        else:
            self.axes.set_title(self.ui.lnEditPlotTitle.text(),
                         color='#09090b',
                         fontsize=12,
                         fontweight='bold')
            self.canvas.draw()

    def setup_icons(self):
        """Setup icons for buttons"""
        try:

            # Set application icon
            app_icon = QIcon()
            app_icon_path = get_resource_path("assets/icon.ico")
            app_icon.addFile(app_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.setWindowIcon(app_icon)
            QApplication.instance().setWindowIcon(app_icon)

            # Set Windows taskbar icon explicitly
            if hasattr(sys, 'frozen'):
                import ctypes
                myappid = 'manastuspace.vapridexanalyzer.1.0'
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)

            # Back button icon for Heater cycles
            back_icon = QIcon()
            back_icon_path = get_resource_path("assets/left_arrow.drawio.png")
            back_icon.addFile(back_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.btnCycleBack.setIcon(back_icon)
            self.ui.btnCycleBack.setIconSize(QSize(70, 70))

            # Next button icon for Heater cycles
            next_icon = QIcon()
            next_icon_path = get_resource_path("assets/right_arrow.drawio.png")
            next_icon.addFile(next_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.btnCycleNext.setIcon(next_icon)
            self.ui.btnCycleNext.setIconSize(QSize(70, 70))

            # Temperature load Icon
            temperature_icon = QIcon()
            temperature_icon_path = get_resource_path("assets/temperature_icon.png")
            temperature_icon.addFile(temperature_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.temp_icon.setIcon(temperature_icon)
            self.ui.temp_icon.setIconSize(QSize(185, 185))
            self.ui.btnTempDataInd.setIcon(temperature_icon)
            self.ui.btnTempDataInd.setIconSize(QSize(45, 45))

            # Pressure load Icon
            pressure_icon = QIcon()
            pressure_icon_path = get_resource_path("assets/pressure_icon.png")
            pressure_icon.addFile(pressure_icon_path, QSize(), QIcon.Mode.Normal, QIcon.State.Off)
            self.ui.pressure_icon.setIcon(pressure_icon)
            self.ui.pressure_icon.setIconSize(QSize(185, 185))
            self.ui.btnPressureDataInd.setIcon(pressure_icon)
            self.ui.btnPressureDataInd.setIconSize(QSize(55, 55))

            # Setting down arrow icon for all combo boxes
            down_arrow_path = get_resource_path("assets/down-arrow.png").replace('\\', '/')
            down_arrow_style = """
                            QComboBox::down-arrow {
                                image: url("%s");
                                width: 24px;
                                height: 24px;
                            }
                            QComboBox::drop-down {
                                border: none;
                                background: transparent;
                                width: 20px;
                                margin-right: 8px;
                            }
                        """ % down_arrow_path

            # Applying the style to all combo boxes
            combo_boxes = [
                self.ui.comboBoxXAxisTemp,
                self.ui.comboBoxYAxisTemp,
                self.ui.comboBoxXAxisPressure,
                self.ui.comboBoxYAxisPressure
            ]

            for combo_box in combo_boxes:
                current_style = combo_box.styleSheet()
                combo_box.setStyleSheet(current_style + down_arrow_style)


        except Exception as e:
            print(f"Error setting up icons: {str(e)}")

    def reset_input_fields(self):
        # Reset temp_data.json file to a blank JSON file
        user_home = os.path.expanduser("~")
        json_path = os.path.join(user_home, "temp_data.json")

        try:
            if os.path.exists(json_path):
                with open(json_path, "w") as temp_json_file:
                    json.dump({}, temp_json_file)
                print(f"{json_path} has been emptied.")

                # Also reset temperature_data and pressure_data to prevent auto-save from creating fallback data
                self.temperature_data = None
                self.pressure_data = None
                self.filtered_temp_data = None
                self.report_plots = {'default': [], 'custom': []}

                # Disable the plots button
                self.ui.btnPlots.setEnabled(False)
                self.ui.plotControlsFrame.hide()

                # Reset the temperature and pressure data indicators
                self.ui.btnTempDataInd.setStyleSheet('''
                QPushButton{
	                background-color: rgba(4, 120, 87, 0.3);
	                padding:5px;
                }

                QPushButton:hover{
                	background-color:#47a08e;
                }

                QPushButton:pressed{
                	background-color:black;
                }''')

                self.ui.btnTempDataLoad.setStyleSheet('''
                QPushButton{
	                background-color: rgba(4, 120, 87, 0.3);
	                padding:5px;
                }

                QPushButton:hover{
                	background-color:#47a08e;
                }

                QPushButton:pressed{
                	background-color:black;
                }''')
                self.ui.btnPressureDataInd.setStyleSheet('''
                QPushButton{
                	background-color:rgba(29, 78, 216, 0.3);
                	padding:5px;
                }

                QPushButton:hover{
                	background-color:#7b92d4;
                }

                QPushButton:pressed{
                	background-color:black;
                }
                ''')
                self.ui.btnPressureDataLoad.setStyleSheet('''
                QPushButton{
                	background-color:rgba(29, 78, 216, 0.3);
                	padding:5px;
                }

                QPushButton:hover{
                	background-color:#7b92d4;
                }

                QPushButton:pressed{
                	background-color:black;
                }
                ''')
                self.ui.btnTempMatrix.setEnabled(False)
            else:
                print(f"{json_path} does not exists in the current directory.")

        except Exception as e:
            print(f"Error resetting temp_data.json: {str(e)}")

        self.ui.lblFiringDuration.clear()
        self.ui.subLnEdtAim.clear()
        self.ui.subLnEdtProp.clear()
        self.ui.subLnEdtPropRI.setValue(0.0)
        self.ui.subLnEdtCat.clear()
        self.ui.subLnEdtTestNo.clear()
        self.ui.subLnEdtTestDate.setDate(QDate.fromString('01/01/2000', 'dd/MM/yyyy'))
        self.ui.subLnEdtChmbrNo.clear()
        self.ui.subLnEdtChmbrMat.clear()
        self.ui.subLnEdtChmbrDept.setValue(0.0)
        self.ui.subLnEdtInternalChmbrDia.setValue(0.0)
        self.ui.subLnEdtExternalChmbrDia.setValue(0.0)
        self.ui.subLnEdtNozlThrtDime.setValue(0.0)
        self.ui.subLnEdtRetainerPltOrfcDia.setValue(0.0)
        self.ui.subLnEdtInjectorOrificeDia.setValue(0.0)
        self.ui.subLnEdtTypeOfProp.clear()
        self.ui.subLnEdtConcBefTest.setValue(0.0)
        self.ui.subLnEdtStability.clear()
        self.ui.subLnEdtWghtOfPropBefTest.setValue(0.0)
        self.ui.subLnEdtWghtOfPropAftTest.setValue(0.0)
        self.ui.subLnEdtCatType.clear()
        self.ui.subLnEdtCatGrade.clear()
        self.ui.subLnEdtCatSize.clear()
        self.ui.subLnEdtCatWghtBefTest.setValue(0.0)
        # self.ui.subLnEdtPrehtTemp.clear()
        # self.ui.subLnEdtPressSensType.setText("Piezo resistive type vacuum pressure transmitter")
        # self.ui.subLnEdtPressSensRange.setText("0~1 bar")
        # self.ui.subLnEdtPressSensIO.setText("\u00b115 VDC, 4-20 mA (0-5 V)")

        self.ui.Vac_Chamb_Pressure_Sensr_type_Input.clear()
        self.ui.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.clear()
        self.ui.Vac_Chamb_Pressure_Snsr_range_Input.clear()
        self.ui.Vac_Chamb_Pressure_Snsr_IO_Input.clear()
        self.ui.Prop_Tank_Pressure_Sensr_type_Input.clear()
        self.ui.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.clear()
        self.ui.Prop_Tank_Pressure_Snsr_range_Input.clear()
        self.ui.Prop_Tank__Pressure_Snsr_IO_Input.clear()
        self.ui.Thruster_Pressure_Sensr_type_Input.clear()
        self.ui.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.clear()
        self.ui.Thruster_Pressure_Snsr_range_Input.clear()
        self.ui.Thruster_Pressure_Snsr_IO_Input.clear()
        self.ui.subLnEdtHtrType_2.clear()
        self.ui.subLnEdtHtrInpPowerHtr_1.setValue(0)
        self.ui.subLnEdtHtrInpPowerHtr_2.setValue(0)
        self.ui.subLnEdtHtrInpPowerHtr_3.setValue(0)
        self.ui.subLnEdtHtrInpPowerHtr_4.setValue(0)
        self.ui.subLnEdtHtrInpPowerHtr_5.setValue(0)
        self.ui.subLnEdtPropTnkHtrCtOfTemp.setValue(0)
        self.ui.subLnEdtPropTnkHtrRstTemp.setValue(0)
        self.ui.subLblTestProcValue.setPlainText(
            'Switch on the heaters on the propellant tank. Let the heater be turned on till the cut-off '
            'temperature of the tank bottom is reached. Switch-off the heater when the cut-off temperature '
            'is reached. Restart the heater when the temperature falls below the reset temperature '
            '(2°C below the cut-off temperature). Repeat this process for 4 cycles. Each cycle is defined as '
            'a tank reaching its cut-off temperature and then re-heating.')

        time = QTime(00, 00)

        # Pump Operation
        self.ui.subLnEdtPmpStrtTime.setTime(time)
        self.ui.subLnEdtCorrTkBtmTemp.setValue(0)
        self.ui.subLnEdtCorrThrstrTemp.setValue(0)
        self.ui.subLnEdtCorrTkPressure.setValue(0)
        self.ui.subLnEdtCorrThrstrPressure.setValue(0)

        # Suction Valve Operation
        self.ui.subLnEdtSuctnValveSwtchOnTime.setTime(time)
        self.ui.subLnEdtSuctnCorrTkBtmTemp.setValue(0)
        self.ui.subLnEdtSuctnCorrThrstrTemp.setValue(0)
        self.ui.subLnEdtSuctnCorrTkPressure.setValue(0)
        self.ui.subLnEdtSuctnCorrThrstrPressure.setValue(0)

        # Vacuum Creation in Tank
        self.ui.subLnEdtVacValveSwtchOnTime.setTime(time)
        self.ui.subLnEdtVacValveOnCorrTkBtmTemp.setValue(0)
        self.ui.subLnEdtVacValveOnCorrThrstrTemp.setValue(0)
        self.ui.subLnEdtVacValveOnCorrTkPressure.setValue(0)
        self.ui.subLnEdtVacValveOnPressureDropInTank.setValue(0)
        self.ui.subLnEdtVacValveSwtchOffTime.setTime(time)
        self.ui.subLnEdtVacValveOffCorrTkBtmTemp.setValue(0)
        self.ui.subLnEdtVacValveOffCorrThrstrTemp.setValue(0)
        self.ui.subLnEdtVacValveOffCorrTkPressure.setValue(0)

        self.ui.subLnEdtHtrType.clear()
        self.ui.subLnEdtHtrInpVoltage.setValue(0)
        self.ui.subLnEdtHtrInpCurrent.setValue(0)
        self.ui.subLnEdtHtrInpWattage.setValue(0)
        self.ui.subLnEdtHtrCtOfTemp.setValue(0)
        self.ui.subLnEdtHtrRstTemp.setValue(0)

        for i in range(1, 5):
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOnTime').setTime(time)
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtONCorspgTankPressure').setValue(0)
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtONCorspgThrusterPressure').setValue(0)
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOffTime').setTime(time)
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOFFCorspgTankPressure').setValue(0)
            getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOFFCorspgThrusterPressure').setValue(0)
            getattr(self.ui, f'cyc{i}SubLnEdtMaxTemp').setValue(0)
            getattr(self.ui, f'cyc{i}SubLnEdtLoc').clear()
            getattr(self.ui, f'cyc{i}SubLnEdtCorrespgTankBottomTemp').setValue(0)
            getattr(self.ui, f'cyc{i}SubLnEdtCorrespgValveTemp').setValue(0)

        # Firing valve operation
        self.ui.subLnEdtValveSwtchOnTime.setTime(time)
        self.ui.subLnEdtValveSwtchOffTime.setTime(time)
        self.ui.subLnEdtValveOnCorrTkBtmTemp.setValue(0)
        self.ui.subLnEdtValveOnCorrPressureTank.setValue(0)
        self.ui.subLnEdtValveOnPressureDrop.setValue(0)
        self.ui.subLnEdtValveOffCorrTankBtmTemp.setValue(0)

        self.ui.subLnEdtChmbrNoPostTestObs.clear()
        self.ui.subLnEdtChmbrLen_2.setValue(0)
        self.ui.subLnEdtChmbrIntDia_2.setValue(0)
        self.ui.subLnEdtChmbrExtDia_2.setValue(0)
        self.ui.subLnEdtRetainerPltCond_2.clear()
        self.ui.subLnEdtCatPhtoBfr_2.setText('No Photo Selected')
        self.ui.subLnEdtCatPhtoAft_2.setText('No Photo Selected')
        self.ui.subLnEdtPropPhtoBfr_2.setText('No Photo Selected')
        self.ui.subLnEdtPropPhtoAft_2.setText('No Photo Selected')
        self.ui.lblCatPhotoPrevBef_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblCatPhotoPrevAft_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblPropPhotoPrevBef_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblPropPhotoPrevAft_2.setStyleSheet(u'background-color:#333333')
        self.ui.lblCatPhotoPrevBef_2.clear()
        self.ui.lblCatPhotoPrevAft_2.clear()
        self.ui.lblPropPhotoPrevBef_2.clear()
        self.ui.lblPropPhotoPrevAft_2.clear()
        self.ui.subLnEdtNote.clear()

        self.ui.subLnEdtCatDet.clear()
        self.ui.subLnEdtCatColBfr.clear()
        self.ui.subLnEdtCatColAft.clear()
        self.ui.subLnEdtCatWghtFild.setValue(0)
        self.ui.subLnEdtCatWghtRecvrd.setValue(0)
        self.ui.subLnEdtCatLosPerc.setValue(0)

        self.ui.subLnEdtPropDet_2.clear()
        self.ui.subLnEdtPropColBef_2.clear()
        self.ui.subLnEdtPropColAft_2.clear()
        self.ui.subLnEdtPropWghtFild_2.setValue(0)
        self.ui.subLnEdtPropWghtRecvrd_2.setValue(0)
        self.ui.subLnEdtPropUsedPerc_2.setValue(0)
        self.ui.subLnEdtPropRIBefFirg_2.setValue(0.0)
        self.ui.subLnEdtPropRIAftFirg_2.setValue(0.0)
        self.ui.subLnEdtValveOprtnTime.setTime(time)
        self.ui.subLnEdtFirgDur_2.setValue(0)
        self.ui.subLnEdtApproxMassFlowRate_2.setValue(0)

        self.ui.subLblPropBefRITable.setText('NA')
        self.ui.subLblPropAftRITable.setText('NA')
        self.ui.subLblPropBefConcTable.setText('NA')
        self.ui.subLblPropAftConcTable.setText('NA')

        self.ui.tableTemperatureAnalysis.clear()

        self.ui.subLnEdtChambPressure.clear()
        self.ui.subLnEdtVacPressure.clear()
        self.ui.subLnEdtChambTemp.clear()
        self.ui.subLnEdtCharVelo.clear()
        self.ui.subLnEdtCoefOfThrust.clear()
        self.ui.subLnEdtBurnTime.clear()
        self.ui.subLnEdtMassFlowRate.clear()
        self.ui.subLnEdtThrust.clear()
        self.ui.subLnEdtSpcImpulse.clear()
        self.ui.subLnEdtTotImpulse.clear()

        self.ui.lblAim.hide()
        self.ui.lblPropellant.hide()
        self.ui.lblCatalyst.hide()
        self.ui.testNoFrame.hide()

        # Resetting indicator color orange
        self.ui.tempMatrixIndicator.setStyleSheet("""
                        background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 0, 0, 255), stop:1 rgba(255, 67, 0, 255));
                        border-radius: 7px;
                        """
                                                  )

        self.ui.lnEdtInitialPropMass.setValue(0)
        self.ui.lnEdtFinalPropMass.setValue(0)
        self.ui.lnEdtChambPressRangeMin.setValue(0)
        self.ui.lnEdtChambPressRangeMax.setValue(0)
        self.ui.lnEdtVacPressRangeMin.setValue(0)
        self.ui.lnEdtVacPressRangeMax.setValue(0)

        self.temp_selection_widget.clear_all_columns()
        self.ui.tempMatrixDataSelection.hide()

        self.ui.comboBoxXAxisTemp.clear()
        self.ui.comboBoxYAxisTemp.clear()
        self.ui.lnEdtXLabelTemp.clear()
        self.ui.lnEdtYLabelTemp.clear()
        self.ui.comboBoxXAxisPressure.clear()
        self.ui.comboBoxYAxisPressure.clear()
        self.ui.lnEdtXLabelPressure.clear()
        self.ui.lnEdtYLabelPressure.clear()
        self.ui.lnEdtRangeMinPressure.setValue(0)
        self.ui.lnEdtRangeMaxPressure.setValue(0)

        self.ui.lnEdtY0PressureRelation.clear()
        self.ui.lnEdtY1PressureRelation.clear()
        self.ui.lnEdtY2PressureRelation.clear()

        self.temperature_data = None
        self.pressure_data = None
        self.test_data = {}

    def setup_photo_connections(self):
        """Connect photo selection buttons to their handlers"""
        try:
            for photo_id, widgets in self.photo_widgets.items():
                # Use lambda with default argument to prevent late binding issues
                widgets['button'].clicked.connect(
                    lambda checked, pid=photo_id: self.select_photo(pid)
                )
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error setting up photo connections: {str(e)}")

    def setup_connections(self):
        """Set up all necessary connections for the application"""
        ## Connecting basic info inputs
        self.ui.subLnEdtTestNo.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtTestNo, 'test_no'))
        self.ui.subLnEdtAim.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtAim, 'aim'))
        self.ui.subLnEdtProp.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtProp, 'propellant'))
        self.ui.subLnEdtCat.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtCat, 'catalyst'))
        self.ui.subLnEdtPropRI.editingFinished.connect(lambda: self.value_changed(self.ui.subLnEdtPropRI, 'prop_RI'))

        ## For Propellant Parameters
        self.ui.lnEdtInitialPropMass.editingFinished.connect(
            lambda: self.value_changed(self.ui.lnEdtInitialPropMass, 'prop_initial'))
        self.ui.subLnEdtWghtOfPropBefTest.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtWghtOfPropBefTest, 'prop_initial'))
        self.ui.subLnEdtPropWghtFild_2.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtPropWghtFild_2, 'prop_initial'))
        self.ui.lnEdtFinalPropMass.editingFinished.connect(
            lambda: self.value_changed(self.ui.lnEdtFinalPropMass, 'prop_final'))
        self.ui.subLnEdtWghtOfPropAftTest.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtWghtOfPropAftTest, 'prop_final'))
        self.ui.subLnEdtPropWghtRecvrd_2.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtPropWghtRecvrd_2, 'prop_final'))

        ## For Catalyst Parameters
        self.ui.subLnEdtCatWghtBefTest.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtCatWghtBefTest, 'cat_initial'))
        self.ui.subLnEdtCatWghtFild.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtCatWghtFild, 'cat_initial'))
        self.ui.subLnEdtCatWghtRecvrd.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtCatWghtRecvrd, 'cat_final'))

        ## Mass flow rate
        self.ui.subLnEdtPropRIAftFirg_2.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtPropRIAftFirg_2, 'mass_flow_rate'))

        ## Burn time
        self.ui.subLnEdtValveSwtchOnTime.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtValveSwtchOnTime, 'burn_time')
        )
        self.ui.subLnEdtValveSwtchOffTime.editingFinished.connect(
            lambda: self.value_changed(self.ui.subLnEdtValveSwtchOffTime, 'burn_time')
        )

        # Connecting data loading buttons
        self.ui.btnTempDataLoad.clicked.connect(self.load_temperature_data)
        self.ui.btnTempDataInd.clicked.connect(self.load_temperature_data)
        self.ui.btnPressureDataLoad.clicked.connect(self.load_pressure_data)
        self.ui.btnPressureDataInd.clicked.connect(self.load_pressure_data)
        self.ui.btnLoadData.clicked.connect(self.refresh_data_files)

        # Connect section buttons to their respective functions
        self.ui.btnTestPrereq.clicked.connect(lambda: self.handle_section_change("Test Prerequisite"))
        self.ui.btnBasicInfo.clicked.connect(lambda: self.update_content(self.ui.basicInformation))
        self.ui.btnSysSpec.clicked.connect(lambda: self.update_content(self.ui.systemSpecification))
        self.ui.btnPropSpec.clicked.connect(lambda: self.update_content(self.ui.propellantSpecification))
        self.ui.btnCatSpec.clicked.connect(lambda: self.update_content(self.ui.catalystSpecification))
        self.ui.btnCompDet.clicked.connect(lambda: self.update_content(self.ui.componentDetails))
        self.ui.btnTestDet.clicked.connect(lambda: self.update_content(self.ui.testDetails))
        self.ui.btnPumpOperation.clicked.connect(lambda: self.update_content(self.ui.pumpOperation))
        self.ui.btnSuctValveOperation.clicked.connect(lambda: self.update_content(self.ui.suctionValveOperation))
        self.ui.btnVacCretnInTank.clicked.connect(lambda: self.update_content(self.ui.vacuumCreationInTank))

        self.ui.btnHtrOp.clicked.connect(lambda: self.handle_section_change("Heater Operation"))
        self.ui.btnHtrInfo.clicked.connect(lambda: self.update_content(self.ui.heaterInformation))
        self.ui.btnHtrCyc.clicked.connect(lambda: self.update_content(self.ui.heaterCycles))

        self.ui.btnValveOperation.clicked.connect(lambda: self.handle_section_change("Valve Operation"))
        self.ui.btnValve.clicked.connect(lambda: self.update_content(self.ui.valveOperation))

        self.ui.btnCycleNext.clicked.connect(lambda: self.change_heater_cycle_tab('next'))
        self.ui.btnCycleBack.clicked.connect(lambda: self.change_heater_cycle_tab('back'))

        self.ui.btnPstTestAn.clicked.connect(lambda: self.handle_section_change("Post Test Analysis"))
        self.ui.btnPstTestObs.clicked.connect(lambda: self.update_content(self.ui.postTestingObs))
        self.ui.btnCatPostAn.clicked.connect(lambda: self.update_content(self.ui.catalystPostAna))
        self.ui.btnPropPostAn.clicked.connect(lambda: self.update_content(self.ui.propellantPostAn))

        self.ui.btnPlots.clicked.connect(lambda: self.handle_section_change("PlotWindow"))

        self.ui.btnPerformance.clicked.connect(lambda: self.handle_section_change("Performance"))

        self.ui.btnTestAuthorization.clicked.connect(
            lambda: self.ui.contentStack.setCurrentWidget(self.ui.testAuthorization))

        # Temperature matrix button
        self.ui.btnTempMatrix.clicked.connect(self.handle_temp_matrix_clicked)

        # Connect plot button
        self.ui.pressurePlotBtn.clicked.connect(self.create_pressure_plot)
        self.ui.tempPlotBtn.clicked.connect(self.create_temperature_plot)
        self.ui.comboBoxYAxisTemp.itemCheckStateChanged.connect(self.create_temperature_plot)
        self.ui.comboBoxYAxisPressure.itemCheckStateChanged.connect(self.create_pressure_plot)
        self.ui.comboBoxY1AxisTemp.itemCheckStateChanged.connect(self.create_temp_n_pressure_plot)
        self.ui.comboBoxY2AxisPressure.itemCheckStateChanged.connect(self.create_temp_n_pressure_plot)

        # Connecting the include in report button to the plot preview and save
        self.ui.btnIncludeInReport.clicked.connect(self.handle_plot_inclusion)

        # Performance Calculation
        self.ui.btnCalculate.clicked.connect(self.calculate_performance)
        self.ui.btnPerformance.clicked.connect(lambda: self.update_content(self.ui.performance))

        # Generate Maximum temperatures plot
        self.ui.btnMaxTempsPlot.clicked.connect(self.generate_max_temperatures_plot)

        # Connecting the Next and Back buttons for Section view change
        self.ui.nextBtn.setShortcut(QKeySequence(Qt.Key_Right))
        self.ui.backBtn.setShortcut(QKeySequence(Qt.Key_Left))
        self.ui.nextBtn.clicked.connect(lambda: self.change_page(next=True))
        self.ui.backBtn.clicked.connect(lambda: self.change_page(next=False))

        # Connecting report generation
        self.ui.showReport.clicked.connect(self.generate_report)

        # Saving data to the database
        self.ui.saveDataToDatabase.clicked.connect(self.save_to_database)

        # Connecting Update table button with RI Concentration table
        self.ui.btnUpdateTable.clicked.connect(self.update_table)

        # Connecting the double click on data from the table to load the test data
        self.ui.results_table_2.doubleClicked.connect(self.load_selected_test)

        # Connecting the search bar tab for the database filter
        self.ui.search_tab.textChanged.connect(self.filter_table)

        # Initially hide plot controls and meta data frame
        self.ui.plotControlsFrame.hide()
        self.ui.htrOpSubSections.hide()
        self.ui.valveOpSubSections.hide()
        self.ui.pstTestAnSubSections.hide()
        self.ui.plotControlsFrame.hide()
        self.ui.pressurePlotSetti.hide()
        self.ui.lblAim.hide()
        self.ui.testNoFrame.hide()
        self.ui.lblPropellant.hide()
        self.ui.lblCatalyst.hide()
        self.ui.perforSubSections.hide()
        self.ui.tempMatrixDataSelection.hide()

        # Connect database icon button to load test data dialog
        self.existing_data_dialog.ui.btnBrowseDatabase.clicked.connect(self.load_test_data)

        # Connect file browser icon button to select the json file
        self.existing_data_dialog.ui.btnBrowseJSONFile.clicked.connect(self.load_data_from_json_file)

        # Create n Connect the valve annotation toggle to the temperature plot creation
        self.valve_annotation_frame_Temp, self.valve_annotation_toggle_Temp = self.create_valve_annotation_toggle()
        self.ui.temperature_tab.layout().insertWidget(1, self.valve_annotation_frame_Temp)
        self.valve_annotation_toggle_Temp.modeChanged.connect(self.create_temperature_plot)

        # Create n Connect the heater annotation toggle to the temperature plot creation
        self.heater_annotation_frame_Temp, self.heater_annotation_toggle_Temp = self.create_heater_annotation_toggle()
        self.ui.temperature_tab.layout().insertWidget(2, self.heater_annotation_frame_Temp)
        self.heater_annotation_toggle_Temp.modeChanged.connect(self.create_temperature_plot)

        # Create n Connect the annotation drag toggle to the temperature plot
        self.annotation_drag_frame_Temp, self.annotation_drag_toggle, self.annotation_reset_button = self.create_annotation_drag_toggle()
        self.ui.temperature_tab.layout().insertWidget(3, self.annotation_drag_frame_Temp)
        # Connect the toggle to the method
        self.annotation_drag_toggle.modeChanged.connect(self.toggle_annotation_drag_mode)

        # Create n Connect the valve annotation toggle for pressure plot creation
        self.valve_annotation_frame_Pressure, self.valve_annotation_toggle_Pressure = self.create_valve_annotation_toggle()
        self.ui.pressure_tab.layout().insertWidget(1, self.valve_annotation_frame_Pressure)
        self.valve_annotation_toggle_Pressure.modeChanged.connect(self.create_pressure_plot)

        # Create n Connect the valve annotation toggle for thrust plot creation
        self.valve_annotation_frame_Thrust, self.valve_annotation_toggle_Thrust = self.create_valve_annotation_toggle()
        self.ui.thrust_tab.layout().insertWidget(2, self.valve_annotation_frame_Thrust)
        self.valve_annotation_toggle_Thrust.modeChanged.connect(self.create_thrust_plot)

        # Create n Connect the valve annotation toggle for both plot creation
        self.valve_annotation_frame_Both, self.valve_annotation_toggle_Both = self.create_valve_annotation_toggle()
        self.ui.both_plot_tab.layout().insertWidget(1, self.valve_annotation_frame_Both)
        self.valve_annotation_toggle_Both.modeChanged.connect(self.create_temp_n_pressure_plot)

        # Create n Connect the heater annotation toggle for both plot creation
        self.heater_annotation_frame_Both, self.heater_annotation_toggle_Both = self.create_heater_annotation_toggle()
        self.ui.both_plot_tab.layout().insertWidget(2, self.heater_annotation_frame_Both)
        self.heater_annotation_toggle_Both.modeChanged.connect(self.create_temp_n_pressure_plot)

        # Create n Connect the annotation drag toggle for both plot
        self.annotation_drag_frame_Both, self.annotation_drag_toggle_Both, self.annotation_reset_button_Both = self.create_annotation_drag_toggle()
        self.ui.both_plot_tab.layout().insertWidget(3, self.annotation_drag_frame_Both)
        # Connect the toggle to the method
        self.annotation_drag_toggle_Both.modeChanged.connect(self.toggle_annotation_drag_mode)

        self.ui.comboBoxThrust.itemCheckStateChanged.connect(self.create_thrust_plot)

    @staticmethod
    def create_valve_annotation_toggle():
        """Creates a toggle switch for valve annotation over the plot"""
        valve_annotation_frame = QFrame()

        valve_annotation_frame.setLayout(QHBoxLayout())
        valve_label = QLabel('Valve annotation')
        valve_annotation_toggle = MultiStateToggleSwitch(['On', 'Off'], width=50, height=25)
        valve_annotation_toggle.background_color = QColor(0, 0, 0)
        valve_annotation_toggle.handle_color = QColor(0, 123, 255)
        valve_annotation_frame.layout().addWidget(valve_label)
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        valve_annotation_frame.layout().addWidget(spacer)
        valve_annotation_frame.layout().addWidget(valve_annotation_toggle)

        return valve_annotation_frame, valve_annotation_toggle

    @staticmethod
    def create_heater_annotation_toggle():
        """Creates a toggle switch for valve annotation over the plot"""
        heater_annotation_frame = QFrame()

        heater_annotation_frame.setLayout(QHBoxLayout())
        heater_label = QLabel('Heater annotation')
        heater_annotation_toggle = MultiStateToggleSwitch(['On', 'Off'], width=50, height=25)
        heater_annotation_toggle.background_color = QColor(0, 0, 0)
        heater_annotation_toggle.handle_color = QColor(0, 123, 255)
        heater_annotation_frame.layout().addWidget(heater_label)
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        heater_annotation_frame.layout().addWidget(spacer)
        heater_annotation_frame.layout().addWidget(heater_annotation_toggle)

        return heater_annotation_frame, heater_annotation_toggle

    def create_annotation_drag_toggle(self):
        """Creates a toggle switch for enabling/disabling annotation drag mode"""
        drag_annotation_frame = QFrame()

        drag_annotation_frame.setLayout(QVBoxLayout())
        drag_label = QLabel('Drag annotations')
        drag_annotation_toggle = MultiStateToggleSwitch(['On', 'Off'], width=50, height=25)
        drag_annotation_toggle.background_color = QColor(0, 0, 0)
        drag_annotation_toggle.handle_color = QColor(0, 123, 255)  # Blue color for drag mode

        # Add reset button
        reset_button = QPushButton('Reset Positions')
        reset_button.setMaximumWidth(150)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px 5px;
                font-size: 10px;
                color: black;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)

        toggle_button_frame = QFrame()
        reset_button_frame = QFrame()

        toggle_button_frame.setLayout(QHBoxLayout())
        reset_button_frame.setLayout(QHBoxLayout())
        toggle_button_frame.layout().addWidget(drag_label)
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        toggle_button_frame.layout().addItem(spacer)
        toggle_button_frame.layout().addWidget(drag_annotation_toggle)
        reset_button_frame.layout().addWidget(reset_button)
        drag_annotation_frame.layout().addWidget(toggle_button_frame)
        drag_annotation_frame.layout().addWidget(reset_button_frame)
        drag_annotation_frame.layout().setAlignment(Qt.AlignmentFlag.AlignCenter)
        reset_button_frame.layout().setAlignment(Qt.AlignmentFlag.AlignCenter)

        # drag_annotation_frame.layout().addWidget(drag_label)
        # drag_annotation_frame.layout().addWidget(drag_annotation_toggle)
        # drag_annotation_frame.layout().addWidget(reset_button)
        drag_annotation_frame.layout().setSpacing(0)
        drag_annotation_frame.layout().setContentsMargins(0, 0, 0, 0)

        # Connect signals
        drag_annotation_toggle.modeChanged.connect(self.toggle_annotation_drag_mode)
        reset_button.clicked.connect(self.reset_annotation_positions)

        return drag_annotation_frame, drag_annotation_toggle, reset_button

    def toggle_annotation_drag_mode(self):
        """Toggle the drag mode for annotations"""
        print(f"[DEBUG] Toggle annotation drag mode called")

        # Get the current mode from any of the drag toggles (they should be synchronized)
        if hasattr(self, 'annotation_drag_toggle'):
            current_mode = self.annotation_drag_toggle.get_current_mode()
            print(f"[DEBUG] Using annotation_drag_toggle, mode: {current_mode}")
        elif hasattr(self, 'annotation_drag_toggle_Both'):
            current_mode = self.annotation_drag_toggle_Both.get_current_mode()
            print(f"[DEBUG] Using annotation_drag_toggle_Both, mode: {current_mode}")
        else:
            current_mode = 'Off'
            print(f"[DEBUG] No toggle found, defaulting to Off")

        self.annotation_drag_mode = (current_mode == 'On')
        print(f"[DEBUG] Drag mode set to: {self.annotation_drag_mode}, total annotations: {len(self.draggable_annotations)}")

        # Enable/disable dragging for all current annotations
        if self.annotation_drag_mode:
            self.enable_annotation_dragging()
            self.ui.lblLogInfo.setText("Annotation drag mode enabled - click and drag annotations to reposition")
        else:
            self.disable_annotation_dragging()
            self.ui.lblLogInfo.setText("Annotation drag mode disabled")

    def enable_annotation_dragging(self):
        """Enable dragging for all current annotations"""
        print(f"[DEBUG] Enabling dragging for {len(self.draggable_annotations)} annotations")
        for i, draggable_annotation in enumerate(self.draggable_annotations):
            print(f"[DEBUG] Connecting annotation {i}: {draggable_annotation.position_key}")
            draggable_annotation.connect()

    def disable_annotation_dragging(self):
        """Disable dragging for all current annotations"""
        print(f"[DEBUG] Disabling dragging for {len(self.draggable_annotations)} annotations")
        for i, draggable_annotation in enumerate(self.draggable_annotations):
            print(f"[DEBUG] Disconnecting annotation {i}: {draggable_annotation.position_key}")
            draggable_annotation.disconnect()

    def reset_annotation_positions(self):
        """Reset all annotations to their original positions"""
        for draggable_annotation in self.draggable_annotations:
            draggable_annotation.reset_position()

        # Clear stored custom positions
        self.annotation_positions.clear()
        self.ui.lblLogInfo.setText("All annotation positions reset to default")

    def clear_draggable_annotations(self):
        """Clear all draggable annotations (called when creating new plots)"""
        for draggable_annotation in self.draggable_annotations:
            draggable_annotation.disconnect()
        self.draggable_annotations.clear()
        self.disconnect_annotation_events()

        # Also clear user annotations
        if hasattr(self, 'annotation_manager'):
            self.annotation_manager.clear_all_user_annotations()

    def register_draggable_annotation(self, annotation):
        """Register a draggable annotation with the centralized event manager"""
        print(f"[DEBUG] register_draggable_annotation called for {annotation.position_key}")
        if annotation not in self.draggable_annotations:
            self.draggable_annotations.append(annotation)
            print(f"[DEBUG] Registered annotation {annotation.position_key}, total: {len(self.draggable_annotations)}")

            # Connect centralized events if this is the first annotation
            if len(self.draggable_annotations) == 1:
                self.connect_annotation_events()
        else:
            print(f"[DEBUG] Annotation {annotation.position_key} already registered")

    def unregister_draggable_annotation(self, annotation):
        """Unregister a draggable annotation from the centralized event manager"""
        if annotation in self.draggable_annotations:
            self.draggable_annotations.remove(annotation)
            print(f"[DEBUG] Unregistered annotation {annotation.position_key}, total: {len(self.draggable_annotations)}")

            # Disconnect centralized events if no annotations left
            if len(self.draggable_annotations) == 0:
                self.disconnect_annotation_events()

    def connect_annotation_events(self):
        """Connect centralized event handlers"""
        if hasattr(self, 'current_canvas') and self.current_canvas:
            self.annotation_event_handlers = [
                self.current_canvas.mpl_connect('pick_event', self.handle_annotation_pick),
                self.current_canvas.mpl_connect('motion_notify_event', self.handle_annotation_motion),
                self.current_canvas.mpl_connect('button_release_event', self.handle_annotation_release)
            ]
            print(f"[DEBUG] Connected centralized annotation events: {self.annotation_event_handlers}")

    def disconnect_annotation_events(self):
        """Disconnect centralized event handlers"""
        if hasattr(self, 'current_canvas') and self.current_canvas and self.annotation_event_handlers:
            for handler_id in self.annotation_event_handlers:
                self.current_canvas.mpl_disconnect(handler_id)
            self.annotation_event_handlers.clear()
            print(f"[DEBUG] Disconnected centralized annotation events")

    def handle_annotation_pick(self, event):
        """Centralized pick event handler"""
        for annotation in self.draggable_annotations:
            if annotation.handle_pick(event):
                break  # Only one annotation should handle the event

    def handle_annotation_motion(self, event):
        """Centralized motion event handler"""
        for annotation in self.draggable_annotations:
            if annotation.handle_motion(event):
                break  # Only the dragging annotation should handle motion

    def handle_annotation_release(self, event):
        """Centralized release event handler"""
        for annotation in self.draggable_annotations:
            if annotation.handle_release(event):
                break  # Only the dragging annotation should handle release

    def handle_double_click(self, event):
        """Handle double-click events for adding annotations"""
        if (event.inaxes and
            self.annotation_manager.is_annotation_mode_enabled() and
            event.dblclick):

            # Create new annotation at click position
            x, y = event.xdata, event.ydata
            if x is not None and y is not None:
                draggable_annotation = self.annotation_manager.create_annotation(
                    event.inaxes, x, y, "New Annotation"
                )
                if draggable_annotation:
                    # Immediately open edit dialog
                    self.show_annotation_edit_dialog(draggable_annotation)

    def handle_right_click(self, event):
        """Handle right-click events for annotation context menu"""
        if event.inaxes and event.button == 3:  # Right click
            # Check if we clicked on a user annotation
            for annotation_key, draggable_annotation in self.annotation_manager.user_annotations.items():
                if draggable_annotation.annotation.contains(event)[0]:
                    # Show context menu for this annotation
                    self.show_annotation_context_menu(draggable_annotation, event)
                    break

    def show_annotation_edit_dialog(self, draggable_annotation):
        """Show the annotation edit dialog"""
        try:
            dialog = AnnotationEditDialog(draggable_annotation, self)
            result = dialog.exec()

            if result == 2:  # Delete was clicked
                self.annotation_manager.delete_annotation(draggable_annotation)
            elif result == QDialog.Accepted:
                # Changes were already applied in the dialog
                pass

        except Exception as e:
            print(f"[ERROR] Failed to show annotation edit dialog: {e}")

    def edit_annotation(self, draggable_annotation):
        """Edit annotation (called by DraggableAnnotation)"""
        self.show_annotation_edit_dialog(draggable_annotation)

    def delete_annotation(self, draggable_annotation):
        """Delete annotation (called by DraggableAnnotation)"""
        self.annotation_manager.delete_annotation(draggable_annotation)

    def show_annotation_context_menu(self, draggable_annotation, event):
        """Show context menu for annotation"""
        from PySide6.QtWidgets import QMenu

        menu = QMenu(self)

        edit_action = menu.addAction("Edit Annotation")
        edit_action.triggered.connect(lambda: self.show_annotation_edit_dialog(draggable_annotation))

        delete_action = menu.addAction("Delete Annotation")
        delete_action.triggered.connect(lambda: self.annotation_manager.delete_annotation(draggable_annotation))

        # Convert matplotlib coordinates to widget coordinates
        if hasattr(self, 'current_canvas'):
            widget_pos = self.current_canvas.mapToGlobal(
                self.current_canvas.mapFromParent(QPoint(int(event.x), int(event.y)))
            )
            menu.exec(widget_pos)

    def refresh_data_files(self):
        # Store the current color assignments before resetting
        color_assignments = self.color.copy() if hasattr(self, 'color') else {}

        # Clearing out all the line Edits filled for earlier test
        self.reset_input_fields()

        # Restore the color assignments
        self.color = color_assignments

        # Clear existing plot
        while self.plot_layout.count():
            item = self.plot_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        self.clear_plot_previews()

        # Delete if there's temporary folder storing plots of previous analysis exists
        self.delete_temp_report_folder()

        # Delete if there's temporary plots folder created from database
        if hasattr(self, 'db_handler') and self.db_handler is not None:
            self.db_handler.delete_temp_plots_folder()

        # Resetting the data load button in the left panel
        self.ui.btnTempDataInd.setStyleSheet(u'''QPushButton{
    	                                                background-color: rgba(4, 120, 87, 0.3);
    	                                                padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#47a08e;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.btnPressureDataInd.setStyleSheet(u'''
                                                    QPushButton{
                                                        	background-color:rgba(29, 78, 216, 0.3);
                                                        	padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#7b92d4;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.btnTempDataLoad.setStyleSheet(u'''QPushButton{
    	                                                    background-color: rgba(4, 120, 87, 0.3);
    	                                                    padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#47a08e;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.btnPressureDataLoad.setStyleSheet(u'''QPushButton{
    	                                                    background-color: rgba(29, 78, 216, 0.3);
    	                                                    padding:5px;
                                                    }

                                                    QPushButton:hover{
    	                                                    background-color:#7b92d4;
                                                    }

                                                    QPushButton:pressed{
    	                                                    background-color:black;
                                                    }''')

        self.ui.subLnEdtTestConductedBy.clear()
        self.ui.subLnEdtReportGeneratedBy.clear()
        self.ui.subLnEdtReportAuthorizedBy.clear()

    def value_changed(self, parameter_whose_value_changed, type_of_parameter):

        dict_of_parameters = {'prop_initial': [self.ui.lnEdtInitialPropMass, self.ui.subLnEdtWghtOfPropBefTest, self.ui.subLnEdtPropWghtFild_2],
                              'prop_final': [self.ui.lnEdtFinalPropMass, self.ui.subLnEdtWghtOfPropAftTest, self.ui.subLnEdtPropWghtRecvrd_2],
                              'cat_initial': [self.ui.subLnEdtCatWghtBefTest, self.ui.subLnEdtCatWghtFild],
                              }

        if type_of_parameter == 'cat_final':
            cat_initial = float(self.ui.subLnEdtCatWghtFild.value())
            cat_final = float(self.ui.subLnEdtCatWghtRecvrd.value())
            cat_loss_percentage = ((cat_final - cat_initial)/cat_initial) * 100
            self.ui.subLnEdtCatLosPerc.setValue(cat_loss_percentage)
        elif type_of_parameter == 'mass_flow_rate':
            initial_prop_mass = self.ui.subLnEdtPropWghtFild_2.value()
            final_prop_mass = self.ui.subLnEdtPropWghtRecvrd_2.value()
            burn_time = self.ui.subLnEdtFirgDur_2.value()
            mass_flow_rate = ((initial_prop_mass - final_prop_mass)/burn_time) * 1000     # milli grams per second
            self.ui.subLnEdtApproxMassFlowRate_2.setValue(mass_flow_rate)
        elif type_of_parameter == 'burn_time':
            # Calculate burn time based on the difference between switch on and switch off times
            if self.ui.subLnEdtValveSwtchOnTime.time() and self.ui.subLnEdtValveSwtchOffTime.time():
                on_time = self.ui.subLnEdtValveSwtchOnTime.time()
                off_time = self.ui.subLnEdtValveSwtchOffTime.time()
                total_duration = on_time.secsTo(off_time)
                self.ui.lblFiringDuration.setText(f"{total_duration} s")
                self.ui.subLnEdtFirgDur_2.setValue(total_duration)
                self.ui.subLnEdtBurnTime.setText(f"{total_duration}")
        elif type_of_parameter == 'test_no':
            self.ui.testNoFrame.setVisible(True)
            text = self.ui.subLnEdtTestNo.text()
            self.ui.lblTestNumber.setText(text)
        elif type_of_parameter == 'aim':
            self.ui.lblAim.setVisible(True)
            text = self.ui.subLnEdtAim.text()
            self.ui.lblAim.setText(text)
        elif type_of_parameter == 'propellant':
            self.ui.lblPropellant.setVisible(True)
            text = self.ui.subLnEdtProp.text()
            self.ui.lblPropellant.setText(text)
        elif type_of_parameter == 'catalyst':
            text = self.ui.subLnEdtCat.text()
            self.ui.lblCatalyst.setVisible(True)
            self.ui.lblCatalyst.setText(text)
        elif type_of_parameter == 'prop_RI':
            current_text_prop = self.ui.subLnEdtProp.text()
            current_text_prop_RI = self.ui.subLnEdtPropRI.value()
            prop_concentration = self.concentration_by_RI(current_text_prop_RI)
            prop_concentration = prop_concentration
            set_text = current_text_prop + ' | ' + str(prop_concentration) + '%'
            self.ui.lblPropellant.setText(set_text)
            self.ui.subLnEdtPropRIBefFirg_2.setValue(float(self.ui.subLnEdtPropRI.value()))
            self.ui.subLnEdtConcBefTest.setValue(prop_concentration)
        else:
            value = parameter_whose_value_changed.value()
            l = dict_of_parameters[type_of_parameter]
            for i in l:
                if i != parameter_whose_value_changed:
                    i.setValue(value)

        if (self.ui.lnEdtInitialPropMass.value() != 0) and (self.ui.lnEdtFinalPropMass.value() >= 0):
            prop_initial = float(self.ui.lnEdtInitialPropMass.value())
            prop_final = float(self.ui.lnEdtFinalPropMass.value())
            prop_used_percentage = ((prop_initial - prop_final) / prop_initial) * 100
            self.ui.subLnEdtPropUsedPerc_2.setValue(prop_used_percentage)

            if self.ui.subLnEdtFirgDur_2.value() is not None:
                initial_prop_mass = self.ui.subLnEdtPropWghtFild_2.value()
                final_prop_mass = self.ui.subLnEdtPropWghtRecvrd_2.value()
                burn_time = self.ui.subLnEdtFirgDur_2.value()
                mass_flow_rate = ((initial_prop_mass - final_prop_mass) / burn_time) * 1000  # milli grams per second
                self.ui.subLnEdtApproxMassFlowRate_2.setValue(mass_flow_rate)

    def reset_button_styles(self):
        """Reset all button styles to default"""
        # Main section buttons
        main_buttons = [
            self.ui.btnTestPrereq,
            self.ui.btnHtrOp,
            self.ui.btnValveOperation,
            self.ui.btnPstTestAn,
            self.ui.btnPlots,
            self.ui.btnPerformance,
            self.ui.btnTestAuthorization,
        ]

        # Subsection buttons
        sub_buttons = [
            self.ui.btnBasicInfo,
            self.ui.btnSysSpec,
            self.ui.btnPropSpec,
            self.ui.btnCatSpec,
            self.ui.btnCompDet,
            self.ui.btnTestDet,
            self.ui.btnPumpOperation,
            self.ui.btnSuctValveOperation,
            self.ui.btnVacCretnInTank,
            self.ui.btnHtrInfo,
            self.ui.btnHtrCyc,
            self.ui.btnValve,
            self.ui.btnPstTestObs,
            self.ui.btnCatPostAn,
            self.ui.btnPropPostAn,
        ]

        # Reset all button styles
        for button in main_buttons:
            button.setStyleSheet(self.default_button_style)

        for button in sub_buttons:
            button.setStyleSheet(self.default_button_style_subsections)

    def update_section_visibility(self, current_page: str):
        """Update the visibility of subsection panels"""
        # Hide all subsection panels first
        self.ui.testPrereqSubSections.hide()
        self.ui.htrOpSubSections.hide()
        self.ui.valveOpSubSections.hide()
        self.ui.pstTestAnSubSections.hide()
        self.ui.plotControlsFrame.hide()
        self.ui.perforSubSections.hide()

        # Show the relevant subsection panel based on the current page
        if current_page in self.section_headers:
            section = self.section_headers[current_page]
            if section == "Test Prerequisite":
                self.ui.testPrereqSubSections.show()
            elif section == "Heater Operation":
                self.ui.htrOpSubSections.show()
            elif section == "Valve Operation":
                self.ui.valveOpSubSections.show()
            elif section == "Post Test Analysis":
                self.ui.pstTestAnSubSections.show()
            elif section == "Plots":
                # Fix DataFrame boolean evaluation
                has_temperature_data = False if self.temperature_data is None else not self.temperature_data.empty
                has_pressure_data = False if self.pressure_data is None else not self.pressure_data.empty

                if not (has_temperature_data or has_pressure_data):
                    QMessageBox.warning(self, 'Load Data', 'Please load the Temperature or Pressure Data first!')
                    # Revert to previous page
                    previous_widget = self.ui.contentStack.widget(self.ui.contentStack.currentIndex() - 1)
                    self.ui.contentStack.setCurrentWidget(previous_widget)
                    previous_page = previous_widget.objectName()
                    return
                self.ui.plotControlsFrame.show()
            elif section == "Performance":
                self.ui.perforSubSections.show()

    def change_heater_cycle_tab(self, direction: str = None):
        if direction == 'next':
            current_tab = self.ui.cycleTabWidget.currentIndex()
            to_set_tab = current_tab + 1
            to_set_tab = to_set_tab % 4
        elif direction == 'back':
            current_tab = self.ui.cycleTabWidget.currentIndex()
            to_set_tab = current_tab - 1
            to_set_tab = to_set_tab % 4

        self.ui.cycleTabWidget.setCurrentIndex(to_set_tab)

    def update_button_states(self, current_page: str):
        """Update button styles based on current page"""
        # Reset all buttons to default style
        self.reset_button_styles()

        # Get the buttons to highlight
        if current_page in self.page_button_mapping:
            main_button, sub_button = self.page_button_mapping[current_page]

            # Highlight the main section button
            if main_button:
                main_button.setStyleSheet("""
                                        background-color:#00b28e;
                                        font-size: 19px;
	                                    font-family: Helvetica;
	                                    font-weight: bold;
                                        color:black;
                                        """
                                          )

            # Highlight the subsection button if it exists
            if sub_button:
                sub_button.setStyleSheet(self.selected_button_style)

    def change_page(self, next: bool):
        """Handle next and back push button click"""
        current_index = self.ui.contentStack.currentIndex()
        total_pages = self.ui.contentStack.count()

        # Calculate new index based on direction
        if next:
            # Don't proceed if already at last page
            if current_index >= total_pages - 1:
                return
            index = current_index + 1
        else:
            # Don't proceed if already at first page
            if current_index <= 0:
                return
            index = current_index - 1

        # Get the target page name
        target_widget = self.ui.contentStack.widget(index)
        target_page = target_widget.objectName()

        # Check if trying to navigate to plots section without data
        if target_page == "PlotWindow" and not (self.temperature_data or self.pressure_data):
            QMessageBox.warning(self, 'Load Data', 'Please load the Temperature or Pressure Data first!')
            return  # Don't proceed with page change

        # If we get here, it's safe to change the page
        self.ui.contentStack.setCurrentIndex(index)

        # Update UI elements - Note the order here is important
        self.update_section_visibility(target_page)  # This will handle reverting if necessary
        self.update_button_states(target_page)

        # Update section header only if we didn't revert in update_section_visibility
        if self.ui.contentStack.currentWidget().objectName() == target_page:
            if target_page in self.section_headers:
                self.ui.lblCurentSection.setText(self.section_headers[target_page])

    def update_content(self, widget):
        self.ui.contentStack.setCurrentWidget(widget)

        # Get current page name
        current_page = self.ui.contentStack.currentWidget().objectName()

        # Update UI elements
        self.update_button_states(current_page)

        # Update section header
        if current_page in self.section_headers:
            self.ui.lblCurentSection.setText(self.section_headers[current_page])

    def get_executable_path(self):
        """Get the correct base path whether running as script or executable"""
        if getattr(sys, 'frozen', False):
            # Running as executable
            return os.path.dirname(sys.executable)
        else:
            # Running as script
            return os.getcwd()

    def concentration_by_RI(self, RI: float):
        return round((-3208.9 * RI ** 2) + (10135 * RI) - 7807.9 , 2)

    def update_table(self):
        # For updating the RI Concentation table of Propellant
        RI_before_test = self.ui.subLnEdtPropRIBefFirg_2.value()
        RI_after_test = self.ui.subLnEdtPropRIAftFirg_2.value()


        conc_before_test = str(self.concentration_by_RI(RI_before_test))
        conc_after_test = str(self.concentration_by_RI(RI_after_test))

        if RI_after_test == 0:
            RI_after_test = 'NA'
            conc_after_test = 'NA'

        self.ui.subLblPropBefRITable.setText(str(RI_before_test))
        self.ui.subLblPropAftRITable.setText(str(RI_after_test))

        self.ui.subLblPropBefConcTable.setText(conc_before_test)
        self.ui.subLblPropAftConcTable.setText(conc_after_test)

    def load_temperature_data(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Temperature Data File",
            "",
            "CSV files (*.csv)"
        )
        if not file_path:
            return

        # Try different encodings
        encodings = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
        df_preview = None

        for encoding in encodings:
            try:
                # Read the data portion of CSV, skipping metadata
                df_preview = pd.read_csv(file_path, skiprows=6, nrows=0, encoding=encoding)
                # If successful, use this encoding for the rest of the operations
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"Error reading file with {encoding} encoding: {str(e)}")
                continue

        if df_preview is None:
            QMessageBox.critical(self, "Error", "Could not read the CSV file with any supported encoding.")
            return

        columns = df_preview.columns.tolist()

        # Try to detect scan rate from metadata using the same encoding
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                metadata_lines = [next(f).strip() for _ in range(6)]
            scan_rate = None
            for line in metadata_lines:
                if 'Scan Rate' in line:
                    try:
                        scan_rate = int(line.split(':')[1].strip().strip('"').strip(','))
                    except (ValueError, IndexError):
                        print(f"Warning: Invalid scan rate value: {line}")
                    break

            # Show configuration dialog
            dialog = TemperatureDataConfigDialog(columns, scan_rate, self)
            if dialog.exec() == QDialog.Accepted:
                sample_col = dialog.get_sample_column()
                temp_cols = dialog.get_temperature_columns()
                scan_rate = dialog.get_scan_rate()

                # Validate selections
                if not sample_col or not temp_cols or scan_rate is None or scan_rate <= 0:
                    QMessageBox.warning(self, "Invalid Selection",
                                        "Please select a Sample column, at least one Temperature column, and provide a valid scan rate.")
                    return

                # Load data with selected columns using the successful encoding
                df = pd.read_csv(file_path, skiprows=6, usecols=[sample_col] + temp_cols, encoding=encoding)

                # Create time column
                df['time'] = df[sample_col] / scan_rate
                df = df.drop(columns=[sample_col])

                # Process temperature columns
                for col in temp_cols:
                    if df[col].dtype == object:
                        df[col] = df[col].astype(str).str.replace(',', '.')
                    df[col] = pd.to_numeric(df[col], errors='coerce')

                self.temperature_data = df[['time'] + temp_cols]

                # Create necessary directories
                base_path = self.get_executable_path()
                temp_report_dir = os.path.join(base_path, "temp_report")
                plots_dir = os.path.join(temp_report_dir, "plots")
                temp_plot_dir = os.path.join(plots_dir, "temperature")

                # Create all required directories
                os.makedirs(temp_report_dir, exist_ok=True)
                os.makedirs(plots_dir, exist_ok=True)
                os.makedirs(temp_plot_dir, exist_ok=True)

                # Ensure directories are writable
                for directory in [temp_report_dir, plots_dir, temp_plot_dir]:
                    if not os.access(directory, os.W_OK):
                        os.chmod(directory, 0o777)

                # Analyze and generate only 'all_temperature' plot
                self.temp_analyzer.analyze_temperature_data(self.temperature_data, temp_plot_dir, save_plot=True,
                                                            plot_types=['all'])

                # Verify plots were created
                if os.path.exists(temp_plot_dir):
                    plots = os.listdir(temp_plot_dir)
                    if plots:
                        self.ui.lblLogInfo.setText("Temperature plots created successfully")

                        # Adding default plots to preview after they're generated
                        self.add_default_plots_to_preview()
                    else:
                        self.ui.lblLogInfo.setText("No plots were generated")
                else:
                    self.ui.lblLogInfo.setText("Plot directory not created")

                # Enable the plots button
                self.ui.btnPlots.setEnabled(True)
                self.ui.btnTempMatrix.setEnabled(True)
                self.setup_plot_controls()
                self.ui.btnTempDataInd.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                self.ui.btnTempDataLoad.setStyleSheet(u'background-color: rgb(6, 196, 142);')

                # Call check_and_enable_plots_button to ensure the plots button is enabled
                if hasattr(self, 'auto_saver'):
                    self.auto_saver.check_and_enable_plots_button()

                QMessageBox.information(self, "Data Loading Status",
                                        f"Temperature Data loaded successfully")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error processing file: {str(e)}")
            import traceback
            traceback.print_exc()
            return

    #---/---/---/---/---/---/---Temperature Matrix---/---/---/---/---/---/---/---/---/---/---/---/---/---/
    def create_interactive_temperature_plot(self):
        """Create interactive temperature plot with range selection"""
        try:
            if self.temperature_data is None:
                self.ui.lblLogInfo.setText("Please load temperature data first!")
                return

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Setting the current section name in the top bar
            self.ui.lblCurentSection.setText('Temperature Matrix Plot')

            # Get selected temperature columns
            selected_cols = self.temp_selection_widget.get_selected_columns()

            # Create figure and canvas
            self.figure, self.axes = plt.subplots(figsize=(6, 4.5))

            # Plot each selected temperature column with color from temperature selection widget
            for col in selected_cols:
                # Get color from the temperature selection widget
                color = self.temp_selection_widget.get_column_color(col)

                self.axes.plot(self.temperature_data['time'],
                               self.temperature_data[col],
                               label=col,
                               linewidth=2,
                               color=color
                               )

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)
            self.axes.patch.set_alpha(0.0)

            # Apply plot styling
            self.plot_style(self.axes, 'Time (s)', 'Temperature (°C)',
                            'Temperature Distribution (Click and drag to select valid ranges)')

            # Tight layout to avoid extra padding
            # self.figure.tight_layout()

            # Store the figure
            self.current_figures['temperature'] = self.figure

            # Create canvas and ensure it has a transparent background
            self.canvas = PlotCanvas(self.figure)
            self.canvas.setStyleSheet("background-color: transparent;")  # Ensure canvas is transparent

            # Create layout for plot and toolbar
            plot_container = QWidget()
            plot_layout = QVBoxLayout(plot_container)
            plot_layout.setContentsMargins(0, 0, 0, 0)
            plot_container.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            self.plot_layout.addWidget(plot_container)

            data_series = {col: col for col in selected_cols}

            self.toolbar = CustomNavigationToolbar(self.canvas, plot_container, data_series)

            plot_layout.addWidget(self.toolbar)
            plot_layout.addWidget(self.canvas)

            # Set parent widget background to transparent if needed
            self.ui.plots.setStyleSheet("background-color: transparent;")

            # Creating annotation
            self.cursor_annotation = self.axes.annotate(
                '',
                xy=(0, 0),
                xytext=(10, 10),
                textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.5', fc='black', alpha=0.7),
                color='white',
                fontsize=9
            )
            self.cursor_annotation.set_visible(False)

            self.axes.title.set_color('#000000')

            # Connect event handlers
            self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
            self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
            self.canvas.mpl_connect('motion_notify_event', self.on_mouse_motion)
            self.canvas.mpl_connect('motion_notify_event', self.update_cursor_info)
            self.canvas.mpl_connect('button_press_event', self.handle_double_click)
            self.canvas.mpl_connect('button_press_event', self.handle_right_click)

            # Add control buttons
            button_layout = QHBoxLayout()

            # Undo button
            undo_button = QPushButton("Undo Last Selection")
            undo_button.setStyleSheet("""
                            QPushButton {
                                background-color: #000000;
                                border-radius: 12px;
                                padding: 8px 15px;
                                color: white;
                                font-size: 14px;
                                font-family: Arial;
                                max-width: 170px;
                            }
                            QPushButton:hover {
                                background-color: #557799;
                            }
                        """)
            undo_button.clicked.connect(self.undo_last_range)
            button_layout.addWidget(undo_button)

            # Clear button
            clear_button = QPushButton("Clear All Selections")
            clear_button.setStyleSheet("""
                            QPushButton {
                                background-color: #000000;
                                border-radius: 12px;
                                padding: 8px 15px;
                                color: white;
                                font-size: 14px;
                                font-family: Arial;
                                max-width: 170px;
                            }
                            QPushButton:hover {
                                background-color: #aa5555;
                            }
                        """)
            clear_button.clicked.connect(self.clear_all_ranges)
            button_layout.addWidget(clear_button)

            # Generate Matrix button
            generate_button = QPushButton("Generate Matrix")
            generate_button.setStyleSheet("""
                            QPushButton {
                                background-color: #000000;
                                border-radius: 12px;
                                padding: 8px 15px;
                                color: white;
                                font-size: 14px;
                                font-family: Arial;
                                max-width: 170px;
                            }
                            QPushButton:hover {
                                background-color: #55aa55;
                            }
                        """)
            generate_button.clicked.connect(self.generate_filtered_matrix)
            button_layout.addWidget(generate_button)

            # Add buttons to plot layout
            button_widget = QWidget()
            button_widget.setLayout(button_layout)
            self.plot_layout.addWidget(button_widget)

            self.ui.lblLogInfo.setText(
                "Interactive plot created. Click and drag to select valid ranges for temperature Matrix.")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating interactive plot: {str(e)}")

    def update_cursor_info(self, event):
        """Update cursor position information with smart annotation positioning"""
        if event.inaxes == self.axes:
            x, y = event.xdata, event.ydata

            # Find closest data points
            time_idx = (self.temperature_data['time'] - x).abs().idxmin()
            current_values = {}

            # Get values for all plotted columns
            selected_cols = self.temp_selection_widget.get_selected_columns()
            for col in selected_cols:
                current_values[col] = self.temperature_data.loc[time_idx, col]

            # Format annotation text
            text = f'Time: {x:.2f}s\n'
            for col, val in current_values.items():
                text += f'{col}: {val:.2f}°C\n'

            # Get the bounds of the plot
            bbox = self.axes.get_position()
            y_range = self.axes.get_ylim()

            # Calculate relative position in the plot
            y_rel = (y - y_range[0]) / (y_range[1] - y_range[0])

            # Determine annotation position
            if y_rel > 0.5:  # If cursor is in upper half
                xytext = (10, -15 - 10 * len(current_values))  # Place below cursor
                va = 'top'
            else:  # If cursor is in lower half
                xytext = (10, 15)  # Place above cursor
                va = 'bottom'

            # Update annotation
            self.cursor_annotation.set_text(text)
            self.cursor_annotation.xy = (x, y)
            self.cursor_annotation.xyann = xytext
            self.cursor_annotation.set_va(va)
            self.cursor_annotation.set_visible(True)
            self.canvas.draw_idle()
        else:
            self.cursor_annotation.set_visible(False)
            self.canvas.draw_idle()

    def update_temperature_plot(self):
        """Update plot when temperature selection changes"""
        if hasattr(self, 'axes') and self.axes is not None:
            try:
                self.axes.clear()
            except Exception as e:
                print(f"Error clearing axes: {str(e)}")
                return  # Exit the method if we can't clear the axes

            # Check if temperature_data exists and is not None
            if not hasattr(self, 'temperature_data') or self.temperature_data is None:
                print("Temperature data is not available")
                return  # Exit the method if temperature data is not available

            selected_cols = self.temp_selection_widget.get_selected_columns()

            # Plot each selected temperature column with color from temperature selection widget
            for col in selected_cols:
                try:
                    # Get color from the temperature selection widget
                    color = self.temp_selection_widget.get_column_color(col)

                    self.axes.plot(self.temperature_data['time'],
                                   self.temperature_data[col],
                                   label=col,
                                   linewidth=2,
                                   color=color
                                   )
                except Exception as e:
                    print(f"Error plotting column {col}: {str(e)}")

            # Redraw Included ranges
            print(f"Selected ranges for the interactive temperature plot: {self.selected_ranges}")
            try:
                if hasattr(self, 'selected_ranges') and self.selected_ranges:
                    for start, end in self.selected_ranges:
                        try:
                            height = self.axes.get_ylim()[1] - self.axes.get_ylim()[0]
                            rect = Rectangle(
                                (start, self.axes.get_ylim()[0]),
                                end - start, height,
                                alpha=0.2,
                                facecolor='green'
                            )
                            self.axes.add_patch(rect)
                        except Exception as e:
                            print(f"Error drawing range rectangle: {str(e)}")
            except Exception as e:
                print(f"Error processing selected ranges: {str(e)}")

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)  # Transparent figure background
            self.axes.patch.set_alpha(0.0)  # Transparent axes background

            x_label = 'Time (s)'
            y_label = 'Temperature (°C)'
            title = 'Temperature Distribution (Click and drag to select ranges to exclude)'

            # Apply plot styling (ensure it doesn’t override transparency)
            self.plot_style(self.axes, x_label, y_label, title)

            # Tight layout to avoid extra padding
            # self.figure.tight_layout()

            # Redraw the plot
            self.canvas.draw()

    def on_mouse_press(self, event):
        """Handle mouse press for range selection"""
        if event.inaxes != self.axes or not event.button == 1:
            return

        # Ignore if toolbar is in pan/zoom mode
        if self.toolbar.mode != '':
            return

        self.selecting = True
        self.start_x = event.xdata

        # Create selection rectangle
        height = self.axes.get_ylim()[1] - self.axes.get_ylim()[0]
        self.current_rect = Rectangle(
            (event.xdata, self.axes.get_ylim()[0]),
            0, height,
            alpha=0.3,
            facecolor='green'
        )
        self.axes.add_patch(self.current_rect)

    def on_mouse_motion(self, event):
        """Handle mouse motion during range selection"""
        if not self.selecting or event.inaxes != self.axes or not self.current_rect:
            return

        # Ignore if toolbar is in pan/zoom mode
        if self.toolbar.mode != '':
            return

        # Update rectangle width
        width = event.xdata - self.start_x
        self.current_rect.set_width(width)
        self.canvas.draw_idle()  # More efficient than full draw

    def on_mouse_release(self, event):
        """Handle mouse release to complete range selection"""
        if not self.selecting:
            return

        self.selecting = False

        # Ignore if toolbar is in pan/zoom mode
        if self.toolbar.mode != '':
            if self.current_rect:
                self.current_rect.remove()
                self.canvas.draw()
            return

        if event.inaxes != self.axes or not self.current_rect:
            if self.current_rect:
                self.current_rect.remove()
                self.canvas.draw()
            return

        # Add range to selected ranges
        x_range = sorted([self.start_x, event.xdata])
        if abs(x_range[1] - x_range[0]) > 0.1:  # Minimum selection width
            self.selected_ranges.append((x_range[0], x_range[1]))
            self.current_rect.set_alpha(0.2)
            self.ui.lblLogInfo.setText(
                f"Added selected range: {x_range[0]:.2f}s to {x_range[1]:.2f}s"
            )
        else:
            self.current_rect.remove()
            self.canvas.draw()

        self.current_rect = None

    def undo_last_range(self):
        """Remove the last selected range"""
        if not self.selected_ranges:
            return

        self.selected_ranges.pop()

        self.update_temperature_plot()
        self.ui.lblLogInfo.setText("Removed last excluded range")

    def clear_all_ranges(self):
        """Clear all selected ranges"""
        self.selected_ranges.clear()
        self.ui.lblLogInfo.setText("Cleared all excluded ranges")

        # Update the plot
        self.update_temperature_plot()

    def generate_filtered_matrix(self):
        """Generate temperature analysis matrix using selected ranges"""
        try:
            # Changing the content window to the temperature matrix
            self.ui.contentStack.setCurrentWidget(self.ui.tableView)

            # Get selected columns
            selected_cols = self.temp_selection_widget.get_selected_columns()

            if not selected_cols:
                QMessageBox.warning(self, "Warning",
                                    "Please select at least one temperature column.")
                return

            if not self.selected_ranges:
                QMessageBox.warning(self, "Warning",
                                    "Please select at least one time range.")
                return

            # Store the selected columns
            self.selected_cols = selected_cols  # Update selected_cols here

            # Create mask for selected ranges
            mask = pd.Series(False, index=self.temperature_data.index)

            for start, end in self.selected_ranges:
                mask |= ((self.temperature_data['time'] >= start) &
                         (self.temperature_data['time'] <= end))

            # Filter data
            filtered_data = self.temperature_data[mask]

            # Select only chosen columns plus time
            columns_to_use = ['time'] + selected_cols
            filtered_data = filtered_data[columns_to_use]

            self.filtered_temp_data = filtered_data

            # Generate matrix
            output_dir = os.path.join(os.getcwd(), "temp_report")
            os.makedirs(output_dir, exist_ok=True)

            self.temp_analyzer.generate_temperature_table(filtered_data, output_dir)

            if hasattr(self.temp_analyzer, 'analysis_results') and 'matrix' in self.temp_analyzer.analysis_results:
                self.display_temperature_analysis(self.temp_analyzer.analysis_results['matrix'])
                self.ui.lblLogInfo.setText("Generated matrix from selected ranges")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error generating matrix: {str(e)}")
            traceback.print_exc()

    def generate_max_temperatures_plot(self):
        try:
            self.current_tab_name = 'temperature'
            self.current_plot_type = 'temperature_matrix'

            # Set the plot title
            plot_title = 'Max Temperature Distribution Across Locations'

            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot and store reference to prevent deletion
            self.clear_plot_layout()

            temp_cols = [col for col in self.filtered_temp_data.columns if col not in ['time']]
            max_temps = [(col, self.filtered_temp_data[col].max()) for col in temp_cols]
            max_temps = [(col, temp) for col, temp in max_temps if not pd.isna(temp)]

            if max_temps:
                self.ui.plotTopBar.show()
                self.current_figures.clear()

                # Create figure with adjusted size and spacing
                self.figure, self.axes = plt.subplots(figsize=(3, 1))

                temps = [temp for _, temp in max_temps]
                locations = [col for col, _ in max_temps]

                # Get color for Maximum Temperature using the color utility
                max_temp_color = assign_color('Maximum Temperature', self.color, self.color_palette)

                self.axes.plot(range(len(temps)), temps, '-o',
                               linewidth=2, markersize=8,
                               color=max_temp_color,
                               label='Maximum Temperature')

                # Add point labels with adjusted position
                for i, (_, val) in enumerate(max_temps):
                    self.axes.annotate(f'{val:.2f}°C',
                                       (i, val),
                                       xytext=(0, 10),
                                       textcoords='offset points',
                                       ha='center',
                                       va='bottom')

                # Set figure and axes backgrounds to transparent
                self.figure.patch.set_alpha(0.0)  # Transparent figure background
                self.axes.patch.set_alpha(0.0)  # Transparent axes background

                # Set labels and style
                plt.xticks(range(len(temps)), locations, rotation=45, ha='right')
                x_label = 'Location'
                y_label = 'Maximum Temperature (°C)'

                # Applying plot styling
                self.plot_style(self.axes, x_label, y_label, plot_title)

                legend = self.axes.legend(bbox_to_anchor=(0.1, 1),
                                          loc='upper left',
                                          facecolor='none',  # Transparent background
                                          edgecolor='#446699'  # Border color
                                          )
                # Setting legend text color
                for text in legend.get_texts():
                    text.set_color('#09090b')

                # Tight layout to avoid extra padding
                # self.figure.tight_layout()

                # Store the figure
                self.current_figures['temperature_matrix'] = self.figure

                # Create canvas and ensure it has a transparent background
                self.canvas = PlotCanvas(self.figure)
                self.canvas.setStyleSheet("background-color: transparent;")

                plot_widget = QWidget()
                plot_widget_layout = QVBoxLayout(plot_widget)
                plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
                self.plot_layout.addWidget(plot_widget)

                data_series = {'Maximum Temperature': temps}
                toolbar = CustomNavigationToolbar(self.canvas, plot_widget, data_series)

                plot_widget_layout.addWidget(toolbar)
                plot_widget_layout.addWidget(self.canvas)

                # Set parent widget background to transparent if needed
                self.ui.plots.setStyleSheet("background-color: transparent;")

                # Update the plot title
                self.update_current_plot_title()

                # Set the title explicitly on the axes
                self.axes.set_title(plot_title,
                                    color='#09090b',
                                    fontsize=12,
                                    fontweight='bold',
                                    y=1.06)

                # Store the current plot information
                self.current_plot_info = {
                    'title': plot_title,
                    'type': 'temperature_matrix'
                }

                self.ui.lblLogInfo.setText("Maximum temperatures plot created successfully")

                # Setting indicator color green
                self.ui.tempMatrixIndicator.setStyleSheet("""
                background-color: rgb(6, 196, 142);
                border-radius: 7px;
                """
                                                          )

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()

    # Method to toggle Visibibility of ui elements
    @staticmethod
    def toggle_visibility(element):
        element.setVisible(not element.isVisible())

    def toggle_section(self, section_key: str, widget: QWidget):
        """Toggle section with animation"""
        try:
            animation = self.section_animations.get(section_key)
            if animation:
                # Reset any ongoing animations
                animation.animation.stop()
                animation.opacity_animation.stop()

                if widget.isHidden() or widget.maximumHeight() == 0:
                    # Ensure widget is in proper state before expanding
                    widget.setMaximumHeight(16777215)  # Reset max height
                    animation.expand()
                else:
                    animation.collapse()

        except Exception as e:
            print(f"Error toggling section: {str(e)}")

    def handle_temp_matrix_clicked(self):
        """Handle temperature matrix button click"""

        # Hiding plot top bar
        self.ui.plotTopBar.hide()

        # Set visible the temperature data selection window
        self.toggle_visibility(self.ui.tempMatrixDataSelection)

        # Switch to plots widget
        self.ui.contentStack.setCurrentWidget(self.ui.plots)
        self.ui.lblCurentSection.setText("Plots")

        # Show temperature selection widget
        self.temp_selection_widget.show()

        # Get temperature columns
        temp_cols = [col for col in self.temperature_data.columns
                     if col not in ['Sample', 'time']]

        # Initialize selection widget with columns
        self.temp_selection_widget.add_temperature_columns(temp_cols)

        # Create initial plot
        self.create_interactive_temperature_plot()

#-/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/---/

    def setup_plot_controls(self):
        """Setup plot controls with validated columns"""
        try:
            if self.temperature_data is not None:
                valid_cols = [col for col in self.temperature_data.columns
                              if col != 'Sample']

                # Update X-axis combo box
                self.ui.comboBoxXAxisTemp.clear()
                self.ui.comboBoxXAxisTemp.addItems(valid_cols)

                # Set default X-axis to 'time' if available
                if 'time' in valid_cols:
                    self.ui.comboBoxXAxisTemp.setCurrentText('time')

                # Update Y-axis options
                self.ui.comboBoxYAxisTemp.clear()
                temp_cols = [col for col in valid_cols
                             if col not in ['Sample', 'time']]
                self.ui.comboBoxYAxisTemp.addItems(temp_cols)

                self.ui.lblLogInfo.setText("Plot controls updated:")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error setting up plot controls: {str(e)}")

    def load_pressure_data(self):
        """Load pressure data and trigger temperature analysis"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(self, "Select Pressure Data File", "",
                                                       "DAT and XLSX Files (*.dat *.xlsx)")

            if file_path:
                # Create and show pressure absolute dialog
                is_absolute = self._show_pressure_absolute_dialog()

                # Load pressure data with the is_absolute parameter
                self.pressure_data = self.data_loader.load_pressure_data(file_path, is_absolute)

                if self.pressure_data is not None:
                    self.ui.lblLogInfo.setText("Pressure data loaded successfully")
                    self.ui.btnPlots.setEnabled(True)

                    # Changing the indicator color to green to show that the data is loaded
                    self.ui.btnPressureDataInd.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                    self.ui.btnPressureDataLoad.setStyleSheet(u'background-color: rgb(29, 78, 216);')

                    # Call check_and_enable_plots_button to ensure the plots button is enabled
                    if hasattr(self, 'auto_saver'):
                        self.auto_saver.check_and_enable_plots_button()

                    QMessageBox.information(self, "Data Loading Status",
                                            f"Pressure Data loaded Successfully")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading pressure data: {str(e)}")

    def _show_pressure_absolute_dialog(self):
        """Show pressure absolute dialog and return user selection"""
        try:
            # Create dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Pressure Data Configuration")
            dialog.setModal(True) # Make the dialog modal
            dialog.resize(300, 150)

            # Setup UI using the existing Ui_Form
            ui_form = Ui_Form()
            ui_form.setupUi(dialog)

            # Create toggle switch and add to the toggle button frame
            pressure_absolute_toggle = MultiStateToggleSwitch(['Yes', 'No'])
            # Use the existing layout instead of creating a new one
            existing_layout = ui_form.toggle_botton_frame.layout()
            if existing_layout:
                existing_layout.addWidget(pressure_absolute_toggle)
            else:
                # Fallback: create new layout if none exists
                layout = QHBoxLayout(ui_form.toggle_botton_frame)
                layout.addWidget(pressure_absolute_toggle)

            # Add OK and Cancel buttons
            button_layout = QHBoxLayout()
            ok_button = QPushButton("OK")
            cancel_button = QPushButton("Cancel")

            button_layout.addWidget(ok_button)
            button_layout.addWidget(cancel_button)

            # Add button layout to the main layout
            ui_form.verticalLayout.addLayout(button_layout)

            # Connect button signals
            ok_button.clicked.connect(dialog.accept)
            cancel_button.clicked.connect(dialog.reject)

            # Show dialog and get result
            if dialog.exec() == QDialog.Accepted:
                # Return True if "Yes" is selected (index 0), False if "No" is selected (index 1)
                absolute_pressure = pressure_absolute_toggle.get_current_index()
                if absolute_pressure == 0:
                    print("User selected Yes")
                    return True
                else:
                    print("User selected No")
                    return False
            else:
                # User cancelled, default to False (not absolute)
                return False

        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Error showing pressure dialog: {str(e)}")
            # Default to False if dialog fails
            return False

    def display_temperature_analysis(self, df: pd.DataFrame):
        """Display temperature analysis in GUI"""
        try:
            # Configure table widget
            table = self.ui.tableTemperatureAnalysis
            table.setRowCount(len(df.index))
            table.setColumnCount(len(df.columns))

            # Set headers
            table.setHorizontalHeaderLabels(df.columns)
            table.setVerticalHeaderLabels(df.index)

            # Populate data
            for i in range(len(df.index)):
                for j in range(len(df.columns)):
                    value = df.iloc[i, j]
                    item = QTableWidgetItem(str(value))
                    if i == j:  # Diagonal elements
                        item.setBackground(QColor(71, 160, 142))  # Green background
                        font = item.font()
                        font.setBold(True)
                        item.setFont(font)
                    table.setItem(j, i, item)

            # Adjust table appearance
            table.resizeColumnsToContents()
            table.resizeRowsToContents()
            table.show()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error displaying temperature analysis: {str(e)}")

    def _perform_scroll(self, widget):
        """Actually perform the scrolling operation"""
        try:
            scroll_area = widget.parent()
            while scroll_area and not isinstance(scroll_area, QScrollArea):
                scroll_area = scroll_area.parent()

            if scroll_area:
                widget_pos = widget.mapTo(scroll_area.widget(), QPoint(0, 0))
                scroll_area_height = scroll_area.height()
                widget_height = widget.height()
                center_position = max(0, widget_pos.y() - (scroll_area_height - widget_height) // 2)
                scroll_area.verticalScrollBar().setValue(center_position)

        except Exception as e:
            print(f"Error performing scroll: {str(e)}")

    def update_plot_segment_scrollable(self):
        """Update plot segment scrollable area based on the heater cycles"""
        # Delete existing buttons if any
        if hasattr(self, 'horizontal_scroll_area'):
            scroll_area = self.horizontal_scroll_area
            if scroll_area.widget():
                scroll_area.widget().deleteLater()
            self.ui.plotControlsFrame.layout().removeWidget(scroll_area)
            scroll_area.deleteLater()
            del self.horizontal_scroll_area

        # Create a scroll area for the catalyst cards
        scroll_area = QScrollArea(self)
        self.ui.plotControlsFrame.layout().insertWidget(0, scroll_area)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setMaximumHeight(45)

        # Event filter to handle mouse wheel for horizontal scrolling
        scroll_area.installEventFilter(self)

        segment_button_container = QWidget()
        segment_button_container.setStyleSheet("QWidget{background-color: #000000; border-radius:3px;}")

        segment_button_container_layout = QHBoxLayout(segment_button_container)
        segment_button_container_layout.setContentsMargins(0, 0, 0, 0)
        segment_button_container_layout.setSpacing(0)

        segment_button_container_layout.addStretch()
        scroll_area.setWidget(segment_button_container)

        # Initialize button states dictionary
        self.cycle_button_states = {}
        self.cycle_buttons = {}

        # Initialize button states dictionary
        self.cycle_button_states = {}
        self.cycle_buttons = {}

        # Add Pre-heat button
        self.preheat_button = QPushButton("Pre-heat")
        self.cycle_buttons['preheat'] = self.preheat_button
        self.cycle_button_states['preheat'] = False
        self._update_cycle_button_style(self.preheat_button, 'preheat', False)
        self.preheat_button.clicked.connect(lambda checked: self._toggle_cycle_button('preheat'))
        segment_button_container_layout.addWidget(self.preheat_button)

        # Add cycle buttons
        for i in range(1, 5):
            # Create a button for each cycle plot
            button = QPushButton(f"Cycle {i}")

            # Store button reference and initialize state
            self.cycle_buttons[i] = button
            self.cycle_button_states[i] = False  # Default to off state

            # Set initial styling based on state
            self._update_cycle_button_style(button, i, False)

            # Connect button click to toggle function
            button.clicked.connect(lambda checked, cycle=i: self._toggle_cycle_button(cycle))

            segment_button_container_layout.addWidget(button)

        # Add Cooldown button
        cooldown_button = QPushButton("Cooldown")
        self.cycle_buttons['cooldown'] = cooldown_button
        self.cycle_button_states['cooldown'] = False
        self._update_cycle_button_style(cooldown_button, 'cooldown', False)
        cooldown_button.clicked.connect(lambda checked: self._toggle_cycle_button('cooldown'))
        segment_button_container_layout.addWidget(cooldown_button)

        self.horizontal_scroll_area = scroll_area


    def _toggle_cycle_button(self, cycle_num):
        """Toggle the state of a cycle button"""
        # Toggle the state
        self.cycle_button_states[cycle_num] = not self.cycle_button_states[cycle_num]

        # Update button styling
        button = self.cycle_buttons[cycle_num]
        self._update_cycle_button_style(button, cycle_num, self.cycle_button_states[cycle_num])

    def _update_cycle_button_style(self, button, cycle_num, is_active):
        """Update button styling based on state"""
        # Base colors
        active_color = "#87CEEB"  # Light blue for active state
        inactive_color = "#000000"  # Dark black for inactive state

        color = active_color if is_active else inactive_color

        if cycle_num == 1:
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};                          
                    border-top-left-radius: 10px;
                    border-bottom-left-radius: 10px;
                    border-top-right-radius: 3px;
                    border-bottom-right-radius: 3px;
                    padding: 3px 3px;
                    color: white;
                    font-size: 10px;
                    font-family: Arial;
                    font-weight: bold;
                    max-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: #446699;
                }}
            """)
        elif cycle_num == 4:
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};                          
                    border-top-left-radius: 3px;
                    border-bottom-left-radius: 3px;
                    border-top-right-radius: 10px;
                    border-bottom-right-radius: 10px;
                    padding: 3px 3px;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    font-family: Arial;
                    max-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: #446699;
                }}
            """)
        else:
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    border-radius: 3px;
                    padding: 3px 3px;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    font-family: Arial;
                    max-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: #446699;
                }}
            """)

    def get_cycle_button_state(self, cycle_num):
        """Get the current state of a cycle button"""
        return self.cycle_button_states.get(cycle_num, False)

    def set_cycle_button_state(self, cycle_num, state):
        """Set the state of a cycle button programmatically"""
        if cycle_num in self.cycle_button_states:
            self.cycle_button_states[cycle_num] = state
            button = self.cycle_buttons[cycle_num]
            self._update_cycle_button_style(button, cycle_num, state)

    def eventFilter(self, obj, event):
        if obj is getattr(self, 'horizontal_scroll_area', None) and event.type() == QEvent.Wheel:
            delta = event.angleDelta().y()
            bar = obj.horizontalScrollBar()
            bar.setValue(bar.value() - delta)
            return True
        return super().eventFilter(obj, event)

    def show_plot_settings(self, plot_type: str):
        """Show plot settings based on selected type and populate columns"""
        try:
            self.current_plot_type = plot_type

            # Create label for default plot preview window
            plot_label = QLabel()
            plot_label.setStyleSheet("""
                background-color: transparent;
                color:black;
            """)

            if plot_type == 'temp_matrix':
                # Set visible the temperature data selection window
                self.toggle_visibility(self.ui.tempMatrixDataSelection)

                # Switch to plot window
                self.ui.contentStack.setCurrentWidget(self.ui.plots)
                self.ui.lblCurentSection.setText("Temperature Matrix")
                self.create_temperature_matrix_plot()

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            if plot_type == 'temperature':

                self.ui.plotTopBar.show()

                # Setting the current section name in the top bar label
                self.ui.lblCurentSection.setText('Temperature Plot')

                if self.temperature_data is not None:
                    self.update_plot_segment_scrollable()
                    columns = list(self.temperature_data.columns)
                    self.ui.comboBoxXAxisTemp.clear()
                    self.ui.comboBoxXAxisTemp.addItems(columns)
                    self.ui.comboBoxXAxisTemp.setCurrentText('time')
                    self.update_y_axis_options('temperature')

                    plot_label.setText('Temperature Plot Window')   # Default text in plot window frame

                    # Changing current tab name as temperature to facilitate the plot preview and inclusion in report
                    self.current_tab_name = 'temperature'
                else:
                    self.ui.lblLogInfo.setText("Please load the temperature data!")

            elif plot_type == 'pressure':
                self.ui.plotTopBar.show()

                # Setting the current section name in the top bar label
                self.ui.lblCurentSection.setText('Pressure Plot')

                if self.pressure_data is not None:
                    columns = list(self.pressure_data.columns)
                    self.ui.comboBoxXAxisPressure.clear()
                    self.ui.comboBoxXAxisPressure.addItems(columns)
                    self.update_y_axis_options('pressure')
                    self.ui.pressurePlotSetti.show()


                    plot_label.setText('Pressure Plot Window')  # Default text in plot window frame
                    self.toggle_visibility(self.ui.pressurePlotBtnFrame)
                else:
                    self.ui.lblLogInfo.setText("Please load the pressure data!")

            elif plot_type == 'both':
                self.ui.plotTopBar.show()

                # Setting the current section name in the top bar label
                self.ui.lblCurentSection.setText('Pressure and Temperature Plot')

                if self.temperature_data is not None and self.pressure_data is not None:
                    self.ui.comboBoxXAxisBoth.clear()
                    self.ui.comboBoxXAxisBoth.addItem('time')
                    self.ui.comboBoxXAxisTemp.setCurrentText('time')
                    self.update_y_axis_options('both')

                    plot_label.setText('Pressure and Temperature Plot Window')

            elif plot_type == 'thrust':
                self.ui.plotTopBar.show()

                # Setting the current section name in the top bar label
                self.ui.lblCurentSection.setText('Thrust Plot')

                plot_label.setText('Thrust Plot Window')

            plot_label.setAlignment(Qt.AlignCenter)

            # Adding label to plot layout for display
            self.plot_layout.addWidget(plot_label)

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error setting up plot settings: {str(e)}")

    def create_tabbed_pressure_plot(self, data, x_col, y_cols, range_min=None, range_max=None):
        """Create pressure plots in separate tabs"""
        try:
            # Closing any existing figures before creating new ones
            plt.close('all')

            # Clearing any existing figures stored in current figures
            self.current_figures.clear()

            # Clear existing plot layout
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            data_series = {col: col for col in y_cols}

            fig_full, ax_full = plt.subplots(figsize=(3, 1))
            self.figure = fig_full
            self.axes = ax_full

            # Plot background transparent
            fig_full.patch.set_alpha(0.0)
            ax_full.patch.set_alpha(0.0)

            # Add the enhanced valve operation indicators
            current_valve_annotation_mode = self.valve_annotation_toggle_Pressure.get_current_mode()
            if current_valve_annotation_mode == 'On':
                # Add the enhanced valve operation indicators
                self.add_valve_indicators('pressure', ax_full)

            canvas_full = PlotCanvas(fig_full)
            self.canvas = canvas_full

            # Plot full range data with dynamically assigned colors
            for y_col in y_cols:
                # Get color for this column using our utility function
                color = assign_color(y_col, self.color, self.color_palette)

                ax_full.plot(data[x_col], data[y_col], label=y_col, linewidth=2, color=color)

            if range_min is not None and range_max is not None:
                # Highlight selected range
                ax_full.axvspan(range_min, range_max, color='green', alpha=0.2)

            x_label = self.ui.lnEdtXLabelPressure.text()
            y_label = self.ui.lnEdtYLabelPressure.text()

            if x_label == '':
                x_label = 'Time (s)'
                self.ui.lnEdtXLabelPressure.setText(x_label)
            if y_label == '':
                y_label = 'Pressure (mbar)'
                self.ui.lnEdtYLabelPressure.setText(y_label)

            self.plot_style(ax_full, x_label, y_label, 'Pressure Plot')

            # Store full range figure
            self.current_figures['full_range'] = fig_full

            # Create tab widget
            self.tab_widget = QTabWidget(self)
            self.tab_widget.setStyleSheet("""
                                    QTabWidget {
                        background-color: transparent;
                        border: none;
                        border-radius: 10px;
                    }

                    QWidget {
                        background-color: transparent;
                    }

                    QTabWidget::tab-bar {
                        alignment: center;
                    }

                    QTabBar::tab {
                        border-radius: 5px;
                        width: 120px;
                        height: 20px;
                        color: black;
                        font-size: 16px;
                        font-family: inter;
                        padding: 2px;
                        background-color: #ffffff;  /* Optional: White background for tabs */
                    }

                    QTabBar::tab:selected {
                        background: black;
                        color: white;
                    }

                    QTabBar::tab:hover {
                        background: #787878;
                    }

                    QTabWidget::pane {  /* Target the content area frame */
                        border: none;  /* Remove border */
                        background-color: transparent;  /* Make background transparent */
                        border-radius: 10px;  /* Match the tab widget's border radius */
                    }
                                """)

            self.tab_widget.setContentsMargins(0, 0, 0, 0)

            self.tab_widget.currentChanged.connect(self.update_current_tab_name)

            # Create Selected Range tab if range is specified
            if range_min >= 0 and range_max != 0:
                selected_range_tab = QWidget()
                selected_range_layout = QVBoxLayout(selected_range_tab)
                selected_range_tab.setStyleSheet("QWidget{background-color:#fcf1ff;}")

                # Create figure with same dimensions as full range
                fig_selected, ax_selected = plt.subplots(figsize=(3, 1))

                self.figure_selected = fig_selected
                self.axes_selected = ax_selected

                # Make plot background transparent
                fig_selected.patch.set_alpha(0.0)
                ax_selected.patch.set_alpha(0.0)

                canvas_selected = PlotCanvas(fig_selected)
                self.canvas_selected = canvas_selected

                # Create mask for selected range
                mask = (data[x_col] >= range_min) & (data[x_col] <= range_max)
                range_data = data[mask].copy()

                # Plot selected range data with dynamically assigned colors
                for y_col in y_cols:
                    # Get color for this column using our utility function
                    color = assign_color(y_col, self.color, self.color_palette)

                    ax_selected.plot(range_data[x_col], range_data[y_col],
                                     label=y_col, linewidth=2, color=color)

                    # Calculate and plot average
                    avg = range_data[y_col].mean()
                    self.axhline = ax_selected.axhline(y=avg, color='r', linestyle='--',
                                        label=f'{y_col} Avg = {avg:.2f} mbar')  # Unit for average pressure values displayed on the plot's legend



                self.plot_style(ax_selected, x_label, y_label, 'Pressure Plot - Selected Range')

                legend = ax_selected.legend(bbox_to_anchor=(1, 1),
                                            loc='upper left',
                                            facecolor='none',  # Transparent background
                                            edgecolor='#446699'  # Border color
                                            )

                # Setting legend text color
                for text in legend.get_texts():
                    text.set_color('#09090b')

                # Store selected range figure
                self.current_figures['selected_range'] = fig_selected

                # Add toolbar for selected range plot
                toolbar_selected = CustomNavigationToolbar(canvas_selected, selected_range_tab, data_series)
                selected_range_layout.addWidget(toolbar_selected)
                selected_range_layout.addWidget(canvas_selected)

                # Add selected range tab
                self.tab_widget.addTab(selected_range_tab, "Selected Range")

            # Creating Full Range tab
            full_range_tab = QWidget()
            full_range_layout = QVBoxLayout(full_range_tab)
            full_range_tab.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            full_range_tab.setContentsMargins(0, 0, 0, 0)

            # Add toolbar for full range plot
            toolbar_full = CustomNavigationToolbar(canvas_full, full_range_tab, data_series)
            full_range_layout.addWidget(toolbar_full)
            full_range_layout.addWidget(canvas_full)

            # Add tabs to widget
            self.tab_widget.addTab(full_range_tab, "Full Range")

            # Add tab widget to plot layout
            self.plot_layout.addWidget(self.tab_widget)

            # Update the current tab name initially
            self.update_current_tab_name(self.tab_widget.currentIndex())

            # Update the plot title dynamically
            self.update_current_plot_title()

            self.ui.lblLogInfo.setText("Pressure plots created successfully")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating pressure plots: {str(e)}")
            traceback.print_exc()
        finally:
            # Ensure any unused figures are closed
            for fig_num in plt.get_fignums():
                if plt.figure(fig_num) not in self.current_figures.values():
                    plt.close(fig_num)

    def update_current_plot_title(self):
        current_window = self.ui.lblCurentSection.text()
        plot_title = None

        if current_window == 'Pressure Plot':
            if self.tab_widget.count() > 1:
                if self.tab_widget.currentIndex() == 0:
                    plot_title = self.axes_selected.title.get_text()
                elif self.tab_widget.currentIndex() == 1:
                    plot_title = self.axes.title.get_text()
            elif self.tab_widget.count() == 1:
                plot_title = self.axes.title.get_text()
        else:
            plot_title = self.axes.title.get_text()

        self.ui.lnEditPlotTitle.setText(plot_title)

    def update_current_tab_name(self, index):
        if self.tab_widget is not None:
            self.current_tab_name = self.tab_widget.tabText(index)

        self.update_current_plot_title()

    def create_pressure_plot(self):
        try:
            # Clear any existing draggable annotations
            self.clear_draggable_annotations()

            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            if self.current_plot_type == 'pressure':
                x_col = self.ui.comboBoxXAxisPressure.currentText()
                y_cols = self.ui.comboBoxYAxisPressure.getCheckedItems()

                if x_col and y_cols:
                    # Get range values if provided
                    range_min = range_max = None
                    try:
                        if self.ui.lnEdtRangeMinPressure.text():
                            range_min = float(self.ui.lnEdtRangeMinPressure.text())
                        if self.ui.lnEdtRangeMaxPressure.text():
                            range_max = float(self.ui.lnEdtRangeMaxPressure.text())
                    except ValueError:
                        self.ui.lblLogInfo.setText("Invalid range values")
                        return

                    # Create tabbed pressure plots
                    self.create_tabbed_pressure_plot(
                        self.pressure_data,
                        x_col,
                        y_cols,
                        range_min,
                        range_max
                    )
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()

    def plot_it(self, figsize: tuple, x_column, y_columns):

        # Creating figure with transparent background
        fig, ax = plt.subplots(figsize=figsize)

        for col in y_columns:
            # Get color for this column using our utility function
            color = assign_color(col, self.color, self.color_palette)

            ax.plot(self.temperature_data[x_column],
                    self.temperature_data[col],
                    label=col,
                    linewidth=2,
                    color=color
                    )
        return fig, ax

    # Trial area for valve operation
    def add_valve_indicators(self, plot_type: str, axes):
        """Add creative and informative valve operation indicators to the plot"""
        print(f"[DEBUG] add_valve_indicators called with plot_type: {plot_type}")

        test_start_time = self.ui.subLnEdtSuctnValveSwtchOnTime.time()
        valve_on_time = self.ui.subLnEdtValveSwtchOnTime.time()
        valve_off_time = self.ui.subLnEdtValveSwtchOffTime.time()

        print(f"[DEBUG] Valve times - start: {test_start_time}, on: {valve_on_time}, off: {valve_off_time}")

        start_sec = test_start_time.secsTo(valve_on_time)
        end_sec = test_start_time.secsTo(valve_off_time)
        duration = end_sec - start_sec

        # Option 1: Gradient Background with Annotations
        def add_gradient_background():
            # Create gradient effect using multiple rectangles
            if plot_type == 'temperature':
                height = axes.get_ylim()[1] - axes.get_ylim()[0]
            elif plot_type == 'pressure':
                height = (axes.get_ylim()[1] - axes.get_ylim()[0]) * 1000 / 4
            y_bottom = axes.get_ylim()[0]

            # Multiple rectangles with decreasing alpha for gradient effect
            for i in range(30):
                alpha = 0.5 - (i * 0.015)  # Fade from 0.15 to 0.0
                rect_height = height * (0.1 + i * 0.09)  # Varying heights
                rect = Rectangle(
                    (start_sec, y_bottom + height * 0.05 * i),
                    duration, rect_height,
                    alpha=alpha,
                    facecolor='lightblue',
                    edgecolor='none'
                )
                axes.add_patch(rect)



        # Option 2: Animated-style indicators with arrows and text (now draggable)
        def add_arrow_indicators():
            if plot_type == 'temperature':
                y_max = axes.get_ylim()[1]
                y_min = axes.get_ylim()[0]
            elif plot_type == 'pressure':
                y_max = axes.get_ylim()[1] * 1000/4
                y_min = axes.get_ylim()[0] * 1000/4

            y_range = y_max - y_min

            # Valve ON arrow (with custom positioning if available)
            valve_on_key = f'valve_on_{plot_type}'
            default_on_xytext = (start_sec - duration * 0.3, y_max - y_range * 0.1)
            on_xytext = self.annotation_positions.get(valve_on_key, default_on_xytext)

            valve_on_annotation = axes.annotate(f'VALVE\nOPENS\n{start_sec}s',
                                              xy=(start_sec, y_max - y_range * 0.2),
                                              xytext=on_xytext,
                                              arrowprops=dict(arrowstyle='->', color='green', lw=2),
                                              fontsize=8, fontweight='bold', color='green',
                                              ha='center', va='bottom',
                                              bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.3))

            # Valve OFF arrow (with custom positioning if available)
            valve_off_key = f'valve_off_{plot_type}'
            default_off_xytext = (end_sec + duration * 0.3, y_max - y_range * 0.1)
            off_xytext = self.annotation_positions.get(valve_off_key, default_off_xytext)

            valve_off_annotation = axes.annotate(f'VALVE\nCLOSES\n{end_sec}s',
                                               xy=(end_sec, y_max - y_range * 0.3),
                                               xytext=off_xytext,
                                               arrowprops=dict(arrowstyle='->', color='red', lw=2),
                                               fontsize=8, fontweight='bold', color='red',
                                               ha='center', va='bottom',
                                               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.3))

            # Duration info (with custom positioning if available)
            duration_key = f'valve_duration_{plot_type}'
            mid_point = (start_sec + end_sec) / 2
            default_duration_xy = (mid_point, y_min + y_range)
            duration_xy = self.annotation_positions.get(duration_key, default_duration_xy)

            duration_annotation = axes.annotate(f'Opertn Durtn:\n{duration:.1f} sec',
                                              xy=duration_xy,
                                              fontsize=6, ha='center', va='bottom', fontweight='bold',
                                              bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.5))

            # Make annotations draggable
            print(f"[DEBUG] Checking if we can create draggable valve annotations...")
            print(f"[DEBUG] Has current_canvas: {hasattr(self, 'current_canvas')}")
            print(f"[DEBUG] Current canvas: {getattr(self, 'current_canvas', None)}")

            if hasattr(self, 'current_canvas') and self.current_canvas:
                print(f"[DEBUG] Creating draggable valve annotations. Canvas: {self.current_canvas}")

                draggable_on = DraggableAnnotation(valve_on_annotation, self.current_canvas, self, valve_on_key)
                draggable_off = DraggableAnnotation(valve_off_annotation, self.current_canvas, self, valve_off_key)
                draggable_duration = DraggableAnnotation(duration_annotation, self.current_canvas, self, duration_key)

                print(f"[DEBUG] Created valve annotations with keys: {valve_on_key}, {valve_off_key}, {duration_key}")

                # Note: Don't add to self.draggable_annotations here - the registration system handles it

                # Enable/disable dragging based on current mode
                drag_mode = getattr(self, 'annotation_drag_mode', True)
                print(f"[DEBUG] Drag mode: {drag_mode}")
                if not drag_mode:
                    print(f"[DEBUG] Disconnecting valve annotations (drag mode off)")
                    draggable_on.disconnect()
                    draggable_off.disconnect()
                    draggable_duration.disconnect()
                else:
                    print(f"[DEBUG] Valve annotations connected and ready for dragging")
            else:
                print(f"[DEBUG] Cannot create draggable valve annotations. Canvas: {getattr(self, 'current_canvas', None)}")

            # Add border lines
            axes.axvline(x=start_sec, color='blue', linestyle='--', linewidth=1, alpha=0.3, label='Valve ON')
            axes.axvline(x=end_sec, color='red', linestyle='--', linewidth=1, alpha=0.3, label='Valve OFF')

        # Option 3: Traffic light style indicator
        def add_traffic_light_indicator():
            y_max = axes.get_ylim()[1]
            y_range = axes.get_ylim()[1] - axes.get_ylim()[0]

            # Traffic light background
            light_height = y_range * 0.15
            light_width = duration * 0.1

            # Background for traffic light
            bg_rect = Rectangle(
                (start_sec - light_width / 4, y_max - light_height),
                light_width, light_height,
                facecolor='black', alpha=0.8, edgecolor='white', linewidth=2
            )
            axes.add_patch(bg_rect)

            # Green light (operation active)
            green_circle = plt.Circle((start_sec, y_max - light_height / 2),
                                      light_width / 6, color='lime', alpha=0.9)
            axes.add_patch(green_circle)

            # Add text
            axes.text(start_sec + light_width / 2, y_max - light_height / 2,
                      'ACTIVE', fontsize=8, fontweight='bold',
                      color='white', ha='left', va='center')

        # Option 4: Heatmap-style intensity bar
        def add_intensity_bar():
            from matplotlib.colors import LinearSegmentedColormap

            # Create custom colormap
            colors = ['white', 'lightblue', 'blue', 'darkblue']
            n_bins = 100
            cmap = LinearSegmentedColormap.from_list('valve_activity', colors, N=n_bins)

            # Create intensity data
            x_data = self.temperature_data[self.ui.comboBoxXAxisTemp.currentText()]
            intensity = np.zeros_like(x_data)

            # Set intensity to 1 during valve operation
            mask = (x_data >= start_sec) & (x_data <= end_sec)
            intensity[mask] = 1.0

            # Add intensity bar at bottom of plot
            y_min = axes.get_ylim()[0]
            bar_height = (axes.get_ylim()[1] - axes.get_ylim()[0]) * 0.05

            # Create meshgrid for imshow
            X = x_data
            Y = np.linspace(y_min - bar_height, y_min, 10)
            Z = np.tile(intensity, (len(Y), 1))

            im = axes.imshow(Z, aspect='auto', extent=[X.min(), X.max(), y_min - bar_height, y_min],
                             cmap=cmap, alpha=0.8, interpolation='bilinear')

            # Add colorbar
            cbar = plt.colorbar(im, ax=axes, orientation='horizontal',
                                shrink=0.3, pad=0.15, aspect=20)
            cbar.set_label('Valve Operation Intensity', fontsize=9)
            cbar.set_ticks([0, 1])
            cbar.set_ticklabels(['Closed', 'Open'])

        # Option 5: Modern UI-style timeline
        def add_timeline_indicator():
            y_pos = axes.get_ylim()[0] + (axes.get_ylim()[1] - axes.get_ylim()[0]) * 0.95

            # Timeline background
            timeline_rect = Rectangle(
                (axes.get_xlim()[0], y_pos - 20),
                axes.get_xlim()[1] - axes.get_xlim()[0], 40,
                facecolor='lightgray', alpha=0.3, edgecolor='gray'
            )
            axes.add_patch(timeline_rect)

            # Active period
            active_rect = Rectangle(
                (start_sec, y_pos - 15),
                duration, 30,
                facecolor='#4CAF50', alpha=0.8, edgecolor='#2E7D32', linewidth=2
            )
            axes.add_patch(active_rect)

            # Start and end markers
            axes.plot([start_sec, start_sec], [y_pos - 15, y_pos + 15],
                      color='#2E7D32', linewidth=3, marker='o', markersize=8)
            axes.plot([end_sec, end_sec], [y_pos - 15, y_pos + 15],
                      color='#2E7D32', linewidth=3, marker='s', markersize=8)

            # Time labels
            axes.text(start_sec, y_pos + 25, f'{start_sec:.1f}s',
                      ha='center', va='bottom', fontweight='bold', fontsize=9)
            axes.text(end_sec, y_pos + 25, f'{end_sec:.1f}s',
                      ha='center', va='bottom', fontweight='bold', fontsize=9)
            axes.text((start_sec + end_sec) / 2, y_pos, f'Δt = {duration:.1f}s',
                      ha='center', va='center', fontweight='bold', fontsize=10, color='white')

        # Choose which indicator to use (you can combine multiple)
        try:
            # Comment/uncomment the ones you want to use
            # add_gradient_background()
            add_arrow_indicators()
            # add_traffic_light_indicator()
            # add_intensity_bar()
            # add_timeline_indicator()

            print(f"Enhanced valve indicators added: {start_sec:.1f}s to {end_sec:.1f}s (Duration: {duration:.1f}s)")

        except Exception as e:
            print(f"Error adding enhanced valve indicators: {str(e)}")

    def add_heater_annotation(self,axes):
        """Add heater operation indicators to the plot"""
        test_start_time = self.ui.subLnEdtSuctnValveSwtchOnTime.time()

        # Heater switch on/off times for each cycle
        self.cycle_on_off_time = {
            **{f'cyc_{i}_on_time': (test_start_time.secsTo(getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOnTime').time())) for i in range(1, 5)},
            **{f'cyc_{i}_off_time': (test_start_time.secsTo(getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOffTime').time())) for i in range(1, 5)}
        }

        # Cycle duration
        self.cyc_1_duration = self.cycle_on_off_time['cyc_1_off_time'] - self.cycle_on_off_time['cyc_1_on_time']
        self.cyc_2_duration = self.cycle_on_off_time['cyc_2_off_time'] - self.cycle_on_off_time['cyc_2_on_time']
        self.cyc_3_duration = self.cycle_on_off_time['cyc_3_off_time'] - self.cycle_on_off_time['cyc_3_on_time']
        self.cyc_4_duration = self.cycle_on_off_time['cyc_4_off_time'] - self.cycle_on_off_time['cyc_4_on_time']

        def add_heater_cycle(cycle_num, on_time, off_time, duration, temp_at_on, temp_at_off, pressr_at_on =None, pressr_at_off =None):
            """Add heater cycle indicators to the plot"""
            y_max = axes.get_ylim()[1]
            y_min = axes.get_ylim()[0]
            y_range = y_max - y_min

            temp_at_on_str = ''
            temp_at_off_str = ''
            for i in range(len(temp_at_on)):
                if i == 0:
                    temp_at_on_str += f'{temp_at_on[i]}°C'
                    temp_at_off_str += f'{temp_at_off[i]}°C'
                else:
                    temp_at_on_str += f'|{temp_at_on[i]}°C'
                    temp_at_off_str += f'|{temp_at_off[i]}°C'

            if pressr_at_on is not None and pressr_at_off is not None and cycle_num == 1:
                pressr_at_on_str = f'{pressr_at_on}bar'
                pressr_at_off_str = f'{pressr_at_off}bar'


                print(f"The pressure at on: {pressr_at_on_str}")

            # Add heater events with custom positioning if available
            heater_on_key = f'heater_on_cyc{cycle_num}'
            default_on_xy = (on_time, y_min)
            on_xy = self.annotation_positions.get(heater_on_key, default_on_xy)

            heater_on_annotation = axes.annotate(f'ON {on_time}s\n{temp_at_on_str}\n{pressr_at_on_str if pressr_at_on is not None else ""}',
                                               xy=on_xy,
                                               fontsize=6, fontweight='bold', color='black', rotation=45,
                                               ha='center', va='bottom',
                                               bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.3))

            heater_off_key = f'heater_off_cyc{cycle_num}'
            default_off_xy = (off_time, y_min + y_range * 0.03)
            off_xy = self.annotation_positions.get(heater_off_key, default_off_xy)

            heater_off_annotation = axes.annotate(f'OFF {off_time}s\n{temp_at_off_str}\n{pressr_at_off_str if pressr_at_off is not None else ""}',
                                                xy=off_xy,
                                                fontsize=6, fontweight='bold', color='black', rotation=45,
                                                ha='center', va='bottom',
                                                bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.3))

            # Duration info
            duration_key = f'heater_duration_cyc{cycle_num}'
            mid_point = (on_time + off_time) / 2
            default_duration_xy = (mid_point, y_min + y_range * 0.5)
            duration_xy = self.annotation_positions.get(duration_key, default_duration_xy)

            duration_annotation = axes.annotate(f'Cyc-{cycle_num} Dur: {duration:.1f} s',
                                              xy=duration_xy,
                                              fontsize=6, ha='center', va='bottom', rotation=45, fontweight='bold',
                                              bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.15))

            # Make annotations draggable
            print(f"[DEBUG] Creating draggable heater annotations for cycle {cycle_num}")
            if hasattr(self, 'current_canvas') and self.current_canvas:
                draggable_on = DraggableAnnotation(heater_on_annotation, self.current_canvas, self, heater_on_key)
                draggable_off = DraggableAnnotation(heater_off_annotation, self.current_canvas, self, heater_off_key)
                draggable_duration = DraggableAnnotation(duration_annotation, self.current_canvas, self, duration_key)

                print(f"[DEBUG] Created heater annotations for cycle {cycle_num}")

                # Note: Don't add to self.draggable_annotations here - the registration system handles it

                # Enable/disable dragging based on current mode
                if not getattr(self, 'annotation_drag_mode', True):
                    draggable_on.disconnect()
                    draggable_off.disconnect()
                    draggable_duration.disconnect()
            else:
                print(f"[DEBUG] Cannot create draggable heater annotations for cycle {cycle_num}. Canvas: {getattr(self, 'current_canvas', None)}")
            # Add border lines
            axes.axvline(x=on_time, color='purple', linestyle='--', linewidth=1, alpha=0.5, )
            axes.axvline(x=off_time, color='red', linestyle='--', linewidth=1, alpha=0.5, )
        # Add heater cycle indicators for each cycle
        try:
            selected_column = self.ui.comboBoxYAxisTemp.getCheckedItems()
            if self.cyc_1_duration > 0:
                self.cyc_1_on_temps = []
                self.cyc_1_off_temps = []
                self.cyc_1_on_pressr = None
                self.cyc_1_off_pressr = None
                for col in selected_column:
                    self.cyc_1_on_temps.append(round(float(self.temperature_data[col][
                        self.temperature_data['time'] == self.cycle_on_off_time['cyc_1_on_time']].values[0]), 2))
                    self.cyc_1_off_temps.append(round(float(self.temperature_data[col][
                        self.temperature_data['time'] == self.cycle_on_off_time['cyc_1_off_time']].values[0]), 2))
                    self.cyc_1_on_pressr = round(float(self.pressure_data['Tank Pressure'][
                        self.pressure_data['time'] == self.cycle_on_off_time['cyc_1_on_time']].values[0]), 2)
                    self.cyc_1_off_pressr = round(float(self.pressure_data['Tank Pressure'][
                        self.pressure_data['time'] == self.cycle_on_off_time['cyc_1_off_time']].values[0]), 2)
                print(f"Cyc 1 On Temp: {self.cyc_1_on_temps}, Cyc 1 Off Temp: {self.cyc_1_off_temps}")
                add_heater_cycle(1, self.cycle_on_off_time['cyc_1_on_time'], self.cycle_on_off_time['cyc_1_off_time'],
                                 self.cyc_1_duration, self.cyc_1_on_temps, self.cyc_1_off_temps, self.cyc_1_on_pressr,
                                 self.cyc_1_off_pressr)

            if self.cyc_2_duration > 0:
                self.cyc_2_on_temps = []
                self.cyc_2_off_temps = []
                for col in selected_column:
                    self.cyc_2_on_temps.append(round(float(self.temperature_data[col][
                                                         self.temperature_data['time'] == self.cycle_on_off_time[
                                                             'cyc_2_on_time']].values[0]), 2))
                    self.cyc_2_off_temps.append(round(float(self.temperature_data[col][
                                                          self.temperature_data['time'] == self.cycle_on_off_time[
                                                              'cyc_2_off_time']].values[0]), 2))
                print(f"Cyc 2 On Temp: {self.cyc_2_on_temps}, Cyc 2 Off Temp: {self.cyc_2_off_temps}")
                add_heater_cycle(2, self.cycle_on_off_time['cyc_2_on_time'], self.cycle_on_off_time['cyc_2_off_time'],
                                 self.cyc_2_duration, self.cyc_2_on_temps, self.cyc_2_off_temps)
            if self.cyc_3_duration > 0:
                self.cyc_3_on_temps = []
                self.cyc_3_off_temps = []
                for col in selected_column:
                    self.cyc_3_on_temps.append(round(float(self.temperature_data[col][
                                                         self.temperature_data['time'] == self.cycle_on_off_time[
                                                             'cyc_3_on_time']].values[0]), 2))
                    self.cyc_3_off_temps.append(round(float(self.temperature_data[col][
                                                          self.temperature_data['time'] == self.cycle_on_off_time[
                                                              'cyc_3_off_time']].values[0]), 2))
                print(f"Cyc 3 On Temp: {self.cyc_3_on_temps}, Cyc 3 Off Temp: {self.cyc_3_off_temps}")
                add_heater_cycle(3, self.cycle_on_off_time['cyc_3_on_time'], self.cycle_on_off_time['cyc_3_off_time'],
                                 self.cyc_3_duration, self.cyc_3_on_temps, self.cyc_3_off_temps)
            if self.cyc_4_duration > 0:
                self.cyc_4_on_temps = []
                self.cyc_4_off_temps = []
                for col in selected_column:
                    self.cyc_4_on_temps.append(round(float(self.temperature_data[col][
                                                         self.temperature_data['time'] == self.cycle_on_off_time[
                                                             'cyc_4_on_time']].values[0]), 2))
                    self.cyc_4_off_temps.append(round(float(self.temperature_data[col][
                                                          self.temperature_data['time'] == self.cycle_on_off_time[
                                                              'cyc_4_off_time']].values[0]), 2))
                print(f"Cyc 4 On Temp: {self.cyc_4_on_temps}, Cyc 4 Off Temp: {self.cyc_4_off_temps}")
                add_heater_cycle(4, self.cycle_on_off_time['cyc_4_on_time'], self.cycle_on_off_time['cyc_4_off_time'],
                                 self.cyc_4_duration, self.cyc_4_on_temps, self.cyc_4_off_temps)
        except Exception as e:
            print(f"Error adding heater indicators: {str(e)}")

    #____________________________________________________####################__________________________________________#



    def plot_style(self, ax, x_label, y_label, title):

        # Update plot settings
        ax.set_title(title,
                     color='#09090b',
                     fontsize=12,
                     fontweight='bold')

        ax.set_xlabel(x_label,
                      color='#09090b',
                      fontsize=10,
                      fontweight='bold')
        ax.set_ylabel(y_label,
                      color='#09090b',
                      fontsize=10,
                      fontweight='bold')

        legend = ax.legend(bbox_to_anchor=(0.5, -0.13),
                           loc='upper center',
                           ncol=5,
                           columnspacing=1,
                           handletextpad=0.5,
                           borderaxespad=0,
                           facecolor='none',  # Transparent background
                           edgecolor='#446699',  # Border color
                           bbox_transform=ax.transAxes
                           )

        # Setting legend text color
        for text in legend.get_texts():
            text.set_color('#09090b')

        # Style the axis numbers and grid
        ax.tick_params(axis='both',
                       colors='#09090b',  # White tick labels
                       labelsize=9)
        # Major grids
        ax.grid(which='major', linestyle='-', linewidth='0.75', alpha=0.6, color='#666666')  # Darker grid lines

        # Minor grids
        ax.minorticks_on()
        ax.grid(True, which='minor', linestyle=':', linewidth='0.5', color='#1e1e1e', alpha=0.6)

        ax.spines['top'].set_visible(True)
        ax.spines['right'].set_visible(True)

        # Style the spines (axis lines)
        for spine in ax.spines.values():
            spine.set_color('#446699')  # Custom spine color


        if title == "Temperature and Pressure Plot":
            legend = self.pressure_axes.legend(bbox_to_anchor=(0.5, -0.2),
                               loc='upper center',
                               ncol=5,
                               columnspacing=1,
                               handletextpad=0.5,
                               borderaxespad=0,
                               facecolor='none',  # Transparent background
                               edgecolor='#446699',  # Border color
                               bbox_transform=ax.transAxes
                               )

            # Setting legend text color
            for text in legend.get_texts():
                text.set_color('#09090b')

    def plot_duration_based_on_button_states(self):
        start_time = self.cycle_on_off_time['cyc_1_on_time']
        end_time = self.cycle_on_off_time['cyc_1_off_time']
        return start_time, end_time

    def create_temperature_plot(self):
        try:
            # Clear any existing draggable annotations
            self.clear_draggable_annotations()

            # Close any existing figures
            plt.close('all')

            # Clear existing plot layout
            self.clear_plot_layout()

            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            self.current_figures.clear()  # Emptying any existing plots stored in it

            current_valve_annotation_mode = self.valve_annotation_toggle_Temp.get_current_mode()
            print(f"[DEBUG] Valve annotation mode: {current_valve_annotation_mode}")
            if current_valve_annotation_mode == 'On':
                print(f"[DEBUG] Adding valve indicators to temperature plot")
                # Add the enhanced valve operation indicators
                self.add_valve_indicators('temperature', self.axes)
            else:
                print(f"[DEBUG] Valve annotations are OFF - not creating draggable annotations")

            current_heater_annotation_mode = self.heater_annotation_toggle_Temp.get_current_mode()
            print(f"[DEBUG] Heater annotation mode: {current_heater_annotation_mode}")
            if current_heater_annotation_mode == 'On':
                print(f"[DEBUG] Adding heater indicators to temperature plot")
                # Add heater operation indicators
                self.add_heater_annotation(self.axes)
            else:
                print(f"[DEBUG] Heater annotations are OFF - not creating draggable annotations")

            start_time, end_time = self.plot_duration_based_on_button_states()

            x_col = self.ui.comboBoxXAxisTemp.currentText()
            y_cols = self.ui.comboBoxYAxisTemp.getCheckedItems()

            data_series = {col: col for col in y_cols}

            # Create figure and axes with specific size (matching pressure plot)
            self.figure, self.axes = plt.subplots(figsize=(9, 3))

            # Plot data with transparent background and colors from temperature selection widget
            for col in y_cols:
                # Get color from the temperature selection widget if available, otherwise use fallback
                if hasattr(self, 'temp_selection_widget') and self.temp_selection_widget:
                    color = self.temp_selection_widget.get_column_color(col)
                else:
                    # Fallback color assignment
                    if col not in self.color:
                        color_index = len(self.color) % len(self.color_palette)
                        self.color[col] = self.color_palette[color_index]
                    color = self.color[col]

                self.axes.plot(self.temperature_data[x_col][(self.temperature_data[x_col] >= start_time) & (self.temperature_data[x_col] <= end_time)],
                               self.temperature_data[col][(self.temperature_data[x_col] >= start_time) & (self.temperature_data[x_col] <= end_time)],
                               label=col,
                               linewidth=2,
                               color=color
                               )

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)  # Transparent figure background
            self.axes.patch.set_alpha(0.0)  # Transparent axes background

            # Set labels
            x_label = self.ui.lnEdtXLabelTemp.text()
            y_label = self.ui.lnEdtYLabelTemp.text()

            if x_label == '':
                x_label = 'Time (s)'
                self.ui.lnEdtXLabelTemp.setText(x_label)
            if y_label == '':
                y_label = 'Temperature (°C)'
                self.ui.lnEdtYLabelTemp.setText(y_label)

            # Create canvas FIRST so it's available for draggable annotations
            self.canvas = PlotCanvas(self.figure)
            self.current_canvas = self.canvas  # Store reference for draggable annotations
            self.canvas.setStyleSheet("background-color: transparent;")  # Ensure canvas is transparent
            print(f"[DEBUG] Canvas created and assigned: {self.current_canvas}")

            # Apply plot styling (ensure it doesn’t override transparency)
            self.plot_style(self.axes, x_label, y_label, 'Temperature Plot')

            # Tight layout to avoid extra padding
            # self.figure.tight_layout()

            # Store the figure
            self.current_figures['temperature'] = self.figure

            # Add global event logging for debugging
            def log_all_events(event):
                if hasattr(event, 'button') and event.button == 1:  # Only log left clicks
                    print(f"[DEBUG] GLOBAL EVENT: {event.name} at ({getattr(event, 'x', 'N/A')}, {getattr(event, 'y', 'N/A')}) in axes: {getattr(event, 'inaxes', 'N/A')}")

            self.canvas.mpl_connect('button_press_event', log_all_events)
            self.canvas.mpl_connect('pick_event', lambda e: print(f"[DEBUG] GLOBAL PICK: {e.artist}"))
            self.canvas.mpl_connect('button_press_event', self.handle_double_click)
            self.canvas.mpl_connect('button_press_event', self.handle_right_click)

            plot_widget = QWidget()
            plot_widget_layout = QVBoxLayout(plot_widget)
            plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            self.plot_layout.addWidget(plot_widget)

            toolbar = CustomNavigationToolbar(self.canvas, plot_widget, data_series)

            self.plot_layout.addWidget(toolbar)

            plot_widget_layout.addWidget(toolbar)
            plot_widget_layout.addWidget(self.canvas)

            # Set parent widget background to transparent if needed
            self.ui.plots.setStyleSheet("background-color: transparent;")

            # Updating the plot title
            self.update_current_plot_title()

            print(f"The state of the cycle 1 button is {self.get_cycle_button_state(1)}")
            print(f"The pre-heat duration range is {0}s to {self.cycle_on_off_time['cyc_1_on_time']}s")
            print(
                f"The cycle 1 duration range is {self.cycle_on_off_time['cyc_1_on_time']}s to {self.cycle_on_off_time['cyc_1_off_time']}s")
            print(
                f"The cool down duration range is {self.cycle_on_off_time['cyc_4_off_time']}s to {self.temperature_data['time'].max()}s")

            self.ui.lblLogInfo.setText("Temperature plot created successfully")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()
        finally:
            # Ensure any unused figures are closed
            for fig_num in plt.get_fignums():
                if plt.figure(fig_num) not in self.current_figures.values():
                    plt.close(fig_num)

    def create_temperature_matrix_plot(self):
        """Create_plot to handle plot creation"""
        try:
            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            self.handle_temp_matrix_clicked()

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()

    def create_temp_n_pressure_plot(self):
        try:
            # Clear any existing draggable annotations
            self.clear_draggable_annotations()

            # Close any existing figures
            plt.close('all')

            # Clear existing plot layout
            self.clear_plot_layout()

            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            self.current_figures.clear()  # Emptying any existing plots stored in it

            x_col = self.ui.comboBoxXAxisBoth.currentText()
            temp_y_cols = self.ui.comboBoxY1AxisTemp.getCheckedItems()
            pressure_y_cols = self.ui.comboBoxY2AxisPressure.getCheckedItems()

            temp_data_series = {col: col for col in temp_y_cols}
            pressure_data_series = {col: col for col in pressure_y_cols}

            # Create figure and axes with specific size
            self.figure, self.axes = plt.subplots(figsize=(9, 3))

            # Plot data with transparent background and colors from temperature selection widget
            for col in temp_y_cols:
                # Get color from the temperature selection widget if available, otherwise use fallback
                if hasattr(self, 'temp_selection_widget') and self.temp_selection_widget:
                    color = self.temp_selection_widget.get_column_color(col)
                else:
                    # Fallback color assignment
                    if col not in self.color:
                        color_index = len(self.color) % len(self.color_palette)
                        self.color[col] = self.color_palette[color_index]
                    color = self.color[col]

                self.axes.plot(self.temperature_data[x_col],
                               self.temperature_data[col],
                               label=col,
                               linewidth=2,
                               color=color,
                               alpha = 0.6
                               )

            self.pressure_axes = self.axes.twinx()  # Create a twin axes for pressure

            # Plot pressure data with dynamically assigned colors
            for col in pressure_y_cols:
                # Get color for this column using our utility function
                color = assign_color(col, self.color, self.color_palette)

                self.pressure_axes.plot(self.pressure_data[x_col],
                                        self.pressure_data[col],
                                        label=col,
                                        linewidth=2,
                                        color=color,
                                        alpha = 0.6
                                        )

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)  # Transparent figure background
            self.axes.patch.set_alpha(0.0)  # Transparent axes background
            self.pressure_axes.patch.set_alpha(0.0)  # Transparent axes background for pressure

            # Set labels
            x_label = self.ui.lnEdtXLabelBoth.text()
            temp_y_label = self.ui.lnEdtY1LabelTemp.text()
            pressure_y_label = self.ui.lnEdtY2LabelPressure.text()

            if x_label == '':
                x_label = 'Time (s)'
                self.ui.lnEdtXLabelBoth.setText(x_label)
            if temp_y_label == '':
                temp_y_label = 'Temperature (°C)'
                self.ui.lnEdtY1LabelTemp.setText(temp_y_label)
            if pressure_y_label == '':
                pressure_y_label = 'Pressure (mbar)'
                self.ui.lnEdtY2LabelPressure.setText(pressure_y_label)

            # Create canvas FIRST so it's available for draggable annotations
            self.canvas = PlotCanvas(self.figure)
            self.current_canvas = self.canvas  # Store reference for draggable annotations
            self.canvas.setStyleSheet("background-color: transparent;")  # Ensure canvas is transparent
            print(f"[DEBUG] Canvas created and assigned for both plot: {self.current_canvas}")

            current_valve_annotation_mode = self.valve_annotation_toggle_Both.get_current_mode()
            if current_valve_annotation_mode == 'On':
                # Add the enhanced valve operation indicators
                self.add_valve_indicators('temperature', self.axes)

            current_heater_annotation_mode = self.heater_annotation_toggle_Both.get_current_mode()
            if current_heater_annotation_mode == 'On':
                # Add heater operation indicators
                self.add_heater_annotation(self.axes)

            # Apply plot styling (ensure it doesn’t override transparency)
            self.plot_style(self.axes, x_label, temp_y_label, 'Temperature and Pressure Plot')

            self.pressure_axes.set_ylabel(pressure_y_label, color='r', fontweight='bold', fontsize=10)

            # Tight layout to avoid extra padding
            # self.figure.tight_layout()

            # Store the figure
            self.current_figures['temperature_n_pressure'] = self.figure

            # Add global event logging for debugging
            def log_all_events(event):
                if hasattr(event, 'button') and event.button == 1:  # Only log left clicks
                    print(f"[DEBUG] GLOBAL EVENT: {event.name} at ({getattr(event, 'x', 'N/A')}, {getattr(event, 'y', 'N/A')}) in axes: {getattr(event, 'inaxes', 'N/A')}")

            self.canvas.mpl_connect('button_press_event', log_all_events)
            self.canvas.mpl_connect('pick_event', lambda e: print(f"[DEBUG] GLOBAL PICK: {e.artist}"))
            self.canvas.mpl_connect('button_press_event', self.handle_double_click)
            self.canvas.mpl_connect('button_press_event', self.handle_right_click)

            plot_widget = QWidget()
            plot_widget_layout = QVBoxLayout(plot_widget)
            plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            self.plot_layout.addWidget(plot_widget)

            print(f"The temp data series contains: {temp_data_series}")
            print(f"The pressure data series contains: {pressure_data_series}")

            data_frame = {'data_frame_1': temp_data_series, 'data_frame_2': pressure_data_series}

            print(f"The data frame contains: {data_frame}")

            toolbar = CustomNavigationToolbar(self.canvas, plot_widget, data_frame, 'Temperature Data', 'Pressure Data')

            self.plot_layout.addWidget(toolbar)

            plot_widget_layout.addWidget(toolbar)
            plot_widget_layout.addWidget(self.canvas)

            # Set parent widget background to transparent if needed
            self.ui.plots.setStyleSheet("background-color: transparent;")

            # Updating the plot title
            self.update_current_plot_title()

            self.ui.lblLogInfo.setText("Temperature and Pressure plot created successfully")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()
        finally:
            # Ensure any unused figures are closed
            for fig_num in plt.get_fignums():
                if plt.figure(fig_num) not in self.current_figures.values():
                    plt.close(fig_num)

    def create_thrust_plot(self):
        try:
            # Clear any existing draggable annotations
            self.clear_draggable_annotations()

            # Close any existing figures
            plt.close('all')

            # Clear existing plot layout
            self.clear_plot_layout()

            # Making sure to show the plots frame in the content stack
            self.ui.contentStack.setCurrentWidget(self.ui.plots)

            # Clear existing plot
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            self.current_figures.clear()  # Emptying any existing plots stored in it

            # Create thrust plot
            thuster_temperature_col = self.temperature_data[self.ui.comboBoxThrust.currentText()]  # Get selected temperature column for thrust calculation
            cf = self.ui.subLnEdtCoefOfThrust.text()    # Coefficient of Thrust
            mass_flow_rate = self.ui.subLnEdtMassFlowRate.text()  # Mass Flow Rate
            gamma = 1.66
            gas_constant = 372.097738  # Default value in J/kgK
            c_star = np.sqrt((1/gamma) * ((gamma + 1)/2)**((gamma + 1)/(gamma - 1)) * gas_constant * thuster_temperature_col)
            time = self.temperature_data['time']
            thrust = float(mass_flow_rate.split()[0]) * float(cf) * c_star * 10**(-3)

            self.figure, self.axes = plt.subplots(figsize=(9, 3))

            self.axes.plot(
                            time, thrust, label='Thrust (N)', linewidth=2
            )

            # Set figure and axes backgrounds to transparent
            self.figure.patch.set_alpha(0.0)  # Transparent figure background
            self.axes.patch.set_alpha(0.0)  # Transparent axes background

            # Set labels
            x_label = 'Time (s)'
            y_label = 'Thrust (mN)'

            # Add the enhanced valve operation indicators
            current_valve_annotation_mode = self.valve_annotation_toggle_Thrust.get_current_mode()
            if current_valve_annotation_mode == 'On':
                # Add the enhanced valve operation indicators
                self.add_valve_indicators('temperature', self.axes)

            # Apply plot styling (ensure it doesn’t override transparency)
            self.plot_style(self.axes, x_label, y_label, 'Thrust Plot')

            # Store the figure
            self.current_figures['thrust'] = self.figure

            # Create canvas and ensure it has a transparent background
            self.canvas = PlotCanvas(self.figure)
            self.canvas.setStyleSheet("background-color: transparent;")  # Ensure canvas is transparent

            plot_widget = QWidget()
            plot_widget_layout = QVBoxLayout(plot_widget)
            plot_widget.setStyleSheet("QWidget{background-color:#fcf1ff;}")
            self.plot_layout.addWidget(plot_widget)

            data_series = {col: col for col in self.temperature_data if col == 'Thruster Chamber (°C)'}
            toolbar = CustomNavigationToolbar(self.canvas, plot_widget, data_series)

            self.plot_layout.addWidget(toolbar)

            plot_widget_layout.addWidget(toolbar)
            plot_widget_layout.addWidget(self.canvas)

            # Set parent widget background to transparent if needed
            self.ui.plots.setStyleSheet("background-color: transparent;")

            # Updating the plot title
            self.update_current_plot_title()

            self.ui.lblLogInfo.setText("Thrust plot created successfully")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating plot: {str(e)}")
            traceback.print_exc()

    def handle_plot_inclusion(self):
        """Handle plot inclusion when button is clicked"""
        try:
            if not self.current_figures:
                self.ui.lblLogInfo.setText("No plots available to include")
                return

            # Get current plot information
            plot_info = self._get_current_plot_info()
            if not plot_info:
                return

            # Save plots based on type
            if self.ui.lblCurentSection.text() == 'Pressure Plot':
                self._handle_pressure_plot_inclusion(plot_info)
            elif self.ui.lblCurentSection.text() in ['Temperature Plot', 'Performance', 'Temperature Matrix Plot', 'Both Plots', 'Thrust Plot']:
                self._handle_temperature_plot_inclusion(plot_info)

            self.ui.lblLogInfo.setText(f"Plots for '{plot_info['title']}' added to report")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error including plot: {str(e)}")
            traceback.print_exc()

    def _get_current_plot_info(self):
        """Get current plot title and type"""
        try:
            current_plot = self.ui.lblCurentSection.text()

            if current_plot == 'Pressure Plot':
                if self.tab_widget.count() > 1:
                    if self.tab_widget.currentIndex() == 0:
                        title = self.axes_selected.title.get_text()
                    else:
                        title = self.axes.title.get_text()
                else:
                    title = self.axes.title.get_text()
            else:
                # Get title from current axes
                if hasattr(self, 'axes') and self.axes is not None:
                    title = self.axes.get_title()
                    if not title:  # If title is empty
                        title = "Max Temperature Distribution Across Locations" if current_plot == "Temperature Matrix Plot" else "Temperature Plot"
                else:
                    title = "Temperature Plot"

            return {
                'title': title,
                'type': 'pressure' if current_plot == 'Pressure Plot' else self.current_plot_type
            }
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error getting plot info: {str(e)}")
            traceback.print_exc()
            return None

    def _handle_pressure_plot_inclusion(self, plot_info):
        """Handle pressure plot inclusion logic"""
        try:
            plots_dir = self._create_plot_directory('pressure')

            # Only save the currently visible tab
            if self.current_tab_name == 'Full Range':
                self._save_single_plot(
                    self.current_figures.get('full_range'),
                    plots_dir,
                    plot_info['title'],
                    'Full Range',
                    'pressure'
                )
            elif self.current_tab_name == 'Selected Range':
                self._save_single_plot(
                    self.current_figures.get('selected_range'),
                    plots_dir,
                    plot_info['title'],
                    'Selected Range',
                    'pressure'
                )
        except Exception as e:
            raise Exception(f"Error in pressure plot inclusion: {str(e)}")

    def _handle_temperature_plot_inclusion(self, plot_info):
        """Handle temperature plot inclusion logic"""
        try:
            plots_dir = self._create_plot_directory('temperature')

            # Handle temperature matrix plot specifically
            if self.current_plot_type == 'temperature_matrix':
                if 'temperature_matrix' in self.current_figures:
                    self._save_single_plot(
                        self.current_figures['temperature_matrix'],
                        plots_dir,
                        plot_info['title'],
                        'matrix',
                        'temperature'
                    )
                    return

            # Handle other temperature plots
            for view_type, fig in self.current_figures.items():
                if fig is not None:
                    self._save_single_plot(
                        fig,
                        plots_dir,
                        plot_info['title'],
                        view_type,
                        self.current_plot_type
                    )
        except Exception as e:
            print(f"Error in temperature plot inclusion: {str(e)}")  # Added print for debugging
            raise Exception(f"Error in temperature plot inclusion: {str(e)}")

    def _create_plot_directory(self, plot_type):
        """Create and return plot directory path"""
        plots_dir = os.path.join(self.get_executable_path(), "temp_report", "plots", plot_type)
        os.makedirs(plots_dir, exist_ok=True)
        return plots_dir

    def _clone_figure_for_report(self, fig):
        """Clone a figure without applying tight_layout for report generation"""
        try:
            # Create a new figure with the same size
            new_fig = plt.figure(figsize=(10, 7), dpi=300)

            # Copy the content from the original figure
            for ax in fig.get_axes():
                # Get the position and add a new axes at the same position
                new_ax = new_fig.add_axes(ax.get_position().bounds)

                # Copy all lines
                for line in ax.get_lines():
                    if isinstance(line, plt.Line2D):
                        # Check if it's an axhline
                        if line.get_xdata()[0] == line.get_xdata()[-1]:
                            # This is likely an axhline
                            new_ax.axhline(y=line.get_ydata()[0],
                                           color=line.get_color(),
                                           linestyle=line.get_linestyle(),
                                           linewidth=line.get_linewidth(),
                                           label=line.get_label(),
                                           alpha=line.get_alpha())
                        else:
                            # Regular line plot
                            new_ax.plot(line.get_xdata(), line.get_ydata(),
                                        color=line.get_color(),
                                        linestyle=line.get_linestyle(),
                                        linewidth=line.get_linewidth(),
                                        marker=line.get_marker(),
                                        markersize=line.get_markersize(),
                                        label=line.get_label(),
                                        alpha=line.get_alpha())

                # Copy axis labels, title, and limits
                new_ax.set_xlabel(ax.get_xlabel())
                new_ax.set_ylabel(ax.get_ylabel())
                new_ax.set_title(ax.get_title())
                new_ax.set_xlim(ax.get_xlim())
                new_ax.set_ylim(ax.get_ylim())

                # Copy grid settings
                new_ax.grid(ax.get_grid())

                # Copy background transparency
                new_ax.patch.set_alpha(0.0)
                new_fig.patch.set_alpha(0.0)

                # Add legend if present with the same properties
                if ax.get_legend() is not None:
                    legend = ax.get_legend()
                    new_ax.legend(
                        bbox_to_anchor=legend.get_bbox_to_anchor(),
                        loc=legend._loc,
                        facecolor=legend.get_frame().get_facecolor(),
                        edgecolor=legend.get_frame().get_edgecolor(),
                        title=legend.get_title().get_text() if legend.get_title() else None
                    )

                # Copy text annotations
                for text in ax.texts:
                    new_ax.text(
                        text.get_position()[0],
                        text.get_position()[1],
                        text.get_text(),
                        fontsize=text.get_fontsize(),
                        color=text.get_color(),
                        alpha=text.get_alpha(),
                        rotation=text.get_rotation(),
                        horizontalalignment=text.get_horizontalalignment(),
                        verticalalignment=text.get_verticalalignment()
                    )

            # Set a reasonable minimum size
            new_fig.set_size_inches(8, 6)

            # Adjust the subplot parameters
            new_fig.subplots_adjust(
                left=0.15,
                right=0.95,
                bottom=0.15,
                top=0.9,
                wspace=0.2,
                hspace=0.2
            )

            return new_fig
        except Exception as e:
            print(f"Error cloning figure: {str(e)}")
            return fig  # Return original figure if cloning fails

    def _save_single_plot(self, fig, plots_dir, title, view_type, plot_type):
        """Save single plot and add to preview"""
        try:
            if fig is None:
                print("Figure is None, skipping save")  # Added debug print
                return

            # Create unique filename
            suffix = f"_{view_type}" if view_type else ""
            filename = f"{title.replace(' ', '_')}{suffix}.png"
            plot_path = os.path.join(plots_dir, filename)

            print(f"Saving plot to: {plot_path}")  # Added debug print

            # Clone the figure for report to avoid tight_layout distortion
            report_fig = self._clone_figure_for_report(fig)

            # Save the cloned plot without tight_layout
            report_fig.savefig(plot_path, bbox_inches='tight', dpi=300, facecolor='white')

            # Close the cloned figure to free memory
            plt.close(report_fig)

            # Create plot info
            display_title = f"{title} ({view_type})" if view_type else title
            if any(p['title'] == display_title for cat in ['default', 'custom'] for p in self.report_plots[cat]):
                return  # already saved → skip
            plot_info = {
                'path': plot_path,
                'title': display_title,
                'type': plot_type
            }

            print(f"Created plot info: {plot_info}")  # Added debug print

            # Check if plot already exists
            existing_plots = [p for p in self.report_plots['custom']
                              if p['title'] == display_title and p['type'] == plot_type]

            if not existing_plots:
                # Add to custom plots list and preview only if it doesn't exist
                self.report_plots['custom'].append(plot_info)
                self.add_plot_to_preview(plot_info)

                QMessageBox.information(self, "Plot Inclusion Status",
                                        f"Plot '{display_title}' will be added to the report.")
                self.ui.lblLogInfo.setText(f"Plot '{display_title}' added to report")
            else:
                print(f"Plot already exists: {display_title}")  # Added debug print

        except Exception as e:
            print(f"Error in _save_single_plot: {str(e)}")  # Added debug print
            raise

    def add_plot_to_preview(self, plot_info):
        """Enhanced version of add_plot_to_preview with interactivity"""
        try:
            # Initialize layout for scroll area widget if it doesn't exist
            if not self.ui.scrollAreaWidgetContents.layout():
                preview_layout = QVBoxLayout()
                self.ui.scrollAreaWidgetContents.setLayout(preview_layout)

            # Create preview container with enhanced styling
            preview_widget = QWidget()
            container_layout = QVBoxLayout(preview_widget)
            container_layout.setSpacing(5)
            container_layout.setContentsMargins(5, 5, 5, 5)
            preview_widget.setStyleSheet("""
                QWidget {
                    background-color: #e8e9ea;
                    border-radius: 5px;
                    padding: 5px;
                    margin: 2px;
                }
                QWidget:hover {
                    background-color: #b5b6b6;
                }
            """)

            # Header layout for title and remove button
            header_widget = QWidget()
            header_layout = QHBoxLayout(header_widget)
            header_layout.setContentsMargins(1, 1, 1, 1)

            # Add title
            title_label = QLabel(plot_info['title'])
            title_label.setStyleSheet("""
                QLabel {
                    color: black;
                    font-size: 12px;
                    padding: 5px;
                    background-color: transparent;
                }
            """)
            header_layout.addWidget(title_label)

            # Add remove button
            remove_button = QPushButton("X")
            remove_button.setFixedSize(25, 25)
            remove_button.setStyleSheet("""
                QPushButton {
                    background-color: #994444;
                    border-radius: 5px;
                    color: white;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #aa5555;
                }
            """)
            remove_button.clicked.connect(lambda: self.remove_plot_preview(plot_info))
            header_layout.addWidget(remove_button)

            container_layout.addWidget(header_widget)

            # Create small preview image
            preview_label = QLabel()
            preview_label.setStyleSheet("background-color: transparent;")
            small_pixmap = self.create_preview_pixmap(plot_info['path'], (234, 170))
            if small_pixmap:
                preview_label.setPixmap(small_pixmap)
                preview_label.setAlignment(Qt.AlignCenter)
                container_layout.addWidget(preview_label)

                # Make the entire widget clickable
                preview_widget.mousePressEvent = lambda e: self.show_large_plot(plot_info['path'])
                preview_widget.setCursor(Qt.PointingHandCursor)

                # Store widget reference in plot_info
                plot_info['widget'] = preview_widget

                # Add to scroll area with spacing
                self.ui.scrollAreaWidgetContents.layout().addWidget(preview_widget)
                self.ui.scrollAreaWidgetContents.layout().addSpacing(5)

                # Ensure scroll area updates
                self.ui.scrollAreaWidgetContents.updateGeometry()
                self.ui.scrlAreaReportPreview.viewport().update()

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error adding plot preview: {str(e)}")
            traceback.print_exc()

    def remove_plot_preview(self, plot_info):
        """Remove a plot preview and its associated file from temp_report folder"""
        try:
            # Remove widget from UI
            if 'widget' in plot_info:
                widget = plot_info['widget']
                self.ui.scrollAreaWidgetContents.layout().removeWidget(widget)
                widget.deleteLater()

            # Remove plot file from temp_report folder
            if 'path' in plot_info and os.path.exists(plot_info['path']):
                try:
                    os.remove(plot_info['path'])
                except Exception as e:
                    print(f"Error removing plot file: {str(e)}")

                # Also remove the directory if it's empty
                plot_dir = os.path.dirname(plot_info['path'])
                if os.path.exists(plot_dir) and not os.listdir(plot_dir):
                    try:
                        os.rmdir(plot_dir)
                    except Exception as e:
                        print(f"Error removing empty directory: {str(e)}")

            # Remove from report plots
            for category in ['default', 'custom']:
                self.report_plots[category] = [
                    plot for plot in self.report_plots[category]
                    if plot['path'] != plot_info['path']
                ]

            self.ui.lblLogInfo.setText(f"Removed plot: {plot_info['title']}")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error removing plot preview: {str(e)}")
            traceback.print_exc()

    def clear_plot_layout(self):
        """Clear all widgets from the plot layout"""
        try:
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                widget = item.widget()
                widget.setParent(None)
                widget.deleteLater()

            # Close all matplotlib figures
            plt.close('all')

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error clearing plot layout: {str(e)}")
            traceback.print_exc()

    def clear_current_plot(self):
        """Clear the current plot and associated widgets"""
        try:
            # Clear the plot layout
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

            # Close current figure if it exists
            if self.current_figure is not None:
                plt.close(self.current_figure)
                self.current_figure = None

            self.current_canvas = None
            self.current_toolbar = None
            if self.plot_container is not None:
                self.plot_container.deleteLater()
                self.plot_container = None

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error clearing plot: {str(e)}")
            traceback.print_exc()

    def create_preview_pixmap(self, image_path, size=(200, 150)):
        """Create a scaled preview pixmap from an image file"""
        try:
            if not os.path.exists(image_path):
                return None

            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                return pixmap.scaled(
                    size[0], size[1],
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
            return None

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error creating preview: {str(e)}")
            return None

    def show_large_plot(self, image_path):
        """Show larger version of the plot in the plot frame"""
        try:
            # Switch to plots view
            self.ui.contentStack.setCurrentWidget(self.ui.plots)
            self.ui.plotTopBar.hide()

            # Clear existing plot layout
            while self.plot_layout.count():
                item = self.plot_layout.takeAt(0)
                if item.widget():
                    widget = item.widget()
                    widget.setParent(None)
                    widget.deleteLater()

            # Create label for large plot
            plot_label = QLabel()
            plot_label.setStyleSheet("background-color: transparent;")

            # Calculate size based on plot frame size
            available_width = self.ui.plotFrame.width() - 10
            available_height = self.ui.plotFrame.height() - 10

            # Load and scale the image
            pixmap = QPixmap(image_path)
            scaled_pixmap = pixmap.scaled(
                available_width,
                available_height,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )

            plot_label.setPixmap(scaled_pixmap)
            plot_label.setAlignment(Qt.AlignCenter)

            # Add to layout
            self.plot_layout.addWidget(plot_label)

            self.ui.lblLogInfo.setText("Plot displayed")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error showing large plot: {str(e)}")
            traceback.print_exc()

    def add_default_plots_to_preview(self):
        """Add default temperature analysis plots to preview"""
        try:
            plot_configs = [
                ('all_temperatures.png', 'All Temperatures'),
                # ('thruster_temperatures.png', 'Thruster Temperatures'),
                # ('tank_temperatures.png', 'Tank Temperatures'),
                # ('chamber_temperature.png', 'Chamber Temperature'),
                # ('max_temperatures.png', 'Maximum Temperatures')
            ]

            base_path = self.get_executable_path()
            plots_dir = os.path.join(base_path, "temp_report", "plots", "temperature")

            if os.path.exists(plots_dir):
                # Clear existing default plots
                self.report_plots['default'] = []

                for filename, title in plot_configs:
                    plot_path = os.path.join(plots_dir, filename)
                    if os.path.exists(plot_path):
                        plot_info = {
                            'path': plot_path,
                            'title': title,
                            'type': 'temperature'
                        }
                        self.report_plots['default'].append(plot_info)
                        self.add_plot_to_preview(plot_info)

                self.ui.lblLogInfo.setText("Added default plots to preview")

            # Only try to delete temp plots folder if db_handler exists
            if hasattr(self, 'db_handler') and self.db_handler is not None:
                self.db_handler.delete_temp_plots_folder()

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error adding default plots: {str(e)}")
            traceback.print_exc()

    def clear_plot_previews(self):
        """Enhanced version of clear_plot_previews"""
        try:
            if self.ui.scrollAreaWidgetContents.layout():
                # Remove all widgets
                while self.ui.scrollAreaWidgetContents.layout().count():
                    item = self.ui.scrollAreaWidgetContents.layout().takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()

                # Reset plot tracking
                self.report_plots = {
                    'default': [],
                    'custom': []
                }

                self.ui.lblLogInfo.setText("Cleared all plot previews")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error clearing plot previews: {str(e)}")
            traceback.print_exc()

    def calculate_performance(self):
        """Calculate performance parameters with range-based pressure calculations"""

        try:
            # Change the content window to the appropriate page
            self.ui.contentStack.setCurrentWidget(self.ui.performance)

            # Get input values from UI
            gamma = float(self.ui.lnEdtGamma.text())
            gas_constant = float(self.ui.lnEdtGasConst.text())

            # Get chamber temperature
            if self.filtered_temp_data is not None:
                self.temp_analyzer.analyze_temperature_data(self.filtered_temp_data, output_dir=None, save_plot=False,
                                                            plot_types=None)
                chamber_temp = self.temp_analyzer.max_temperature_data['value_kelvin']
                max_temp_location = self.temp_analyzer.max_temperature_data['location']
                max_temp_time = self.temp_analyzer.max_temperature_data['time']

                # Update chamber temperature label
                self.ui.subLnEdtChambTemp.setText(
                    f"{chamber_temp:.2f}K @ {max_temp_location.split('(')[0].strip()}"
                )
            else:
                raise ValueError("Temperature analysis has not been performed yet")

            # Get propellant masses
            initial_mass = self.ui.subLnEdtWghtOfPropBefTest.value()  # in grams
            unused_mass = self.ui.subLnEdtWghtOfPropAftTest.value()  # in grams

            # Get pressure ranges from UI
            try:
                vac_min = self.ui.lnEdtVacPressRangeMin.value()
                vac_max = self.ui.lnEdtVacPressRangeMax.value()
                vac_range = (vac_min, vac_max)
            except (ValueError, AttributeError):
                vac_range = None
                print("Using full range for vacuum pressure")

            try:
                chamb_min = self.ui.lnEdtChambPressRangeMin.value()
                chamb_max = self.ui.lnEdtChambPressRangeMax.value()
                chamb_range = (chamb_min, chamb_max)
            except (ValueError, AttributeError):
                chamb_range = None
                print("Using full range for chamber pressure")

            # Verify pressure data is available
            if self.pressure_data is None:
                raise ValueError("Pressure data not available")

            # Get burn time from valve operation times
            on_time = self.ui.subLnEdtValveSwtchOnTime.time()
            off_time = self.ui.subLnEdtValveSwtchOffTime.time()
            burn_time = on_time.secsTo(off_time)

            # Initialize performance calculator
            performance_calc = Performance()
            performance_calc.set_properties(gas_constant, gamma)

            # Calculate parameters with range-based pressure
            results = performance_calc.calculate_performance_parameters(
                pressure_data=self.pressure_data,
                chamber_temp=chamber_temp,
                initial_mass=initial_mass,
                unused_mass=unused_mass,
                burn_time=burn_time,
                tank_press_range=chamb_range,
                vac_press_range=vac_range
            )

            # Format results
            formatted_results = performance_calc.format_performance_results(results)

            # Update UI with results
            self.ui.subLnEdtChambPressure.setText(f'{results["tank_pressure"]:.2f}mbar')
            self.ui.subLnEdtVacPressure.setText(f'{results["vacuum_pressure"]:.2f}mbar')
            self.ui.subLnEdtCharVelo.setText(formatted_results['characteristic_velocity'])
            self.ui.subLnEdtMassFlowRate.setText(formatted_results['mass_flow_rate'])
            self.ui.subLnEdtCoefOfThrust.setText(formatted_results['thrust_coefficient'])
            self.ui.subLnEdtThrust.setText(formatted_results['thrust'])
            self.ui.subLnEdtSpcImpulse.setText(formatted_results['specific_impulse'])
            self.ui.subLnEdtTotImpulse.setText(formatted_results['total_impulse'])
            self.ui.subLnEdtBurnTime.setText(formatted_results['burn_time'])

            # Create summary for the ranges used
            range_summary = "Pressure Ranges Used:\n"
            if vac_range:
                range_summary += f"Vacuum Pressure: {vac_min:.2f}s to {vac_max:.2f}s\n"
            else:
                range_summary += "Vacuum Pressure: Full range\n"
            if chamb_range:
                range_summary += f"Chamber Pressure: {chamb_min:.2f}s to {chamb_max:.2f}s"
            else:
                range_summary += "Chamber Pressure: Full range"

            # Store results
            self.performance_results = {
                'inputs': {
                    'gamma': gamma,
                    'gas_constant': gas_constant,
                    'chamber_temp': chamber_temp,
                    'initial_mass': initial_mass,
                    'unused_mass': unused_mass,
                    'vac_range': vac_range,
                    'chamb_range': chamb_range,
                    'burn_time': burn_time
                },
                'results': results
            }

            # Update log
            self.ui.lblLogInfo.setText("Performance parameters calculated successfully")

            # Perform enhanced analysis
            self.perform_enhanced_analysis()

            # Show ranges used
            QMessageBox.information(self, "Calculation Summary", range_summary)

        except ValueError as e:
            QMessageBox.critical(self, "Input Error",
                                 f"Please check your input values: {str(e)}")
            self.ui.lblLogInfo.setText("Error: Invalid input values")

    def save_plot_for_report(self, fig, plot_type, title):
        """Save plot information for report generation"""
        try:
            if not hasattr(self, 'report_plots'):
                self.report_plots = []

            # Create a unique filename
            filename = f"{plot_type}plot.png"

            # Create plots directory if it doesn't exist
            plots_dir = "report_plots"
            os.makedirs(plots_dir, exist_ok=True)

            # Save the figure
            filepath = os.path.join(plots_dir, filename)
            fig.savefig(filepath, bbox_inches='tight', dpi=300)

            # Store plot information
            plot_info = {
                'type': plot_type,
                'title': title,
                'filepath': filepath
            }

            self.report_plots.append(plot_info)
            self.ui.lblLogInfo.setText(f"Plot saved for report: {title}")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error saving plot for report: {str(e)}")

    def perform_enhanced_analysis(self):
        """Perform comprehensive enhanced analysis using all new analyzers."""
        try:
            # Collect current test data
            test_data = self.collect_form_data()

            # Perform heater analysis if heater data is available
            heater_cycles = test_data.get('heater_cycles', [])
            heater_info = test_data.get('heater_info', {})

            if heater_cycles and heater_info:
                heater_results = self.heater_analyzer.analyze_heater_cycles(
                    heater_cycles, heater_info, self.temperature_data)
                heater_summary = self.heater_analyzer.get_summary_metrics()

                # Store results for later use
                if not hasattr(self, 'enhanced_analysis_results'):
                    self.enhanced_analysis_results = {}
                self.enhanced_analysis_results['heater_analysis'] = heater_summary

                print("Heater analysis completed")
                print(f"Heater summary: {heater_summary}")

            # Perform thermal analysis if temperature data is available
            if self.temperature_data is not None:
                thermal_results = self.thermal_analyzer.analyze_thermal_response(
                    self.temperature_data, heater_cycles, test_data.get('test_details', {}))
                thermal_summary = self.thermal_analyzer.get_thermal_summary()

                if not hasattr(self, 'enhanced_analysis_results'):
                    self.enhanced_analysis_results = {}
                self.enhanced_analysis_results['thermal_analysis'] = thermal_summary

                print("Thermal analysis completed")
                print(f"Thermal summary: {thermal_summary}  ")

            # Perform efficiency analysis if performance data is available
            if hasattr(self, 'performance_results') and self.performance_results:
                performance_data = self.performance_results.get('results', {})

                heater_analysis = self.enhanced_analysis_results.get('heater_analysis', {}) if hasattr(self, 'enhanced_analysis_results') else {}
                thermal_analysis = self.enhanced_analysis_results.get('thermal_analysis', {}) if hasattr(self, 'enhanced_analysis_results') else {}

                efficiency_results = self.efficiency_analyzer.analyze_system_efficiency(
                    test_data, performance_data, heater_analysis, thermal_analysis)
                efficiency_summary = self.efficiency_analyzer.get_efficiency_summary()

                if not hasattr(self, 'enhanced_analysis_results'):
                    self.enhanced_analysis_results = {}
                self.enhanced_analysis_results['efficiency_analysis'] = efficiency_summary

                print("Efficiency analysis completed")
                print(f"Efficiency summary: {efficiency_summary}")

            # Update UI to indicate enhanced analysis is complete
            if hasattr(self, 'enhanced_analysis_results'):
                self.ui.lblLogInfo.setText("Enhanced analysis completed successfully")

                # Enable comparison features with enhanced analysis
                self.enhanced_analysis_available = True
            else:
                self.ui.lblLogInfo.setText("Enhanced analysis completed with limited data")

        except Exception as e:
            print(f"Error in enhanced analysis: {str(e)}")
            import traceback
            traceback.print_exc()
            self.ui.lblLogInfo.setText(f"Enhanced analysis error: {str(e)}")

    def get_enhanced_analysis_results(self):
        """Get the enhanced analysis results for use in comparisons."""
        if hasattr(self, 'enhanced_analysis_results'):
            return self.enhanced_analysis_results
        return {}

    def collect_form_data(self):
        """Collect all form data from UI inputs"""
        try:
            # Convert DataFrame to a serializable format
            filtered_temp_dict = None
            if self.filtered_temp_data is not None:
                filtered_temp_dict = {
                    'selected_columns': self.selected_cols,
                    'selected_ranges': self.selected_ranges
                }

            # Basic Information about the test
            if self.ui.lblTestNumber.text() == '':
                test_no = None
            else:
                test_no = self.ui.subLnEdtTestNo.value()

            test_date = self.ui.subLnEdtTestDate.text()

            basic_info = {
                'Aim': self.ui.subLnEdtAim.text(),
                'Propellant': self.ui.subLnEdtProp.text(),
                'Catalyst': self.ui.subLnEdtCat.text(),
                'Propellant_RI_Before_Test': self.ui.subLnEdtPropRI.text(),
                # 'firing_duration': self.ui.lblFiringDuration.text()
            }

            # System specifications
            system_specs = {
                'Chamber number': self.ui.subLnEdtChmbrNo.text(),
                'Chamber material': self.ui.subLnEdtChmbrMat.text(),
                'Chamber depth (mm)': self.ui.subLnEdtChmbrDept.text(),
                'Chamber internal diameter (mm)': self.ui.subLnEdtInternalChmbrDia.text(),
                'Chamber external diameter (mm)': self.ui.subLnEdtExternalChmbrDia.text(),
                'Nozzle throat dimension (mm)': self.ui.subLnEdtNozlThrtDime.text(),
                'Retainer plate orifice diameter (mm)': self.ui.subLnEdtRetainerPltOrfcDia.text(),
                'Injector orifice diameter (mm)': self.ui.subLnEdtInjectorOrificeDia.text()
            }

            # Propellant specifications
            propellant_specs = {
                'Type of Propellant': self.ui.subLnEdtTypeOfProp.text(),
                'Concentration before testing (%)': self.ui.subLnEdtConcBefTest.value(),
                'Stability (Old/New -MIL)': self.ui.subLnEdtStability.text(),
                'Weight_of_propellant_before the test (g)': self.ui.subLnEdtWghtOfPropBefTest.text(),
                'Weight_of_propellant_after the test (g)': self.ui.subLnEdtWghtOfPropAftTest.text()
            }

            # Catalyst Specifications
            catalyst_specs = {
                'Catalyst_type': self.ui.subLnEdtCatType.text(),
                'Catalyst_Grade/ Composition': self.ui.subLnEdtCatGrade.text(),
                'Catalyst_size (mm)': self.ui.subLnEdtCatSize.text(),
                'Weight_of_the_catalyst_before the test (g)': self.ui.subLnEdtCatWghtBefTest.text(),
                # 'Preheat_temperature (°C)': self.ui.subLnEdtPrehtTemp.text()
            }

            # Component Details
            component_details = [
                # 'Pressure_sensor_type': self.ui.subLnEdtPressSensType.text(),
                # 'Pressure_sensor_range (mbar)': self.ui.subLnEdtPressSensRange.text(),
                # 'Pressure_sensor_input_and_output': self.ui.subLnEdtPressSensIO.text(),
                {'Pressure_sensor_type': self.ui.Vac_Chamb_Pressure_Sensr_type_Input.text(),
                'Pressure_sensor_number_&_slope_equation': self.ui.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.text(),
                'Pressure_sensor_range': self.ui.Vac_Chamb_Pressure_Snsr_range_Input.text(),
                'Pressure_sensor_input_and_output': self.ui.Vac_Chamb_Pressure_Snsr_IO_Input.text()},
                {'Pressure_sensor_type': self.ui.Prop_Tank_Pressure_Sensr_type_Input.text(),
                 'Pressure_sensor_number_&_slope_equation': self.ui.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.text(),
                 'Pressure_sensor_range': self.ui.Prop_Tank_Pressure_Snsr_range_Input.text(),
                 'Pressure_sensor_input_and_output': self.ui.Prop_Tank__Pressure_Snsr_IO_Input.text()},
                {'Pressure_sensor_type': self.ui.Thruster_Pressure_Sensr_type_Input.text(),
                 'Pressure_sensor_number_&_slope_equation': self.ui.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.text(),
                 'Pressure_sensor_range': self.ui.Thruster_Pressure_Snsr_range_Input.text(),
                 'Pressure_sensor_input_and_output': self.ui.Thruster_Pressure_Snsr_IO_Input.text()},
                {'Heater_type': self.ui.subLnEdtHtrType.text(),
                'Heater_input_power_Htr_1 (W)': self.ui.subLnEdtHtrInpPowerHtr_1.value(),
                'Heater_input_power_Htr_2 (W)': self.ui.subLnEdtHtrInpPowerHtr_2.value(),
                'Heater_input_power_Htr_3 (W)': self.ui.subLnEdtHtrInpPowerHtr_3.value(),
                'Heater_input_power_Htr_4 (W)': self.ui.subLnEdtHtrInpPowerHtr_4.value(),
                'Heater_input_power_Htr_5 (W)': self.ui.subLnEdtHtrInpPowerHtr_5.value()}
            ]

            # Test Details
            test_details = {
                'Propellant_tank_heater_cut-off_temperature (°C)': self.ui.subLnEdtPropTnkHtrCtOfTemp.text(),
                'Propellant_tank_heater_reset_temperature (°C)': self.ui.subLnEdtPropTnkHtrRstTemp.text(),
                'Test_procedure': self.ui.subLblTestProcValue.toPlainText()
            }

            # Pump Operation
            pump_operation = {
                'Pump_start_time': self.ui.subLnEdtPmpStrtTime.text(),
                'Corresponding_tank_bottom_temperature (°C)': self.ui.subLnEdtCorrTkBtmTemp.value(),
                'Corresponding Thruster Temperature (°C)': self.ui.subLnEdtCorrThrstrTemp.value(),
                'Corresponding Tank Pressure (Bar)': self.ui.subLnEdtCorrTkPressure.value(),
                'Corresponding Thruster Pressure (Bar)': self.ui.subLnEdtCorrThrstrPressure.value()
            }

            # Suction Valve Operation
            suction_valve_operation = {
                'Suction_valve_open_time': self.ui.subLnEdtSuctnValveSwtchOnTime.text(),
                'Corresponding_tank_bottom_temperature (°C)': self.ui.subLnEdtSuctnCorrTkBtmTemp.value(),
                'Corresponding Thruster Temperature (°C)': self.ui.subLnEdtSuctnCorrThrstrTemp.value(),
                'Corresponding Tank Pressure (Bar)': self.ui.subLnEdtSuctnCorrTkPressure.value(),
                'Corresponding Thruster Pressure (Bar)': self.ui.subLnEdtSuctnCorrThrstrPressure.value()
            }

            # Vacuum Creation in Tank
            vacuum_creation_in_tank_valve_on = {
                'Vacuum_valve_open_time': self.ui.subLnEdtVacValveSwtchOnTime.text(),
                'Corresponding_tank_bottom_temperature (°C)': self.ui.subLnEdtVacValveOnCorrTkBtmTemp.value(),
                'Corresponding Thruster Temperature (°C)': self.ui.subLnEdtVacValveOnCorrThrstrTemp.value(),
                'Corresponding Tank Pressure (Bar)': self.ui.subLnEdtVacValveOnCorrTkPressure.value(),
                'Vacuum_valve_open_pressure_drop_in_tank (mbar)': self.ui.subLnEdtVacValveOnPressureDropInTank.value(),
            }

            vacuum_creation_in_tank_valve_off = {
                'Vacuum_valve_close_time': self.ui.subLnEdtVacValveSwtchOffTime.text(),
                'Corresponding_tank_bottom_temperature (°C)': self.ui.subLnEdtVacValveOffCorrTkBtmTemp.value(),
                'Corresponding Thruster Temperature (°C)': self.ui.subLnEdtVacValveOffCorrThrstrTemp.value(),
                'Corresponding Tank Pressure (Bar)': self.ui.subLnEdtVacValveOffCorrTkPressure.value()
            }

            # Heater Info
            heater_info = {
                'Heater_type': self.ui.subLnEdtHtrType_2.text(),
                'Heater_input_Voltage': self.ui.subLnEdtHtrInpVoltage.value(),
                'Heater_input_Current': self.ui.subLnEdtHtrInpCurrent.value(),
                'Heater_input_Wattage': self.ui.subLnEdtHtrInpWattage.value(),
                'Heater_cut_off_temp (°C)': self.ui.subLnEdtHtrCtOfTemp.text(),
                'Heater_reset_temp (°C)' : self.ui.subLnEdtHtrRstTemp.text()
            }

            # Heater Cycles
            heater_cycles = []
            for cycle in range(1, 5):  # 4 cycles
                cycle_data = {
                    'switch_on': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOnTime').text(),
                    'switch_on_corresponding_tank_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtONCorspgTankPressure').text(),
                    'switch_on_corresponding_thruster_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtONCorspgThrusterPressure').text(),
                    'switch_off': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOffTime').text(),
                    'switch_off_corresponding_tank_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOFFCorspgTankPressure').text(),
                    'switch_off_corresponding_thruster_pressure': getattr(self.ui, f'cyc{cycle}SubLnEdtHtrSwtOFFCorspgThrusterPressure').text(),
                    'max_temp': getattr(self.ui, f'cyc{cycle}SubLnEdtMaxTemp').text(),
                    'max_temp_location': getattr(self.ui, f'cyc{cycle}SubLnEdtLoc').text(),
                    'tank_bottom_temp': getattr(self.ui, f'cyc{cycle}SubLnEdtCorrespgTankBottomTemp').text(),
                    'valve_temp': getattr(self.ui, f'cyc{cycle}SubLnEdtCorrespgValveTemp').text()
                }
                heater_cycles.append(cycle_data)

            # Firing Valve Operation
            firing_valve_operation = {
                'Firing_valve_open_time': self.ui.subLnEdtValveSwtchOnTime.text(),
                'Valve_On_Corresponding_tank_bottom_temperature (°C)': self.ui.subLnEdtValveOnCorrTkBtmTemp.value(),
                'Valve_On_Corresponding_tank_pressure (mbar)': self.ui.subLnEdtValveOnCorrPressureTank.value(),
                'Valve_On_Pressure_drop_in_tank (mbar)': self.ui.subLnEdtValveOnPressureDrop.value(),
                'Firing_valve_close_time': self.ui.subLnEdtValveSwtchOffTime.text(),
                'Valve_Off_Corresponding_tank_bottom_temperature (°C)': self.ui.subLnEdtValveOffCorrTankBtmTemp.value(),
            }

            # Note
            note = self.ui.subLnEdtNote.toPlainText()

            # Post Test Observations
            post_test_obs = {
                'Chamber_number': self.ui.subLnEdtChmbrNoPostTestObs.text(),
                'Chamber_length (mm)': self.ui.subLnEdtChmbrLen_2.text(),
                'Chamber_internal_diameter (mm)': self.ui.subLnEdtChmbrIntDia_2.text(),
                'Chamber_external_diameter (mm)': self.ui.subLnEdtChmbrExtDia_2.text(),
                'Retainer_plate_condition': self.ui.subLnEdtRetainerPltCond_2.text(),
            }

            catalyst_post_analysis = {
                'catalyst_details/specification': self.ui.subLnEdtCatDet.text(),
                'catalyst_color_before': self.ui.subLnEdtCatColBfr.text(),
                'catalyst_color_after': self.ui.subLnEdtCatColAft.text(),
                'catalyst_weight_filled': self.ui.subLnEdtCatWghtFild.value(),
                'catalyst_weight_recovered': self.ui.subLnEdtCatWghtRecvrd.value(),
                'catalyst_change_percentage': self.ui.subLnEdtCatLosPerc.value()
            }

            propellant_post_analysis = {
                'Propellant_details/specification': self.ui.subLnEdtPropDet_2.text(),
                'Propellant_color_before': self.ui.subLnEdtPropColBef_2.text(),
                'Propellant_color_after': self.ui.subLnEdtPropColAft_2.text(),
                'Propellant_weight_filled (g)': self.ui.subLnEdtPropWghtFild_2.text(),
                'Propellant_weight_recovered (g)': self.ui.subLnEdtPropWghtRecvrd_2.text(),
                'Propellant_used_percentage (%)': self.ui.subLnEdtPropUsedPerc_2.text(),
                'Propellant_RI_(before_firing)': self.ui.subLnEdtPropRIBefFirg_2.text(),
                'Propellant_RI_(after_firing)': self.ui.subLnEdtPropRIAftFirg_2.text(),
                'Valve_Operation_Time': self.ui.subLnEdtValveOprtnTime.text(),
                'Firing_duration (s)': self.ui.subLnEdtFirgDur_2.text(),
                'Approximate_mass_flow_rate (mg/s)': self.ui.subLnEdtApproxMassFlowRate_2.text(),
                "prop_conc_bef_table": self.ui.subLblPropBefConcTable.text(),
                "prop_conc_aft_table": self.ui.subLblPropAftConcTable.text(),
                "prop_ri_bef_table": self.ui.subLblPropBefRITable.text(),
                "prop_ri_aft_table": self.ui.subLblPropAftRITable.text()
            }

            system_performance = {
                'Tank_pressure (mbar)': self.ui.subLnEdtChambPressure.text(),
                'Vacuum_chamber_pressure (mbar)': self.ui.subLnEdtVacPressure.text(),
                'Maximum_temperature (K)': self.ui.subLnEdtChambTemp.text(),
                'Characteristic_velocity (m/s)': self.ui.subLnEdtCharVelo.text(),
                'Coefficient_of_thrust': self.ui.subLnEdtCoefOfThrust.text(),
                'Burn_time (s)': self.ui.subLnEdtBurnTime.text(),
                'Mass_flow_rate (mg/s)': self.ui.subLnEdtMassFlowRate.text(),
                'Thrust (mN)': self.ui.subLnEdtThrust.text(),
                'Specific_impulse (s)': self.ui.subLnEdtSpcImpulse.text(),
                'Total_impulse (Ns)': self.ui.subLnEdtTotImpulse.text(),
                'Chamber_pressure_lower_limit (s)': self.ui.lnEdtChambPressRangeMin.value(),
                'Chamber_pressure_upper_limit (s)': self.ui.lnEdtChambPressRangeMax.value(),
                'Vacuum_pressure_lower_limit (s)': self.ui.lnEdtVacPressRangeMin.value(),
                'Vacuum_pressure_upper_limit (s)': self.ui.lnEdtVacPressRangeMax.value(),
                'filtered_data': filtered_temp_dict,
            }

            RI_Table = [
                ['Refractive Index', safe_float(self.ui.subLblPropBefRITable.text()), safe_float(self.ui.subLblPropAftRITable.text())],
                ['Concentration', safe_float(self.ui.subLblPropBefConcTable.text()), safe_float(self.ui.subLblPropAftConcTable.text())]
            ]

            test_authorization = {
                'Test Conducted by': self.ui.subLnEdtTestConductedBy.text(),
                'Report Generated by': self.ui.subLnEdtReportGeneratedBy.text(),
                'Report Authorized by': self.ui.subLnEdtReportAuthorizedBy.text()
            }

            pressure_relations = {
                'Vacuum_chamber_pressure_relation': self.ui.lnEdtY0PressureRelation.text(),
                'Propellant_tank_pressure_relation': self.ui.lnEdtY1PressureRelation.text(),
                'Thruster_chamber_pressure_relation': self.ui.lnEdtY2PressureRelation.text()
            }

            # Update test data dictionary
            self.test_data.update({
                'test_no': test_no,
                'test_date': test_date,
                'basic_info': basic_info,
                'system_specs': system_specs,
                'propellant_specs': propellant_specs,
                'catalyst_specs': catalyst_specs,
                'component_details': component_details,
                'test_details': test_details,
                'pump_operation': pump_operation,
                'suction_valve_operation': suction_valve_operation,
                'vacuum_creation_in_tank_valve_on': vacuum_creation_in_tank_valve_on,
                'vacuum_creation_in_tank_valve_off': vacuum_creation_in_tank_valve_off,
                'heater_info': heater_info,
                'heater_cycles': heater_cycles,
                'firing_valve_operation': firing_valve_operation,
                'note': note,
                'post_test_observations': post_test_obs,
                'catalyst_post_analysis': catalyst_post_analysis,
                'propellant_post_analysis': propellant_post_analysis,
                'RI_table': RI_Table,
                'system_performance': system_performance,
                'test_authorization': test_authorization,
                'pressure_relations': pressure_relations
            })

            # Log success
            self.ui.lblLogInfo.setText("Form data collected successfully")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error collecting form data: {str(e)}")
            self.ui.lblLogInfo.setText("Error collecting form data")
            raise

    def update_y_axis_options(self, plot_type: str):
        """Update Y-axis options based on selected X-axis"""
        try:
            if plot_type == 'temperature':
                selected_x = self.ui.comboBoxXAxisTemp.currentText()
                self.ui.comboBoxYAxisTemp.model.clear()
                columns = [col for col in self.temperature_data.columns if col != selected_x]
                self.ui.comboBoxYAxisTemp.addItems(columns)

            elif plot_type == 'pressure':
                selected_x = self.ui.comboBoxXAxisPressure.currentText()
                self.ui.comboBoxYAxisPressure.clear()
                columns = [col for col in self.pressure_data.columns if col != selected_x]
                self.ui.comboBoxYAxisPressure.addItems(columns)

            elif plot_type == 'both':
                selected_x = self.ui.comboBoxXAxisBoth.currentText()
                self.ui.comboBoxY1AxisTemp.clear()
                self.ui.comboBoxY2AxisPressure.clear()
                temp_columns = [col for col in self.temperature_data.columns if col != selected_x]
                pressure_columns = [col for col in self.pressure_data.columns if col != selected_x]
                self.ui.comboBoxY1AxisTemp.addItems(temp_columns)
                self.ui.comboBoxY2AxisPressure.addItems(pressure_columns)


        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error updating Y-axis options: {str(e)}")

    def select_photo(self, photo_id: str):
        """Handle photo selection and preview for the specified photo widget"""
        try:
            widgets = self.photo_widgets.get(photo_id)
            if not widgets:
                raise ValueError(f"Invalid photo ID: {photo_id}")

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                f"Select {widgets['label']}",
                "",
                "Images (*.png *.jpg *.jpeg)"
            )

            if file_path:
                # Validate image using image handler
                is_valid, error = self.image_handler.validate_image(file_path)
                if is_valid:
                    # Update line edit with file path
                    widgets['line_edit'].setText(file_path)

                    # Create preview pixmap
                    preview_pixmap = QPixmap(file_path)
                    scaled_pixmap = preview_pixmap.scaled(
                        40, 40,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )

                    # Create a clickable label for the preview
                    preview_label = widgets['preview']
                    preview_label.setPixmap(scaled_pixmap)
                    preview_label.setCursor(Qt.PointingHandCursor)

                    # Store the file path for later use
                    preview_label.setProperty("image_path", file_path)

                    # Connect click handler if not already connected
                    try:
                        preview_label.disconnect()
                    except:
                        pass

                    preview_label.mousePressEvent = lambda e, pid=photo_id: self.show_full_photo(file_path, pid)

                    self.ui.lblLogInfo.setText(f"Selected photo for {widgets['label']}")
                else:
                    QMessageBox.warning(
                        self,
                        "Invalid Image",
                        f"Please select a valid image file. Error: {error}"
                    )
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error selecting photo: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error selecting photo: {str(e)}")

    def show_full_photo(self, image_path, photo_id):
        try:
            if image_path and os.path.exists(image_path):
                dialog = PhotoPreviewDialog(image_path, photo_id, self)
                dialog.image_deleted.connect(self.handle_image_deleted)
                dialog.exec()
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error showing full photo: {str(e)}")

    def handle_image_deleted(self, photo_id):
        widgets = self.photo_widgets.get(photo_id)
        if widgets:
            widgets['line_edit'].setText('No Photo Selected')
            widgets['preview'].clear()
            widgets['preview'].setStyleSheet(u'background-color:#333333')
            # Reset the internal data if needed
            self.test_data.get('photo_paths', {}).pop(photo_id, None)

    def get_photo_paths(self) -> Dict[str, str]:
        """Get dictionary of photo paths for report generation"""
        return {
            photo_id: widgets['line_edit'].text().strip()
            for photo_id, widgets in self.photo_widgets.items()
            if widgets['line_edit'].text().strip()
        }

    def generate_report(self):
        """Generate and handle the report"""
        temp_dir = None
        try:
            # Collect form data
            self.collect_form_data()

            # Get photo paths
            photo_paths = self.get_photo_paths()

            # Font verification is now handled centrally by setup_fonts()

            # Create temporary directory
            temp_dir = tempfile.mkdtemp(prefix='vapr_idex_')
            plots_dir = os.path.join(temp_dir, 'plots')
            temp_plots_dir = os.path.join(plots_dir, 'temperature')
            pressure_plots_dir = os.path.join(plots_dir, 'pressure')

            # Create directories with proper permissions
            for directory in [temp_dir, plots_dir, temp_plots_dir, pressure_plots_dir]:
                os.makedirs(directory, exist_ok=True)
                os.chmod(directory, 0o777)

            # Prepare plot paths
            plot_paths = {}

            for category in ['default', 'custom']:
                for plot_info in self.report_plots[category]:
                    if os.path.exists(plot_info['path']):
                        dest_dir = temp_plots_dir if plot_info['type'] == 'temperature' else pressure_plots_dir
                        os.makedirs(dest_dir, exist_ok=True)
                        dest_path = os.path.join(dest_dir, os.path.basename(plot_info['path']))
                        shutil.copy2(plot_info['path'], dest_path)

            # Update plot_paths if directories contain files
            if os.path.exists(temp_plots_dir) and os.listdir(temp_plots_dir):
                plot_paths['temperature'] = temp_plots_dir
            if os.path.exists(pressure_plots_dir) and os.listdir(pressure_plots_dir):
                plot_paths['pressure'] = pressure_plots_dir

            # Generate PDF
            self.report_generator = TestReportGenerator(temp_dir)
            self.report_generator.test_data = self.test_data
            pdf_path = self.report_generator.generate_pdf(plot_paths, photo_paths)

            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"Generated PDF not found at {pdf_path}")

            self._current_plot_paths = plot_paths

            print(f"The plot path contains: {plot_paths}")

            # Show preview dialog
            preview_dialog = ReportPreviewDialog(pdf_path, self)
            if preview_dialog.exec() == QDialog.DialogCode.Accepted:
                # Get save location for PDF
                save_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save Report",
                    f"Test_Report_{self.test_data['test_no']}.pdf",
                    "PDF files (*.pdf)"
                )
                if save_path:
                    try:
                        if not save_path.lower().endswith('.pdf'):
                            save_path += '.pdf'
                        chosen_dir = os.path.dirname(save_path)
                        base_name = os.path.splitext(os.path.basename(save_path))[0]
                        final_pdf_path = os.path.join(chosen_dir, f"{base_name}.pdf")
                        shutil.copy2(pdf_path, final_pdf_path)
                        final_plots_dir = os.path.join(chosen_dir, 'plots')
                        if os.path.exists(final_plots_dir):
                            shutil.rmtree(final_plots_dir)
                        shutil.copytree(plots_dir, final_plots_dir)
                        json_path = self.save_json_file(chosen_dir, base_name, plots_dir=final_plots_dir)
                        QMessageBox.information(
                            self,
                            "Success",
                            f"Report and test data saved successfully:\nPDF: {final_pdf_path}\nJSON: {json_path}"
                        )
                        self._current_report_path = final_pdf_path
                        self._current_plots_dir = final_plots_dir
                        # Update plot paths to final locations
                        self._current_plot_paths = {
                            'temperature': os.path.join(final_plots_dir, 'temperature'),
                            'pressure': os.path.join(final_plots_dir, 'pressure')
                        }
                    except Exception as e:
                        QMessageBox.critical(self, "Error", f"Error saving report: {str(e)}")

        except Exception as e:
            error_msg = f"Error generating report: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            QMessageBox.critical(self, "Error", error_msg)

        finally:
            # Cleanup temporary directory
            if temp_dir and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    def save_to_database(self):
        """Save test data and report to database"""
        try:
            # Collect form data
            self.collect_form_data()

            # Convert DataFrame to a serializable format
            if hasattr(self, 'filtered_temp_data') and self.filtered_temp_data is not None:
                try:
                    # Save the selection criteria using the actual columns from filtered_temp_data
                    filtered_temp_dict = {
                        'selected_columns': [col for col in self.filtered_temp_data.columns if col != 'time'],
                        'selected_ranges': self.selected_ranges if hasattr(self, 'selected_ranges') else []
                    }

                    # Also save the actual data in a format that can be directly loaded
                    time_data = self.filtered_temp_data['time'].tolist()
                    temp_cols = [col for col in self.filtered_temp_data.columns if col != 'time']
                    temperature_data = {col: self.filtered_temp_data[col].tolist() for col in temp_cols}

                    # Combine both formats for maximum compatibility
                    filtered_temp_dict.update({
                        'time_data': time_data,
                        'temperature_data': temperature_data
                    })

                    # Add to performance_data
                    if 'performance_data' not in self.test_data:
                        self.test_data['performance_data'] = {}
                    self.test_data['performance_data']['filtered_temp_data'] = filtered_temp_dict

                    # Make sure we're not using the old key name
                    if 'filtered_temperature_data' in self.test_data['performance_data']:
                        del self.test_data['performance_data']['filtered_temperature_data']

                    print("Successfully prepared filtered temperature data for database storage")
                except Exception as e:
                    print(f"Error preparing filtered temperature data: {str(e)}")
                    import traceback
                    traceback.print_exc()

            # Check if test number is valid
            test_no = self.test_data.get('test_no') if hasattr(self, 'test_data') else None
            if not test_no or str(test_no).strip() == '':
                QMessageBox.warning(
                    self,
                    "Invalid Test Number",
                    "Please enter a valid test number before saving to database."
                )
                return

            # Check if a report has been generated
            if not hasattr(self, '_current_report_path') or not os.path.exists(self._current_report_path):
                QMessageBox.warning(self, "Warning", "Please generate and save a report first.")
                return

            # Check if plot paths are available
            if not hasattr(self, '_current_plot_paths'):
                QMessageBox.warning(self, "Warning", "Plot paths are not available. Please generate the report first.")
                return

            if not all(os.path.exists(path) for path in self._current_plot_paths.values()):
                QMessageBox.warning(self, "Warning",
                                    "One or more plot directories are missing. Cannot save to database.")
                return

            # Check if database handler is initialized
            if not hasattr(self, 'db_handler') or self.db_handler is None:
                QMessageBox.warning(
                    self,
                    "Database Not Connected",
                    "Please connect to the database first by entering the database password."
                )
                return

            # Verify database connection
            test_params = DatabaseConfig.get_connection_params(is_server=True)
            success, message = DatabaseConfig.test_connection(test_params)
            if not success:
                QMessageBox.critical(
                    self,
                    "Database Error",
                    f"Not connected to database: {message}\nPlease check your connection and try again."
                )
                return

            # Ensure test_no is an integer
            try:
                self.test_data['test_no'] = int(test_no)
            except (ValueError, TypeError):
                QMessageBox.warning(
                    self,
                    "Invalid Test Number",
                    "Test number must be a valid integer."
                )
                return

            test_auth_data = self._prepare_test_authorization_data()
            self.test_data.update({'test_authorization': test_auth_data})

            # Include temperature analysis if available
            if hasattr(self, 'temp_analyzer') and 'matrix' in self.temp_analyzer.analysis_results:
                self.test_data['temperature_analysis'] = self.temp_analyzer.analysis_results['matrix']

            current_photo_paths = {
                'prop_before': self.ui.subLnEdtPropPhtoBfr_2.text(),
                'prop_after': self.ui.subLnEdtPropPhtoAft_2.text(),
                'cat_before': self.ui.subLnEdtCatPhtoBfr_2.text(),
                'cat_after': self.ui.subLnEdtCatPhtoAft_2.text()
            }

            # Save data to database, including plot paths
            test_id = self.db_handler.save_test_data(self.test_data, self._current_plot_paths, current_photo_paths)
            if not test_id:
                raise Exception("Failed to save test data to database")

            # Save additional data if available
            if hasattr(self, 'temperature_data') and self.temperature_data is not None:
                self.db_handler.save_temperature_data(test_id, self.temperature_data)
            if hasattr(self, 'pressure_data') and self.pressure_data is not None:
                self.db_handler.save_pressure_data(test_id, self.pressure_data)

            # Save the report to the database
            self.db_handler.save_report(test_id, self._current_report_path)

            QMessageBox.information(
                self,
                "Success",
                "Data successfully saved to database!"
            )

            # Clean up temporary files
            self._cleanup_temp_directory()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Database Error",
                f"Error saving to database: {str(e)}"
            )

    def _prepare_test_authorization_data(self):
        """Prepare test authorization data for database storage"""
        try:
            return {
                'conducted_by': self.ui.subLnEdtTestConductedBy.text(),
                'generated_by': self.ui.subLnEdtReportGeneratedBy.text(),
                'authorized_by': self.ui.subLnEdtReportAuthorizedBy.text()
            }
        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error preparing test authorization data: {str(e)}")
            return {}

    def _cleanup_temp_directory(self):
        """Clean up temporary directory after successful save"""
        try:
            if hasattr(self, '_temp_dir_to_cleanup') and self._temp_dir_to_cleanup:
                if os.path.exists(self._temp_dir_to_cleanup):
                    shutil.rmtree(self._temp_dir_to_cleanup)
                    self._temp_dir_to_cleanup = None
        except Exception as e:
            print(f"Error cleaning up temporary directory: {str(e)}")

    def save_json_file(self, base_dir, base_name, plots_dir=None):
        """Saves all test data fields entered by the user and associated plots in a JSON file."""
        json_file_path = os.path.join(base_dir, f"{base_name}.json")
        try:
            # Convert DataFrame to a serializable format
            if hasattr(self, 'filtered_temp_data') and self.filtered_temp_data is not None:
                # Save the selection criteria using the actual columns from filtered_temp_data
                self.filtered_temp_dict = {
                    'selected_columns': [col for col in self.filtered_temp_data.columns if col != 'time'],
                    'selected_ranges': self.selected_ranges if hasattr(self, 'selected_ranges') else []
                }

                # Also save the actual data in a format that can be directly loaded
                time_data = self.filtered_temp_data['time'].tolist()
                temp_cols = [col for col in self.filtered_temp_data.columns if col != 'time']
                temperature_data = {col: self.filtered_temp_data[col].tolist() for col in temp_cols}

                # Combine both formats for maximum compatibility
                self.filtered_temp_dict.update({
                    'time_data': time_data,
                    'temperature_data': temperature_data
                })

            # Collect test data
            test_data = self.auto_saver.get_all_test_data()

            # Add filtered temperature selection data if available
            if hasattr(self, 'filtered_temp_dict') and self.filtered_temp_dict is not None:
                test_data['filtered_temperature'] = self.filtered_temp_dict

            # Add firing duration if available
            on_time = self.ui.subLnEdtValveSwtchOnTime.time()
            off_time = self.ui.subLnEdtValveSwtchOffTime.time()
            firing_duration = on_time.secsTo(off_time)

            test_data['firing_duration'] = firing_duration

            # Add temperature analysis if available
            if hasattr(self, 'temp_analyzer') and hasattr(self.temp_analyzer, 'analysis_results'):
                if 'matrix' in self.temp_analyzer.analysis_results:
                    analysis_df = self.temp_analyzer.analysis_results['matrix']
                    test_data['temperature_analysis'] = analysis_df.to_dict(orient='index')

            # Save temperature data if available
            if hasattr(self, 'temperature_data') and self.temperature_data is not None:
                test_data['temperature_data'] = {
                    'time': self.temperature_data['time'].tolist(),
                    'temperatures': {
                        col: self.temperature_data[col].tolist()
                        for col in self.temperature_data.columns if col != 'time'
                    }
                }

            # Save pressure data if available
            if hasattr(self, 'pressure_data') and self.pressure_data is not None:
                time_col = next((col for col in self.pressure_data.columns if 'time' in col.lower()), None)
                if time_col:
                    test_data['pressure_data'] = {
                        'time': self.pressure_data[time_col].tolist(),
                        'pressures': {
                            col: self.pressure_data[col].tolist()
                            for col in self.pressure_data.columns if col != time_col
                        }
                    }

            # Handle photos
            photos_metadata = {}
            photos_dir = os.path.join(base_dir, 'photos')
            os.makedirs(photos_dir, exist_ok=True)

            # Process each photo type
            for photo_type, widget_info in self.photo_widgets.items():
                photo_path = widget_info['line_edit'].text()
                if photo_path and os.path.exists(photo_path):
                    # Create a standardized filename
                    new_filename = f"{photo_type}_{base_name}.png"
                    new_photo_path = os.path.join(photos_dir, new_filename)

                    # Copy the photo file
                    shutil.copy2(photo_path, new_photo_path)

                    # Store relative path in metadata
                    rel_path = os.path.join('photos', new_filename)
                    photos_metadata[photo_type] = {
                        'relative_path': rel_path,
                        'label': widget_info['label']
                    }

            # Add photos metadata to test data
            test_data['photos'] = photos_metadata

            # Handle plots
            plot_metadata = {'default': [], 'custom': []}
            if plots_dir:
                # Use existing plots_dir with temperature and pressure subfolders
                temp_dir = os.path.join(plots_dir, 'temperature')
                press_dir = os.path.join(plots_dir, 'pressure')
                for category in ['default', 'custom']:
                    for plot_info in self.report_plots[category]:
                        sub_dir = 'temperature' if plot_info['type'] == 'temperature' else 'pressure'
                        plot_filename = os.path.basename(plot_info['path'])
                        rel_path = os.path.join('plots', sub_dir, plot_filename)
                        full_path = os.path.join(plots_dir, sub_dir, plot_filename)
                        if os.path.exists(full_path):
                            plot_metadata[category].append({
                                'relative_path': rel_path,
                                'title': plot_info['title'],
                                'type': plot_info['type']
                            })
                        else:
                            print(f"Warning: Plot file not found at {full_path}")
            else:
                # Original behavior: create a new plots folder
                new_plots_dir = os.path.join(base_dir, f"{base_name}_plots")
                os.makedirs(new_plots_dir, exist_ok=True)
                for category in ['default', 'custom']:
                    for plot_info in self.report_plots[category]:
                        if 'path' in plot_info and os.path.exists(plot_info['path']):
                            plot_filename = f"{plot_info['title'].replace(' ', '_')}_{category}.png"
                            new_plot_path = os.path.join(new_plots_dir, plot_filename)
                            shutil.copy2(plot_info['path'], new_plot_path)
                            rel_path = os.path.join(f"{base_name}_plots", plot_filename)
                            plot_metadata[category].append({
                                'relative_path': rel_path,
                                'title': plot_info['title'],
                                'type': plot_info['type']
                            })

            # Add plot metadata to test data
            test_data['plots'] = plot_metadata

            # Save to JSON
            with open(json_file_path, 'w') as f:
                json.dump(test_data, f, indent=4)

            return json_file_path

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving test data: {str(e)}")
            print(f"Detailed error: {traceback.format_exc()}")
            return None

    def load_data_from_json_file(self):
        """Loads all the saved data field and plots in the GUI from a json file"""
        try:
            # Helper function to safely convert to float
            def safe_float(value, default=0.0):
                try:
                    if value and str(value).strip():
                        return float(value)
                    return default
                except (ValueError, TypeError):
                    return default

            json_file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Load Test Data JSON File",
                "",
                "JSON Files (*.json)"
            )

            if not json_file_path:
                return

            # Close the existing data dialog if it's open
            if hasattr(self, 'existing_data_dialog'):
                self.existing_data_dialog.accept()

            # Clear existing plot previews
            self.clear_plot_previews()

            # Initialize report_plots
            if not hasattr(self, 'report_plots'):
                self.report_plots = {'default': [], 'custom': []}

            # Load and parse JSON data
            with open(json_file_path, 'r') as f:
                data = json.load(f)

            base_dir = os.path.dirname(json_file_path)

            # Load firing duration if available
            if 'performance_data' in data:
                firing_duration = data['performance_data']["Burn_time (s)"]
                self.ui.lblFiringDuration.setText(f"{firing_duration}s")
                self.ui.subLnEdtFirgDur_2.setValue(safe_float(firing_duration.split(' ')[0]))

            # Load photos if available
            if 'photos' in data:
                for photo_type, photo_info in data['photos'].items():
                    if photo_type in self.photo_widgets:
                        rel_path = photo_info.get('relative_path')
                        if rel_path:
                            full_path = os.path.join(base_dir, rel_path)
                            if os.path.exists(full_path):
                                # Update the line edit with the path
                                self.photo_widgets[photo_type]['line_edit'].setText(full_path)
                                # Update the preview
                                self.update_photo_preview(
                                    self.photo_widgets[photo_type]['preview'],
                                    full_path
                                )

            # Load form data
            self.auto_saver.load_data_from_json(json_file_path)

            # Display temperature analysis if loaded
            if 'temperature_analysis' in data:
                df = pd.DataFrame.from_dict(data['temperature_analysis'], orient='index')
                self.temp_analyzer.analysis_results = {'matrix': df}
                self.display_temperature_analysis(df)

            # Load temperature data if available
            if 'temperature_data' in data:
                temp_data = data['temperature_data']
                df_data = {'time': temp_data['time']}
                df_data.update(temp_data['temperatures'])
                self.temperature_data = pd.DataFrame(df_data)

                # Update UI indicators
                self.ui.btnTempDataInd.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                self.ui.btnTempDataLoad.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                self.ui.btnTempMatrix.setEnabled(True)
                self.ui.btnPlots.setEnabled(True)

            # Load pressure data if available
            if 'pressure_data' in data:
                pressure_data = data['pressure_data']
                df_data = {'time': pressure_data['time']}
                df_data.update(pressure_data['pressures'])
                self.pressure_data = pd.DataFrame(df_data)

                # Update UI indicators
                self.ui.btnPressureDataInd.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                self.ui.btnPressureDataLoad.setStyleSheet(u'background-color: rgb(29, 78, 216);')

                # Enable the plots button
                self.ui.btnPlots.setEnabled(True)

            # Load filter data
            filtered_data_dict = data.get('filtered_temperature', {})
            if filtered_data_dict:
                # Check if we have the direct data format
                if 'time_data' in filtered_data_dict and 'temperature_data' in filtered_data_dict:
                    # Create DataFrame directly from the saved data
                    time_data = filtered_data_dict['time_data']
                    temperature_data = filtered_data_dict['temperature_data']

                    df_data = {'time': time_data}
                    for col, values in temperature_data.items():
                        df_data[col] = values

                    self.filtered_temp_data = pd.DataFrame(df_data)

                    # Also set selected columns and ranges for future use
                    self.selected_cols = filtered_data_dict.get('selected_columns', list(temperature_data.keys()))
                    self.selected_ranges = filtered_data_dict.get('selected_ranges', [])

                    # Setting indicator color green
                    self.ui.tempMatrixIndicator.setStyleSheet("""
                        background-color: rgb(6, 196, 142);
                        border-radius: 7px;
                    """)
                else:
                    # Use the old method of reconstructing from selection criteria
                    self.selected_cols = filtered_data_dict.get('selected_columns', [])
                    self.selected_ranges = filtered_data_dict.get('selected_ranges', [])

                    if self.selected_cols and self.selected_ranges is not None and hasattr(self, 'temperature_data') and self.temperature_data is not None:
                        # Create mask for selected ranges
                        mask = pd.Series(False, index=self.temperature_data.index)

                        for start, end in self.selected_ranges:
                            mask |= ((self.temperature_data['time'] >= start) &
                                     (self.temperature_data['time'] <= end))

                        # Filter data
                        filtered_data = self.temperature_data[mask]

                        # Select only chosen columns plus time
                        columns_to_use = ['time'] + self.selected_cols
                        filtered_data = filtered_data[columns_to_use]

                        self.filtered_temp_data = filtered_data

                        # Setting indicator color green
                        self.ui.tempMatrixIndicator.setStyleSheet("""
                            background-color: rgb(6, 196, 142);
                            border-radius: 7px;
                        """)

            # Enable plots button if either data is loaded
            if hasattr(self, 'temperature_data') or hasattr(self, 'pressure_data'):
                self.ui.btnPlots.setEnabled(True)

            # Show top bar labels
            self.ui.lblAim.setVisible(True)
            self.ui.lblPropellant.setVisible(True)
            self.ui.lblCatalyst.setVisible(True)
            self.ui.testNoFrame.setVisible(True)

            self.ui.lblAim.setText(self.ui.subLnEdtAim.text())
            self.ui.lblPropellant.setText(self.ui.subLnEdtProp.text())
            self.ui.lblCatalyst.setText(self.ui.subLnEdtCat.text())
            self.ui.lblTestNumber.setText(str(self.ui.subLnEdtTestNo.value()))

            # Reset report plots dictionary
            self.report_plots = {'default': [], 'custom': []}

            # Load plots if they exist in the data
            if 'plots' in data:
                base_dir = os.path.dirname(json_file_path)

                for category in ['default', 'custom']:
                    if category in data['plots']:
                        for plot_info in data['plots'][category]:
                            try:
                                # Try absolute path first
                                plot_path = plot_info.get('absolute_path')

                                # If absolute path doesn't exist, try relative path
                                if not plot_path or not os.path.exists(plot_path):
                                    rel_path = plot_info.get('relative_path')
                                    if rel_path:
                                        plot_path = os.path.join(base_dir, rel_path)

                                if plot_path and os.path.exists(plot_path):
                                    plot_data = {
                                        'path': plot_path,
                                        'title': plot_info.get('title', 'Unnamed Plot'),
                                        'type': plot_info.get('type', 'temperature')
                                    }

                                    self.report_plots[category].append(plot_data)
                                    self.add_plot_to_preview(plot_data)
                                    print(f"Successfully loaded plot: {plot_data['title']}")
                                else:
                                    print(f"Plot file not found: {plot_path}")

                            except Exception as e:
                                print(f"Error loading plot: {str(e)}")
                                import traceback
                                traceback.print_exc()

            # Force enable the plots button
            self.ui.btnPlots.setEnabled(True)
            self.setup_plot_controls()

            print(f"Plots button directly enabled in load_data_from_json_file: {self.ui.btnPlots.isEnabled()}")

            # Call check_and_enable_plots_button to ensure the plots button is enabled
            if hasattr(self, 'auto_saver'):
                self.auto_saver.check_and_enable_plots_button()

            # Force enable again after a short delay using a timer
            QTimer.singleShot(500, lambda: self.force_enable_plots_button())

            # Set up multiple timers to keep trying to enable the button
            for delay in [1000, 2000, 3000, 4000, 5000]:
                QTimer.singleShot(delay, lambda: self.force_enable_plots_button())

            QMessageBox.information(
                self,
                "Success",
                "Test data and plots loaded successfully!"
            )

        except FileNotFoundError:
            QMessageBox.warning(
                self,
                "Error",
                "The specified JSON file or associated plot files could not be found."
            )
        except json.JSONDecodeError:
            QMessageBox.warning(
                self,
                "Error",
                "The JSON file is invalid or corrupted."
            )
        except Exception as e:
            import traceback
            QMessageBox.critical(
                self,
                "Error",
                f"Error loading test data: {str(e)}"
            )
            print(f"Detailed error: {traceback.format_exc()}")  # For debugging

    def update_photo_preview(self, preview_label, photo_path):
        """Update the photo preview in the UI"""
        try:
            pixmap = QPixmap(photo_path)
            if not pixmap.isNull():
                # Scale the pixmap to fit the label while maintaining aspect ratio
                scaled_pixmap = pixmap.scaled(
                    preview_label.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                preview_label.setPixmap(scaled_pixmap)
        except Exception as e:
            print(f"Error updating photo preview: {str(e)}")

    def load_test_data(self):
        """Load test data from database."""
        try:
            # Close the initial dialog box
            self.existing_data_dialog.accept()

            # Clear existing plot previews
            self.clear_plot_previews()

            # Get all test numbers
            test_numbers = self.db_handler.get_all_test_numbers()
            if not test_numbers:
                QMessageBox.information(self, "Info", "No test data found in database.")
                return

            # Creating a dialog for test number selection
            test_no, ok = QInputDialog.getItem(
                self,
                "Load Test Data",
                "Select Test Number:",
                [str(num) for num in test_numbers],  # Convert to strings for display
                0,
                False
            )

            if ok and test_no:
                # Load test data
                test_data = self.db_handler.get_test_data(str(test_no))  # Convert back to string for DB query
                if test_data:
                    # Clear existing data
                    self.temperature_data = None
                    self.pressure_data = None
                    self.test_data = {}

                    # Update UI
                    self.update_ui_with_test_data(test_data)

                    # Show top bar labels
                    self.ui.lblAim.setVisible(True)
                    self.ui.lblPropellant.setVisible(True)
                    self.ui.lblCatalyst.setVisible(True)
                    self.ui.testNoFrame.setVisible(True)

                    self.filtered_temp_data = None

                    # Load temperature data
                    temp_data = self.db_handler.get_temperature_data(test_data['test_id'])
                    if temp_data is not None:
                        self.temperature_data = temp_data
                        self.ui.btnTempDataInd.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                        self.ui.btnTempDataLoad.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                        self.ui.btnPlots.setEnabled(True)
                        self.ui.btnTempMatrix.setEnabled(True)
                        self.setup_plot_controls()

                    # Load pressure data
                    press_data = self.db_handler.get_pressure_data(test_data['test_id'])
                    if press_data is not None:
                        self.pressure_data = press_data
                        self.ui.btnPressureDataInd.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                        self.ui.btnPressureDataLoad.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                        self.ui.btnPlots.setEnabled(True)

                    # Load filter data
                    if 'performance_data' in test_data:
                        try:
                            # Try to get filtered temperature data directly from the database
                            filtered_temp_data = self.db_handler.get_filtered_temp_data_from_performance(test_data['test_id'])
                            if filtered_temp_data is not None:
                                self.filtered_temp_data = filtered_temp_data

                                # Also set selected columns and ranges for future use
                                self.selected_cols = [col for col in filtered_temp_data.columns if col != 'time']

                                # Try to get selected ranges from performance data
                                filtered_data_dict = None
                                if 'filtered_temp_data' in test_data['performance_data']:
                                    filtered_data_dict = test_data['performance_data']['filtered_temp_data']
                                elif 'filtered_temperature_data' in test_data['performance_data']:
                                    filtered_data_dict = test_data['performance_data']['filtered_temperature_data']

                                if isinstance(filtered_data_dict, dict) and 'selected_ranges' in filtered_data_dict:
                                    self.selected_ranges = filtered_data_dict['selected_ranges']
                                else:
                                    # If we don't have ranges, try to infer them from the data
                                    self.selected_ranges = [(filtered_temp_data['time'].min(), filtered_temp_data['time'].max())]

                                # Setting indicator color green
                                self.ui.tempMatrixIndicator.setStyleSheet("""
                                    background-color: rgb(6, 196, 142);
                                    border-radius: 7px;
                                """)

                                print("Successfully loaded filtered temperature data")
                            else:
                                print("Failed to load filtered temperature data from database")
                        except Exception as e:
                            print(f"Error loading filtered temperature data: {str(e)}")
                            import traceback
                            traceback.print_exc()

                    # Load and display plots
                    test_plots = self.db_handler.get_test_plots(test_data['test_id'], self.add_plot_to_preview)
                    if test_plots:
                        self.report_plots = {
                            'default': [],
                            'custom': test_plots
                        }

                    # Load and display photos
                    test_data = self.db_handler.load_test_data(test_data['test_id'])
                    if test_data and 'photos' in test_data:
                        photos = test_data['photos']
                        # Update UI with photos
                        for photo_type, photo_path in photos.items():
                            # Update the corresponding photo widget
                            self.update_photo_widget(photo_type, photo_path)

                    # Update firing duration in UI if available
                    if 'performance_data' in test_data:
                        firing_duration = test_data['performance_data']["Burn_time (s)"]
                        self.ui.lblFiringDuration.setText(f"{firing_duration}s")
                        # Safe float conversion for firing duration
                        try:
                            duration_value = float(firing_duration.split(' ')[0])
                            self.ui.subLnEdtFirgDur_2.setValue(duration_value)
                        except (ValueError, TypeError, IndexError):
                            self.ui.subLnEdtFirgDur_2.setValue(0.0)

                    # Display temperature analysis if available
                    if 'temperature_analysis' in test_data and test_data['temperature_analysis'] is not None:
                        self.temp_analyzer.analysis_results = {'matrix': test_data['temperature_analysis']}
                        self.display_temperature_analysis(test_data['temperature_analysis'])

                    # Update chamber and vacuum pressure ranges
                    if 'Vacuum_pressure_lower_limit (s)' in test_data and test_data['Vacuum_pressure_lower_limit (s)'] is not None:
                        self.ui.lnEdtVacPressRangeMin.setValue(test_data['Vacuum_pressure_lower_limit (s)'])
                        self.ui.lnEdtVacPressRangeMax.setValue(test_data['Vacuum_pressure_upper_limit (s)'])
                        self.ui.lnEdtChambPressRangeMin.setValue(test_data['Chamber_pressure_lower_limit (s)'])
                        self.ui.lnEdtChambPressRangeMax.setValue(test_data['Chamber_pressure_upper_limit (s)'])

                    QMessageBox.information(self, "Success", "Test data loaded successfully!")

                else:
                    QMessageBox.warning(self, "Warning", f"Failed to load data for test {test_no}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading test data: {str(e)}")
            traceback.print_exc()

    def update_photo_widget(self, photo_type: str, photo_path: str):
        """
        Update a photo widget with the loaded photo from database.

        Args:
            photo_type (str): Type of photo ('prop_before', 'prop_after', 'cat_before', 'cat_after')
            photo_path (str): Path to the photo file
        """
        try:
            # Get the widget info from photo_widgets dictionary
            widget_info = self.photo_widgets.get(photo_type)
            if not widget_info:
                raise ValueError(f"Invalid photo type: {photo_type}")

            if os.path.exists(photo_path):
                # Update line edit with file path
                widget_info['line_edit'].setText(photo_path)

                # Create preview pixmap
                preview_pixmap = QPixmap(photo_path)
                scaled_pixmap = preview_pixmap.scaled(
                    40, 40,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

                # Update preview label
                preview_label = widget_info['preview']
                preview_label.setPixmap(scaled_pixmap)
                preview_label.setCursor(Qt.PointingHandCursor)
                preview_label.setProperty("image_path", photo_path)

                # Disconnect any existing click handlers
                try:
                    preview_label.disconnect()
                except:
                    pass

                # Connect new click handler
                preview_label.mousePressEvent = lambda e, pid=photo_type: self.show_full_photo(photo_path, pid)

                self.ui.lblLogInfo.setText(f"Loaded photo for {widget_info['label']}")
            else:
                # Reset widget if photo doesn't exist
                widget_info['line_edit'].setText('No Photo Selected')
                widget_info['preview'].clear()
                widget_info['preview'].setStyleSheet(u'background-color:#333333')
                self.ui.lblLogInfo.setText(f"Photo file not found: {photo_path}")

        except Exception as e:
            self.ui.lblLogInfo.setText(f"Error updating photo widget: {str(e)}")
            print(f"Error updating photo widget: {str(e)}")

    def update_ui_with_test_data(self, test_data):
        """Update UI elements with loaded test data."""
        try:
            # Safe float conversion with default values if conversion fails
            def safe_float(value, default=0.0):
                try:
                    return float(value) if value != '' else default
                except (ValueError, TypeError):
                    return default

            # Basic Info
            basic_info = test_data.get('basic_info', {})


            # Handle test number
            test_no = test_data.get('test_no')
            if test_no is not None and str(test_no).strip():
                self.ui.subLnEdtTestNo.setValue(int(test_no))

            # Handle text fields with empty string fallback
            self.ui.subLnEdtAim.setText(basic_info.get('Aim', ''))
            self.ui.subLnEdtProp.setText(basic_info.get('Propellant', ''))
            self.ui.subLnEdtCat.setText(basic_info.get('Catalyst', ''))

            # Handle numeric fields with validation
            prop_ri = basic_info.get('Propellant_RI_Before_Test', '')
            if prop_ri and str(prop_ri).strip():
                try:
                    self.ui.subLnEdtPropRI.setValue(safe_float(prop_ri))
                except (ValueError, TypeError):
                    self.ui.subLnEdtPropRI.setValue(0.0)

            # Handle date
            test_date = test_data.get('test_date')
            if test_date:
                self.ui.subLnEdtTestDate.setDate(test_date)

            # Handle system performance data
            sys_perf_data = test_data.get('system_performance', {})
            self.ui.subLnEdtSpcImpulse.setText(str(sys_perf_data.get('Specific_impulse (s)', '')))
            self.ui.subLnEdtTotImpulse.setText(str(sys_perf_data.get('Total_impulse (Ns)', '')))
            # Update firing duration widget
            firing_duration = sys_perf_data.get('Burn_time (s)')
            if firing_duration is not None:
                self.ui.lblFiringDuration.setText(f"{str(firing_duration)}s")

            # Update test Authorization with empty string fallbacks
            test_authorization = test_data.get('test_authorization', {})
            self.ui.subLnEdtTestConductedBy.setText(test_authorization.get('Test Conducted by', ''))
            self.ui.subLnEdtReportGeneratedBy.setText(test_authorization.get('Report Generated by', ''))
            self.ui.subLnEdtReportAuthorizedBy.setText(test_authorization.get('Report Authorized by', ''))

            # Top bar
            self.ui.lblAim.setText(basic_info.get('Aim', ''))
            self.ui.lblPropellant.setText(basic_info.get('Propellant', ''))
            self.ui.lblCatalyst.setText(basic_info.get('Catalyst', ''))
            self.ui.lblTestNumber.setText(str(test_data.get('test_no', '')))

            # System Specs
            sys_specs = test_data.get('system_specs', {})
            self.ui.subLnEdtChmbrNo.setText(sys_specs.get('Chamber number', ''))
            self.ui.subLnEdtChmbrMat.setText(sys_specs.get('Chamber material', ''))
            self.ui.subLnEdtChmbrDept.setValue(safe_float(sys_specs.get('Chamber depth (mm)', '')))
            self.ui.subLnEdtInternalChmbrDia.setValue(safe_float(sys_specs.get('Chamber internal diameter (mm)', '')))
            self.ui.subLnEdtExternalChmbrDia.setValue(safe_float(sys_specs.get('Chamber external diameter (mm)', '')))
            self.ui.subLnEdtNozlThrtDime.setValue(safe_float(sys_specs.get('Nozzle throat dimension (mm)', '')))
            self.ui.subLnEdtRetainerPltOrfcDia.setValue(
                safe_float(sys_specs.get('Retainer plate orifice diameter (mm)', '')))
            self.ui.subLnEdtInjectorOrificeDia.setValue(
                safe_float(sys_specs.get('Injector orifice diameter (mm)', '')))

            # # Propellant Specs
            prop_specs = test_data.get('propellant_specs', {})
            self.ui.subLnEdtTypeOfProp.setText(prop_specs.get('Type of Propellant', ''))
            self.ui.subLnEdtConcBefTest.setValue(safe_float(prop_specs.get('Concentration before testing (%)', '')))
            self.ui.subLnEdtStability.setText(prop_specs.get('Stability (Old/New -MIL)', ''))
            self.ui.subLnEdtWghtOfPropBefTest.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_before the test (g)', '')))
            self.ui.subLnEdtWghtOfPropAftTest.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_after the test (g)', '')))

            # # Catalyst Specs
            cat_specs = test_data.get('catalyst_specs', {})
            self.ui.subLnEdtCatType.setText(cat_specs.get('Catalyst_type', ''))
            self.ui.subLnEdtCatGrade.setText(cat_specs.get('Catalyst_Grade/ Composition', ''))
            self.ui.subLnEdtCatSize.setText(cat_specs.get('Catalyst_size (mm)', ''))
            self.ui.subLnEdtCatWghtBefTest.setValue(
                safe_float(cat_specs.get('Weight_of_the_catalyst_before the test (g)', '')))
            # self.ui.subLnEdtPrehtTemp.setText(cat_specs.get('Preheat_temperature (°C)', ''))

            # Component Details
            comp_details = test_data.get('component_details', {})
            print(f'Component Details contains: {comp_details}')
            self.ui.Vac_Chamb_Pressure_Sensr_type_Input.setText(comp_details[0].get('Pressure_sensor_type', ''))
            self.ui.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input.setText(comp_details[0].get('Pressure_sensor_number_&_slope_equation', ''))
            self.ui.Vac_Chamb_Pressure_Snsr_range_Input.setText(comp_details[0].get('Pressure_sensor_range', ''))
            self.ui.Vac_Chamb_Pressure_Snsr_IO_Input.setText(comp_details[0].get('Pressure_sensor_input_and_output', ''))
            self.ui.Prop_Tank_Pressure_Sensr_type_Input.setText(comp_details[1].get('Pressure_sensor_type', ''))
            self.ui.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input.setText(comp_details[1].get('Pressure_sensor_number_&_slope_equation', ''))
            self.ui.Prop_Tank_Pressure_Snsr_range_Input.setText(comp_details[1].get('Pressure_sensor_range', ''))
            self.ui.Prop_Tank__Pressure_Snsr_IO_Input.setText(comp_details[1].get('Pressure_sensor_input_and_output', ''))
            self.ui.Thruster_Pressure_Sensr_type_Input.setText(comp_details[2].get('Pressure_sensor_type', ''))
            self.ui.Thruster_Pressure_Snsr_No_Slope_Eqn_Input.setText(comp_details[2].get('Pressure_sensor_number_&_slope_equation', ''))
            self.ui.Thruster_Pressure_Snsr_range_Input.setText(comp_details[2].get('Pressure_sensor_range', ''))
            self.ui.Thruster_Pressure_Snsr_IO_Input.setText(comp_details[2].get('Pressure_sensor_input_and_output', ''))
            self.ui.subLnEdtHtrType.setText(comp_details[3].get('Heater_type', ''))
            self.ui.subLnEdtHtrInpPowerHtr_1.setValue(safe_float(comp_details[3].get('Heater_input_power_Htr_1 (W)', '')))
            self.ui.subLnEdtHtrInpPowerHtr_2.setValue(safe_float(comp_details[3].get('Heater_input_power_Htr_2 (W)', '')))
            self.ui.subLnEdtHtrInpPowerHtr_3.setValue(safe_float(comp_details[3].get('Heater_input_power_Htr_3 (W)', '')))
            self.ui.subLnEdtHtrInpPowerHtr_4.setValue(safe_float(comp_details[3].get('Heater_input_power_Htr_4 (W)', '')))
            self.ui.subLnEdtHtrInpPowerHtr_5.setValue(safe_float(comp_details[3].get('Heater_input_power_Htr_5 (W)', '')))

            # Test Details
            test_details = test_data.get('test_details', {})
            self.ui.subLnEdtPropTnkHtrCtOfTemp.setValue(
                safe_float(test_details.get('Propellant_tank_heater_cut-off_temperature (°C)', '')))
            self.ui.subLnEdtPropTnkHtrRstTemp.setValue(
                safe_float(test_details.get('Propellant_tank_heater_reset_temperature (°C)', '')))
            self.ui.subLblTestProcValue.setPlainText(test_details.get('Test_procedure', ''))

            # Pump Operation
            pump_operation = test_data.get('pump_operation', {})
            self.ui.subLnEdtPmpStrtTime.setTime(QTime.fromString(pump_operation.get('Pump_start_time', ''), 'HH:mm'))
            self.ui.subLnEdtCorrTkBtmTemp.setValue(safe_float(pump_operation.get('Corresponding_tank_bottom_temperature (°C)', '')))
            self.ui.subLnEdtCorrThrstrTemp.setValue(safe_float(pump_operation.get('Corresponding Thruster Temperature (°C)', '')))
            self.ui.subLnEdtCorrTkPressure.setValue(safe_float(pump_operation.get('Corresponding Tank Pressure (Bar)', '')))
            self.ui.subLnEdtCorrThrstrPressure.setValue(safe_float(pump_operation.get('Corresponding Thruster Pressure (Bar)', '')))

            # Suction Valve Operation
            suction_valve_operation = test_data.get('suction_valve_operation', {})
            self.ui.subLnEdtSuctnValveSwtchOnTime.setTime(
                QTime.fromString(suction_valve_operation.get('Suction_valve_open_time', ''), 'HH:mm'))
            self.ui.subLnEdtSuctnCorrTkBtmTemp.setValue(
                safe_float(suction_valve_operation.get('Corresponding_tank_bottom_temperature (°C)', '')))
            self.ui.subLnEdtSuctnCorrThrstrTemp.setValue(
                safe_float(suction_valve_operation.get('Corresponding Thruster Temperature (°C)', '')))
            self.ui.subLnEdtSuctnCorrTkPressure.setValue(
                safe_float(suction_valve_operation.get('Corresponding Tank Pressure (Bar)', '')))
            self.ui.subLnEdtSuctnCorrThrstrPressure.setValue(
                safe_float(suction_valve_operation.get('Corresponding Thruster Pressure (Bar)', '')))

            # Vacuum Creation in Tank
            vacuum_creation_in_tank_valve_on = test_data.get('vacuum_creation_in_tank_valve_on', {})
            self.ui.subLnEdtVacValveSwtchOnTime.setTime(
                QTime.fromString(vacuum_creation_in_tank_valve_on.get('Vacuum_valve_open_time', ''), 'HH:mm'))
            self.ui.subLnEdtVacValveOnCorrTkBtmTemp.setValue(
                safe_float(vacuum_creation_in_tank_valve_on.get('Corresponding_tank_bottom_temperature (°C)', '')))
            self.ui.subLnEdtVacValveOnCorrThrstrTemp.setValue(
                safe_float(vacuum_creation_in_tank_valve_on.get('Corresponding Thruster Temperature (°C)', '')))
            self.ui.subLnEdtVacValveOnCorrTkPressure.setValue(
                safe_float(vacuum_creation_in_tank_valve_on.get('Corresponding Tank Pressure (Bar)', '')))
            self.ui.subLnEdtVacValveOnPressureDropInTank.setValue(
                safe_float(vacuum_creation_in_tank_valve_on.get('Vacuum_valve_open_pressure_drop_in_tank (mbar)', '')))
            vacuum_creation_in_tank_valve_off = test_data.get('vacuum_creation_in_tank_valve_off', {})
            self.ui.subLnEdtVacValveSwtchOffTime.setTime(
                QTime.fromString(vacuum_creation_in_tank_valve_off.get('Vacuum_valve_close_time', ''), 'HH:mm'))
            self.ui.subLnEdtVacValveOffCorrTkBtmTemp.setValue(
                safe_float(vacuum_creation_in_tank_valve_off.get('Corresponding_tank_bottom_temperature (°C)', '')))
            self.ui.subLnEdtVacValveOffCorrThrstrTemp.setValue(
                safe_float(vacuum_creation_in_tank_valve_off.get('Corresponding Thruster Temperature (°C)', '')))
            self.ui.subLnEdtVacValveOffCorrTkPressure.setValue(
                safe_float(vacuum_creation_in_tank_valve_off.get('Corresponding Tank Pressure (Bar)', '')))

            # Heater Information
            heater_info = test_data.get('heater_info', {})
            self.ui.subLnEdtHtrType_2.setText(heater_info.get('Heater_type', ''))

            # Handle heater input values - check both old and new formats for compatibility
            # New format (direct fields)
            voltage = heater_info.get('Heater_input_Voltage', '')
            current = heater_info.get('Heater_input_Current', '')
            wattage = heater_info.get('Heater_input_Wattage', '')

            # Old format (nested in Heater_input object) - fallback for compatibility
            if not voltage and not current and not wattage:
                heater_input = heater_info.get('Heater_input', {})
                if isinstance(heater_input, dict):
                    voltage = heater_input.get('Voltage', '')
                    current = heater_input.get('Current', '')
                    wattage = heater_input.get('Wattage', '')

            self.ui.subLnEdtHtrInpVoltage.setValue(safe_float(voltage))
            self.ui.subLnEdtHtrInpCurrent.setValue(safe_float(current))
            self.ui.subLnEdtHtrInpWattage.setValue(safe_float(wattage))
            self.ui.subLnEdtHtrCtOfTemp.setValue(safe_float(heater_info.get('Heater_cut_off_temp (°C)', '')))
            self.ui.subLnEdtHtrRstTemp.setValue(safe_float(heater_info.get('Heater_reset_temp (°C)', '')))

            # Heater Cycles
            heater_cycles = test_data.get('heater_cycles', {})
            for cycle in range(0, 4):
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOnTime').setTime(
                    QTime.fromString(heater_cycles[cycle].get('switch_on', ), 'HH:mm'))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtONCorspgTankPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_on_corresponding_tank_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtONCorspgThrusterPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_on_corresponding_thruster_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOffTime').setTime(
                    QTime.fromString(heater_cycles[cycle].get('switch_off', ), 'HH:mm'))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOFFCorspgTankPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_off_corresponding_tank_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtHtrSwtOFFCorspgThrusterPressure').setValue(
                    safe_float(heater_cycles[cycle].get('switch_off_corresponding_thruster_pressure', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtMaxTemp').setValue(
                    safe_float(heater_cycles[cycle].get('max_temp', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtLoc').setText(
                    heater_cycles[cycle].get('max_temp_location', ''))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtCorrespgTankBottomTemp').setValue(
                    safe_float(heater_cycles[cycle].get('tank_bottom_temp', '')))
                getattr(self.ui, f'cyc{cycle + 1}SubLnEdtCorrespgValveTemp').setValue(
                    safe_float(heater_cycles[cycle].get('valve_temp', '')))

            # Firing valve Operation
            firing_valve_operation = test_data.get('firing_valve_operation', {})
            self.ui.subLnEdtValveSwtchOnTime.setTime(
                QTime.fromString(firing_valve_operation.get('Firing_valve_open_time', ''), 'HH:mm'))
            self.ui.subLnEdtValveOnCorrTkBtmTemp.setValue(
                safe_float(firing_valve_operation.get('Valve_On_Corresponding_tank_bottom_temperature (°C)', '')))
            self.ui.subLnEdtValveOnCorrPressureTank.setValue(
                safe_float(firing_valve_operation.get('Valve_On_Corresponding_tank_pressure (mbar)', '')))
            self.ui.subLnEdtValveOnPressureDrop.setValue(
                safe_float(firing_valve_operation.get('Valve_On_Pressure_drop_in_tank (mbar)', '')))
            self.ui.subLnEdtValveSwtchOffTime.setTime(
                QTime.fromString(firing_valve_operation.get('Firing_valve_close_time', ''), 'HH:mm'))
            self.ui.subLnEdtValveOffCorrTankBtmTemp.setValue(
                safe_float(firing_valve_operation.get('Valve_Off_Corresponding_tank_bottom_temperature (°C)', '')))

            # Post Test Observations
            post_test_obs = test_data.get('post_test_observations', {})
            self.ui.subLnEdtChmbrNoPostTestObs.setText(post_test_obs.get('Chamber_number', ''))
            self.ui.subLnEdtChmbrLen_2.setValue(safe_float(post_test_obs.get('Chamber_length (mm)', '')))
            self.ui.subLnEdtChmbrIntDia_2.setValue(safe_float(post_test_obs.get('Chamber_internal_diameter (mm)', '')))
            self.ui.subLnEdtChmbrExtDia_2.setValue(safe_float(post_test_obs.get('Chamber_external_diameter (mm)', '')))
            self.ui.subLnEdtRetainerPltCond_2.setText(post_test_obs.get('Retainer_plate_condition', ''))
            self.ui.subLnEdtNote.setPlainText(test_data.get('note', ''))

            # Catalyst Post Analysis
            cat_post_analysis = test_data.get('catalyst_post_analysis', {})
            self.ui.subLnEdtCatDet.setText(cat_post_analysis.get('catalyst_details/specification', ''))
            self.ui.subLnEdtCatColBfr.setText(cat_post_analysis.get('catalyst_color_before', ''))
            self.ui.subLnEdtCatColAft.setText(cat_post_analysis.get('catalyst_color_after', ''))
            self.ui.subLnEdtCatWghtFild.setValue(safe_float(cat_post_analysis.get('catalyst_weight_filled', '')))
            self.ui.subLnEdtCatWghtRecvrd.setValue(safe_float(cat_post_analysis.get('catalyst_weight_recovered', '')))
            self.ui.subLnEdtCatLosPerc.setValue(safe_float(cat_post_analysis.get('catalyst_change_percentage', '')))

            # Propellant Post Analysis
            prop_post_analysis = test_data.get('propellant_post_analysis', {})
            self.ui.subLnEdtPropDet_2.setText(prop_post_analysis.get('Propellant_details/specification', ''))
            self.ui.subLnEdtPropColBef_2.setText(prop_post_analysis.get('Propellant_color_before', ''))
            self.ui.subLnEdtPropColAft_2.setText(prop_post_analysis.get('Propellant_color_after', ''))
            self.ui.subLnEdtPropWghtFild_2.setValue(safe_float(prop_post_analysis.get('Propellant_weight_filled (g)', '')))
            self.ui.subLnEdtPropWghtRecvrd_2.setValue(
                safe_float(prop_post_analysis.get('Propellant_weight_recovered (g)', '')))
            self.ui.subLnEdtPropUsedPerc_2.setValue(safe_float(prop_post_analysis.get('Propellant_used_percentage (%)', '')))
            self.ui.subLnEdtPropRIBefFirg_2.setValue(safe_float(prop_post_analysis.get('Propellant_RI_(before_firing)', '')))
            self.ui.subLnEdtPropRIAftFirg_2.setValue(safe_float(prop_post_analysis.get('Propellant_RI_(after_firing)', '')))
            self.ui.subLnEdtValveOprtnTime.setTime(QTime.fromString(prop_post_analysis.get('Valve_Operation_Time', ''), 'HH:mm'))
            self.ui.subLnEdtFirgDur_2.setValue(safe_float(prop_post_analysis.get('Firing_duration (s)', '')))
            self.ui.subLnEdtApproxMassFlowRate_2.setValue(safe_float(prop_post_analysis.get('Approximate_mass_flow_rate (mg/s)', '')))
            self.ui.subLblPropBefRITable.setNum(safe_float(prop_post_analysis.get('prop_ri_bef_table', '')))
            self.ui.subLblPropAftRITable.setNum(safe_float(prop_post_analysis.get('prop_ri_aft_table', '')))
            self.ui.subLblPropBefConcTable.setNum(safe_float(prop_post_analysis.get('prop_conc_bef_table', '')))
            self.ui.subLblPropAftConcTable.setNum(safe_float(prop_post_analysis.get('prop_conc_aft_table', '')))

            # Update performance data if available
            sys_perf_data = test_data.get('performance_data', {})
            self.ui.subLnEdtChambPressure.setText(sys_perf_data.get('Tank_pressure (mbar)', ''))
            self.ui.subLnEdtVacPressure.setText(sys_perf_data.get('Vacuum_chamber_pressure (mbar)', ''))
            self.ui.subLnEdtChambTemp.setText(sys_perf_data.get('Maximum_temperature (K)', ''))
            self.ui.subLnEdtCharVelo.setText(sys_perf_data.get('Characteristic_velocity (m/s)', ''))
            self.ui.subLnEdtCoefOfThrust.setText(sys_perf_data.get('Coefficient_of_thrust', ''))
            self.ui.subLnEdtBurnTime.setText(sys_perf_data.get('Burn_time (s)', ''))
            self.ui.subLnEdtMassFlowRate.setText(sys_perf_data.get('Mass_flow_rate (mg/s)', ''))
            self.ui.subLnEdtThrust.setText(sys_perf_data.get('Thrust (mN)', ''))
            self.ui.subLnEdtSpcImpulse.setText(sys_perf_data.get('Specific_impulse (s)', ''))
            self.ui.subLnEdtTotImpulse.setText(sys_perf_data.get('Total_impulse (Ns)', ''))
            self.ui.lnEdtInitialPropMass.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_before the test (g)', '')))
            self.ui.lnEdtFinalPropMass.setValue(
                safe_float(prop_specs.get('Weight_of_propellant_after the test (g)', '')))
            self.ui.lnEdtChambPressRangeMin.setValue(
                safe_float(sys_perf_data.get('Chamber_pressure_lower_limit (s)', '')))
            self.ui.lnEdtChambPressRangeMax.setValue(
                safe_float(sys_perf_data.get('Chamber_pressure_upper_limit (s)', '')))
            self.ui.lnEdtVacPressRangeMin.setValue(
                safe_float(sys_perf_data.get('Vacuum_pressure_lower_limit (s)', '')))
            self.ui.lnEdtVacPressRangeMax.setValue(
                safe_float(sys_perf_data.get('Vacuum_pressure_upper_limit (s)', '')))

            # Update test Authorization
            test_authorization = test_data.get('test_authorization', {})
            self.ui.subLnEdtTestConductedBy.setText(test_authorization.get('conducted_by', ''))
            self.ui.subLnEdtReportGeneratedBy.setText(test_authorization.get('generated_by', ''))
            self.ui.subLnEdtReportAuthorizedBy.setText(test_authorization.get('authorized_by', ''))

            # Update pressure relations
            pressure_relations = test_data.get('pressure_relations', {})
            self.ui.lnEdtY0PressureRelation.setText(pressure_relations.get('Vacuum_chamber_pressure_relation', ''))
            self.ui.lnEdtY1PressureRelation.setText(pressure_relations.get('Propellant_tank_pressure_relation', ''))
            self.ui.lnEdtY2PressureRelation.setText(pressure_relations.get('Thruster_chamber_pressure_relation', ''))

            # Force enable the plots button if temperature or pressure data is available
            if 'temperature_data' in test_data or 'pressure_data' in test_data:
                # Force enable the plots button
                self.force_enable_plots_button()

                # Set up multiple timers to keep trying to enable the button
                for delay in [500, 1000, 2000]:
                    QTimer.singleShot(delay, self.force_enable_plots_button)

        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Error updating UI with test data: {str(e)}"
            )
            traceback.print_exc()  # This will print the full error traceback to console

    def handle_plots_mode_change(self, mode_name, mode_index):
        if mode_name == 'Temperature' and self.temperature_data is None:
            QMessageBox.warning(self, 'Load Data', 'Please, load the Temperature Data first!')
            self.mode_plot_settings.set_mode(1)
        elif mode_name == 'Pressure' and self.pressure_data is None:
            QMessageBox.warning(self, 'Load Data', 'Please, load the Pressure Data first!')
            self.mode_plot_settings.set_mode(0)

    def handle_mode_change(self, mode_name, mode_index):
        """Handle theme mode changes."""
        if mode_name == "New Test":
            self.menuBar().clear()
            self.ui.tabWidget.setCurrentIndex(0)
            self.ui.showReport.show()
            self.ui.saveDataToDatabase.show()
            self.ui.lblTestNumber_DataAnalysis_Compare.hide()
        elif mode_name == "Existing Test":
            self.menuBar().clear()
            self.ui.tabWidget.setCurrentIndex(0)
            self.existing_data_dialog.exec()
            self.ui.showReport.show()
            self.ui.saveDataToDatabase.show()
            self.ui.lblTestNumber_DataAnalysis_Compare.hide()
        elif mode_name == "Database":
            if self._initialize_authentication():  # Check return value
                self.ui.tabWidget.setCurrentIndex(1)
                self.ui.lblAim.hide()
                self.ui.testNoFrame.hide()
                self.ui.lblPropellant.hide()
                self.ui.lblCatalyst.hide()
                self.ui.lblCurentSection.setText('Data-Analysis')
                self.ui.showReport.hide()
                self.ui.saveDataToDatabase.hide()
            else:
                self.ui.tabWidget.setCurrentIndex(0)  # Reset to first mode
        elif mode_name == "Temp":
            self.update_content(self.ui.plots)
            self.show_plot_settings('temperature')
            self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.temperature_tab)
            self.ui.lblCurentSection.setText('Temperature Plot')

            self.ui.lnEdtY0PressureRelation.hide()
            self.ui.lnEdtY1PressureRelation.hide()
            self.ui.lnEdtY2PressureRelation.hide()
        elif mode_name == "Presr":
            self.update_content(self.ui.plots)
            self.show_plot_settings('pressure')
            self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.pressure_tab)
            self.ui.lblCurentSection.setText('Pressure Plot')
            self.ui.lnEdtY0PressureRelation.show()
            self.ui.lnEdtY1PressureRelation.show()
            if self.pressure_data is not None and 'y2' in self.pressure_data.columns:
                self.ui.lnEdtY2PressureRelation.show()
        elif mode_name == "Both":
            self.update_content(self.ui.plots)
            self.show_plot_settings('both')
            self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.both_plot_tab)
            self.ui.lblCurentSection.setText('Both Plots')
            self.ui.lnEdtY0PressureRelation.hide()
            self.ui.lnEdtY1PressureRelation.hide()
            self.ui.lnEdtY2PressureRelation.hide()
        elif mode_name == 'Thrust':
            self.update_content(self.ui.plots)
            self.show_plot_settings('thrust')
            self.ui.tabWidgetPlotSettings.setCurrentWidget(self.ui.thrust_tab)
            self.ui.lblCurentSection.setText('Thrust Plot')
            columns = list(self.temperature_data.columns)
            self.ui.comboBoxThrust.clear()
            self.ui.comboBoxThrust.addItems(columns)

    def handle_section_change(self, section_name):
        """Handle section button clicks with animations"""
        try:
            # Update current section label in the top bar
            self.ui.lblCurentSection.setText(section_name)

            # Define a mapping of section names to their keys and widgets
            section_mapping = {
                "Test Prerequisite": ('test_prereq', self.ui.testPrereqSubSections, self.ui.basicInformation),
                "Heater Operation": ('heater_op', self.ui.htrOpSubSections, self.ui.heaterInformation),
                "Valve Operation": ('valve_op', self.ui.valveOpSubSections, self.ui.valveOperation),
                "Post Test Analysis": ('post_test', self.ui.pstTestAnSubSections, self.ui.postTestingObs),
                "PlotWindow": ('plot_controls', self.ui.plotControlsFrame, self.ui.plots),
                "Performance": ('performance', self.ui.perforSubSections, self.ui.performance)
            }

            # Get section info and handle the change
            if section_name in section_mapping:
                section_key, section_widget, content_widget = section_mapping[section_name]
                self.ui.contentStack.setCurrentWidget(content_widget)
                self.toggle_section(section_key, section_widget)

                # Get current page name
                current_page = self.ui.contentStack.currentWidget().objectName()

                # Update UI elements
                self.update_button_states(current_page)

                # Update section header
                if current_page in self.section_headers:
                    self.ui.lblCurentSection.setText(self.section_headers[current_page])
                if section_name == 'PlotWindow':
                    self.ui.lblCurentSection.setText('Temperature Plot')
                    if self.pressure_data is not None and self.temperature_data is None:
                        self.mode_plot_settings.set_mode(1)

            elif section_name == "Load_Data":
                self.ui.contentStack.setCurrentWidget(self.ui.plotDataLoad)

        except Exception as e:
            print(f"Error handling section change: {str(e)}")

    def setupAdvancedAnimation(self):
        """Configure advanced animation settings"""
        try:
            for animation in self.section_animations.values():
                # Use custom easing curve for more natural motion
                custom_curve = QEasingCurve(QEasingCurve.OutCubic)
                custom_curve.setAmplitude(1.0)
                custom_curve.setPeriod(0.3)
                animation.animation.setEasingCurve(custom_curve)
                animation.opacity_animation.setEasingCurve(custom_curve)

                # Connect finished signal to handle cleanup
                animation.animation.finished.connect(
                    lambda w=animation.widget: self.animationFinished(w)
                )

                # Ensure initial state
                animation.widget.setMaximumHeight(16777215)

        except Exception as e:
            print(f"Error setting up advanced animation: {str(e)}")

    def animationFinished(self, widget):
        """Handle cleanup after animation finishes"""
        try:
            if widget.maximumHeight() == 0:
                widget.hide()
                # Reset maximum height for next expansion
                widget.setMaximumHeight(16777215)
            else:
                # Ensure widget is fully expanded
                widget.setMaximumHeight(16777215)
        except Exception as e:
            print(f"Error in animation finished handler: {str(e)}")

    def delete_temp_report_folder(self):
        """Clean up temporary directory"""
        try:
            if hasattr(self, 'temp_dir') and self.temp_dir:
                if os.path.exists(self.temp_dir):
                    shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"Error cleaning up temp directory: {str(e)}")

    def closeEvent(self, event):
        """
        Handle the window close event (X button, Alt+F4, or close() called)
        """
        try:
            # Stop the auto-save timer
            if hasattr(self, 'auto_saver') and self.auto_saver is not None:
                try:
                    self.auto_saver.stop_auto_save()
                except Exception as e:
                    print(f"Error stopping auto-save timer: {str(e)}")

            # Close all matplotlib figures
            try:
                plt.close('all')
            except Exception as e:
                print(f"Error closing matplotlib figures: {str(e)}")

            # Delete temporary plot folders
            if hasattr(self, 'db_handler') and self.db_handler is not None:
                try:
                    self.db_handler.delete_temp_plots_folder()
                except Exception as e:
                    print(f"Error deleting temp plots folder: {str(e)}")
        except Exception as e:
            print(f"Error during application cleanup: {str(e)}")

        # Call parent class closeEvent
        super().closeEvent(event)

    def __del__(self):
        """Cleanup method to ensure all resources are released when the window is destroyed"""
        try:
            # Close all matplotlib figures
            plt.close('all')

            # Stop the auto-save timer
            if hasattr(self, 'auto_saver') and self.auto_saver is not None:
                try:
                    self.auto_saver.stop_auto_save()
                except (RuntimeError, AttributeError, Exception):
                    # Silently ignore errors when accessing auto_saver
                    pass
        except Exception:
            # Silently ignore all errors in destructor
            pass

    def force_enable_plots_button(self):
        """Enable the plots button only if valid data exists"""
        print("\n==== CHECKING BEFORE ENABLING PLOTS BUTTON ====\n")

        # Check if temperature data is valid
        temp_valid = False
        if hasattr(self, 'temperature_data') and self.temperature_data is not None:
            try:
                if isinstance(self.temperature_data, pd.DataFrame):
                    if not self.temperature_data.empty and len(self.temperature_data) > 1:
                        # Check if it has at least one temperature column (not just time)
                        temp_columns = [col for col in self.temperature_data.columns
                                      if col != 'time' and col != 'Time']
                        if len(temp_columns) > 0:
                            temp_valid = True
                            print(f"Valid temperature data found: {self.temperature_data.shape}")
            except Exception as e:
                print(f"Error checking temperature data: {str(e)}")

        # Check if pressure data is valid
        pressure_valid = False
        if hasattr(self, 'pressure_data') and self.pressure_data is not None:
            try:
                if isinstance(self.pressure_data, pd.DataFrame):
                    if not self.pressure_data.empty and len(self.pressure_data) > 1:
                        # Check if it has at least one pressure column (not just time)
                        pressure_columns = [col for col in self.pressure_data.columns
                                          if col != 'time' and col != 'Time']
                        if len(pressure_columns) > 0:
                            pressure_valid = True
                            print(f"Valid pressure data found: {self.pressure_data.shape}")
            except Exception as e:
                print(f"Error checking pressure data: {str(e)}")

        # Only enable the button if we have valid data
        if temp_valid or pressure_valid:
            try:
                print("Enabling plots button due to valid data")
                self.ui.btnPlots.setEnabled(True)
                print(f"Plots button enabled state: {self.ui.btnPlots.isEnabled()}")
            except Exception as e:
                print(f"Error enabling plots button: {str(e)}")
        else:
            try:
                print("No valid data found, disabling plots button")
                self.ui.btnPlots.setEnabled(False)
                print(f"Plots button disabled state: {self.ui.btnPlots.isEnabled()}")
            except Exception as e:
                print(f"Error disabling plots button: {str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    app.setWindowIcon(QIcon("assets/icon.ico"))

    # Create and show splash screen
    splash = CustomSplashScreen()
    splash.show()

    # Create main window
    main_window = MainWindow()
    # Create and start init thread
    init_thread = InitThread()
    init_thread.status_update.connect(splash.update_status)

    # Play the boot sound when the splash screen is shown
    def on_init_finished():
        splash.play_boot_sound()
        # Adding a small delay to ensure sound starts playing before window transition
        QTimer.singleShot(500, lambda: (splash.close(), main_window.show()))

    init_thread.finished.connect(on_init_finished)
    init_thread.start()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()