# -*- mode: python ; coding: utf-8 -*-
import os
from PyInstaller.utils.win32 import versioninfo

block_cipher = None

# Define version info
version_info = versioninfo.VSVersionInfo(
    ffi=versioninfo.FixedFileInfo(
        filevers=(1, 0, 0, 0),    # File version
        prodvers=(1, 0, 0, 0),    # Product version
        mask=0x3f,                # Mask
        flags=0x0,                # Flags
        OS=0x40004,              # Operating System
        fileType=0x1,            # File Type
        subtype=0x0,             # Subtype
        date=(0, 0)              # Creation date
    ),
    kids=[
        versioninfo.StringFileInfo([
            versioninfo.StringTable('040904B0', [
                versioninfo.StringStruct('CompanyName', 'Your Company Name'),
                versioninfo.StringStruct('FileDescription', 'VAPR iDEX Analyzer'),
                versioninfo.StringStruct('FileVersion', '1.0.0'),
                versioninfo.StringStruct('InternalName', 'VAPR_iDEX_Analyzer'),
                versioninfo.StringStruct('LegalCopyright', '© 2024 Your Company Name'),
                versioninfo.StringStruct('OriginalFilename', 'VAPR_iDEX_Analyzer.exe'),
                versioninfo.StringStruct('ProductName', 'VAPR iDEX Analyzer'),
                versioninfo.StringStruct('ProductVersion', '1.0.0')])
        ]),
        versioninfo.VarFileInfo([versioninfo.VarStruct('Translation', [1033, 1200])])
    ]
)

# Ensure icon path is absolute
icon_path = os.path.abspath(os.path.join('assets', 'icon.ico'))

a = Analysis(
    ['gui_main.py'],
    pathex=['D:\\SoftwareDevelopment\\VAPR-iDEX_SOFTWARE_EM-2'],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('gui', 'gui'),
        ('assets', 'assets'),
        ('user_authentication', 'user_authentication'),
        ('assets/sine_wave_animation.gif', 'assets'),
        ('assets/icon.ico', 'assets'),
        ('fonts/DejaVuSansCondensed.ttf', 'fonts'),
        ('fonts/DejaVuSansCondensed-Bold.ttf', 'fonts'),
        ('.env', '.'),
        ('user_authentication/user_auth.json', 'user_authentication'),  # Relative to root
        ('user_authentication/tokens.json', 'user_authentication'),
    ],
    hiddenimports=[
        'matplotlib',
        'numpy',
        'pandas',
        'PIL',
        'PySide6',
        'fpdf',
        'psycopg2',
        'psycopg2._psycopg',
        'psycopg2.pool',
        'psycopg2.extras',
        'pytz',
        'dateutil',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.backends.backend_qt5cairo',
        'PySide6.QtCore',
        'PySide6.QtGui',
        'PySide6.QtWidgets',
        'PySide6.QtNetwork',
        'PySide6.QtSvg',
        'PySide6.QtPrintSupport',
        'openpyxl',
        'cryptography',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'cryptography.hazmat.primitives.hashes',
        'cryptography.hazmat.backends',
        'ratelimit',
        'dotenv',
        'user_authentication',
        'user_authentication.auth',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False
)

# Add matplotlib data files
import matplotlib
matplotlib_data = os.path.join(os.path.dirname(matplotlib.__file__), 'mpl-data')
a.datas += Tree(matplotlib_data, prefix='mpl-data')

pyz = PYZ(
    a.pure,
    a.zipped_data,
    cipher=block_cipher
)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VAPR_iDEX_Analyzer',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Set to False if you don't want console window
    icon=icon_path,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version=version_info,
)
