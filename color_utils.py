"""
Utility functions for color management in plots
"""

from color_manager import color_manager

# Default bright color palette - optimized for visibility
DEFAULT_COLOR_PALETTE = [
            '#511D43',      # Purple
            '#901E3E',      # Maroon
            '#DC2525',      # Red
            '#9BC09C',      # <PERSON> Green
            '#0B1D51',      # Dark Blue
            '#FFC107',      # Yellow
            '#FF7601',      # Orange
            '#4DA8DA',      # Light Blue
            '#16610E',      # Dark Green
            '#A5158C',      # Violet
]

def assign_color(column, color_dict=None, color_palette=None):
    """
    Assign a color to a column name using centralized color manager.

    Args:
        column: The column name to assign a color to
        color_dict: Dictionary mapping column names to colors (deprecated, kept for compatibility)
        color_palette: List of colors to choose from (deprecated, kept for compatibility)

    Returns:
        The color for the column
    """
    # Use centralized color manager for consistent colors across the application
    return color_manager.get_color(column)
