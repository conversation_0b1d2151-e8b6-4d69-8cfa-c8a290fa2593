from PySide6.QtWidgets import QApplication, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt

app = QApplication([])

# Main container widget
window = QWidget()
layout = QHBoxLayout()

# Remove spacing and margins
layout.setSpacing(0)
layout.setContentsMargins(0, 0, 0, 0)

# Add buttons with no border or margin
for i in range(5):
    button = QPushButton(f"Button {i+1}")
    button.setStyleSheet("""
        QPushButton {
        
            margin: 0px;
            padding: 5px 10px;
        }
        QPushButton:hover {
            background-color: #e0e0e0;
        }
    """)
    layout.addWidget(button)

window.setLayout(layout)
window.show()

app.exec()