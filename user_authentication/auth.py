import os
import json
import secrets
import smtplib
import re
import logging
from html import escape
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime, timedelta
from typing import Optional, Dict, Tuple
from ratelimit import limits, sleep_and_retry
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from base64 import urlsafe_b64encode, urlsafe_b64decode
from PySide6.QtCore import QTimer
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QLabel, QLineEdit,
                               QPushButton, QGridLayout, QGroupBox, QWidget)

from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# logger.info(f"SENDER_EMAIL: {os.getenv('SENDER_EMAIL')}, SENDER_PASSWORD: {os.getenv('SENDER_PASSWORD')}")

class EmailConfig:
    """Email configuration for office email system"""
    SMTP_SERVER = "smtp.gmail.com"
    SMTP_PORT = 587
    SENDER_EMAIL = os.getenv("SENDER_EMAIL", "<EMAIL>")
    SENDER_PASSWORD = os.getenv("SENDER_PASSWORD")
    COMPANY_DOMAIN = "manastuspace.com"

# Utility function to derive key using PBKDF2
def derive_key(password: str, salt: bytes = None, iterations: int = 100000) -> Tuple[bytes, bytes]:
    if salt is None:
        salt = secrets.token_bytes(16)  # Generate a 16-byte salt
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,  # Output length of 32 bytes
        salt=salt,
        iterations=iterations,
        backend=default_backend()
    )
    key = kdf.derive(password.encode('utf-8'))
    return key, salt

# Utility function to hash password
def hash_password(password: str) -> str:
    key, salt = derive_key(password)
    # Combine salt and key in a single string for storage (base64-encoded)
    return urlsafe_b64encode(salt + key).decode('ascii')

# Utility function to verify password
def verify_password(stored_hash: str, password: str) -> bool:
    try:
        decoded = urlsafe_b64decode(stored_hash.encode('ascii'))
        salt = decoded[:16]  # First 16 bytes are salt
        key = decoded[16:]  # Remaining bytes are the derived key
        new_key, _ = derive_key(password, salt)
        return key == new_key
    except Exception as e:
        logger.error(f"Password verification failed: {str(e)}")
        return False


class UserAuth:
    """Enhanced user authentication with email verification"""
    VERIFICATION_TOKEN_EXPIRY = timedelta(hours=24)
    PASSWORD_RESET_TOKEN_EXPIRY = timedelta(minutes=30)
    TEMP_PASSWORD_LENGTH = 12
    AUTH_RATE_LIMIT = 5  # Calls per minute
    EMAIL_RATE_LIMIT = 10  # Emails per hour

    def __init__(self, auth_file: str = "user_auth.json", token_file: str = "tokens.json"):
        self.auth_file = auth_file
        self.token_file = token_file
        self.verification_tokens = {}
        self.password_reset_tokens = {}
        self._load_users()
        self._load_tokens()

    def _load_users(self) -> None:
        """Load user data from file"""
        try:
            auth_file_path = os.path.join(os.path.dirname(__file__), self.auth_file)
            if os.path.exists(auth_file_path):
                with open(auth_file_path, 'r') as f:
                    self.users = json.load(f)
            else:
                os.makedirs(os.path.dirname(auth_file_path), exist_ok=True)
                admin_email = os.getenv("ADMIN_EMAIL", f"admin@{EmailConfig.COMPANY_DOMAIN}")
                username = self._extract_username_from_email(admin_email)
                admin_password = secrets.token_urlsafe(16)
                logger.info(f"Creating admin account: {admin_email}, Password: {admin_password}")
                self.users = {
                    username: {
                        "password": hash_password(admin_password),
                        "role": "admin",
                        "email": admin_email,
                        "verified": True
                    }
                }
                self._save_users()
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"Error loading users: {str(e)}")
            self.users = {}

    def _save_users(self) -> None:
        """Save user data to file"""
        try:
            auth_file_path = os.path.join(os.path.dirname(__file__), self.auth_file)
            os.makedirs(os.path.dirname(auth_file_path), exist_ok=True)
            with open(auth_file_path, 'w') as f:
                json.dump(self.users, f)
        except IOError as e:
            logger.error(f"Error saving users: {str(e)}")

    def _load_tokens(self) -> None:
        """Load tokens from file"""
        try:
            token_file_path = os.path.join(os.path.dirname(__file__), self.token_file)
            if os.path.exists(token_file_path):
                with open(token_file_path, 'r') as f:
                    data = json.load(f)
                    self.verification_tokens = data.get("verification_tokens", {})
                    self.password_reset_tokens = data.get("password_reset_tokens", {})
            else:
                os.makedirs(os.path.dirname(token_file_path), exist_ok=True)
                self.verification_tokens = {}
                self.password_reset_tokens = {}
        except (IOError, json.JSONDecodeError) as e:
            logger.error(f"Error loading tokens: {str(e)}")
            self.verification_tokens = {}
            self.password_reset_tokens = {}

    def _save_tokens(self) -> None:
        """Save tokens to file"""
        try:
            token_file_path = os.path.join(os.path.dirname(__file__), self.token_file)
            os.makedirs(os.path.dirname(token_file_path), exist_ok=True)
            with open(token_file_path, 'w') as f:
                json.dump({
                    "verification_tokens": self.verification_tokens,
                    "password_reset_tokens": self.password_reset_tokens
                }, f)
        except IOError as e:
            logger.error(f"Error saving tokens: {str(e)}")

    def _generate_token(self) -> str:
        """Generate a 6-digit verification code"""
        return str(secrets.randbelow(1000000)).zfill(6)

    @sleep_and_retry
    @limits(calls=EMAIL_RATE_LIMIT, period=3600)
    def _send_email(self, to_email: str, subject: str, body: str) -> bool:
        """Send email using office SMTP server"""
        try:
            if not EmailConfig.SENDER_PASSWORD:
                raise ValueError("Email password not configured.")
            msg = MIMEMultipart()
            msg['From'] = EmailConfig.SENDER_EMAIL
            msg['To'] = to_email
            msg['Subject'] = subject
            msg.attach(MIMEText(body, 'html'))
            with smtplib.SMTP(EmailConfig.SMTP_SERVER, EmailConfig.SMTP_PORT) as server:
                server.starttls()
                server.login(EmailConfig.SENDER_EMAIL, EmailConfig.SENDER_PASSWORD)
                server.send_message(msg)
                logger.info(f"Email sent successfully to {to_email}")
                return True
        except smtplib.SMTPAuthenticationError:
            logger.error("Authentication failed. Check email credentials.")
            return False
        except smtplib.SMTPException as e:
            logger.error(f"SMTP error: {str(e)}")
            return False
        except ValueError as e:
            logger.error(f"Configuration error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending email: {str(e)}")
            return False

    @staticmethod
    def test_email_configuration():
        """Test email configuration"""
        try:
            server = smtplib.SMTP(EmailConfig.SMTP_SERVER, EmailConfig.SMTP_PORT)
            server.starttls()
            server.login(EmailConfig.SENDER_EMAIL, EmailConfig.SENDER_PASSWORD)
            logger.info("Email configuration test successful")
            server.quit()
            return True
        except Exception as e:
            logger.error(f"Email configuration test failed: {str(e)}")
            return False

    def _validate_email(self, email: str) -> bool:
        pattern = r"^[a-zA-Z0-9_.+-]+@([a-zA-Z0-9-]+\.)*" + EmailConfig.COMPANY_DOMAIN + r"$"
        return bool(re.match(pattern, email, re.IGNORECASE))

    def _extract_username_from_email(self, email: str) -> str:
        """Extract username from email address"""
        return email.split('@')[0].replace('.', '_').lower()

    @sleep_and_retry
    @limits(calls=AUTH_RATE_LIMIT, period=60)
    def authenticate(self, username: str, password: str) -> Optional[Dict]:
        if username in self.users:
            user = self.users[username]
            stored_hash = user["password"]
            if verify_password(stored_hash, password):
                if user.get("verified", False):
                    return user
                return {"error": "Invalid credentials"}
        return {"error": "Invalid credentials"}

    def add_user(self, email: str, password: str, role: str = "user") -> Tuple[bool, str]:
        """Add new user with email verification"""
        if not self._validate_email(email):
            return False, "Invalid email domain"
        username = self._extract_username_from_email(email)
        if username in self.users:
            return False, "Username already exists"
        token = self._generate_token()
        expiry = datetime.now() + self.VERIFICATION_TOKEN_EXPIRY
        self.verification_tokens[token] = {
            "username": username,
            "email": email,
            "password": hash_password(password),
            "role": role,
            "expiry": expiry.isoformat()
        }
        self._save_tokens()
        verification_code = escape(token)
        email_body = f"""
        <html>
            <body>
                <h2>Welcome to VAPR-iDEX</h2>
                <p>Your verification code is:</p>
                <p><strong>{verification_code}</strong></p>
                <p>Enter this code in the VAPR-iDEX application to complete your registration.</p>
                <p>This code will expire in 24 hours.</p>
            </body>
        </html>
        """
        if self._send_email(email, "Verify Your Email", email_body):
            return True, "Verification email sent"
        return False, "Error sending verification email"

    def verify_email(self, token: str) -> Tuple[bool, str]:
        """Verify email using token"""
        if token not in self.verification_tokens:
            logger.error(f"Invalid token: {token}")
            return False, "Invalid token"
        token_data = self.verification_tokens[token]
        expiry = datetime.fromisoformat(token_data["expiry"])
        if datetime.now() > expiry:
            del self.verification_tokens[token]
            self._save_tokens()
            return False, "Token expired"
        username = token_data["username"]
        self.users[username] = {
            "password": token_data["password"],
            "role": token_data["role"],
            "email": token_data["email"],
            "verified": True
        }
        self._save_users()
        del self.verification_tokens[token]
        self._save_tokens()
        logger.info(f"Verified email for {token_data['username']}")
        return True, "Email verified successfully"

    def initiate_password_reset(self, email: str) -> Tuple[bool, str]:
        """Initiate password reset process"""
        if not self._validate_email(email):
            return False, "Invalid email domain"
        username = None
        for user_id, user_data in self.users.items():
            if user_data["email"] == email:
                username = user_id
                break
        if not username:
            return False, "Email not found"
        token = self._generate_token()
        expiry = datetime.now() + self.PASSWORD_RESET_TOKEN_EXPIRY
        self.password_reset_tokens[token] = {
            "username": username,
            "expiry": expiry.isoformat()
        }
        self._save_tokens()
        email_body = f"""
        <html>
            <body>
                <h2>Password Reset Request</h2>
                <p>Your password reset verification code is:</p>
                <p><strong>{escape(token)}</strong></p>
                <p>Enter this code in the VAPR-iDEX application to reset your password.</p>
                <p>This code will expire in 30 minutes.</p>
                <p>If you didn't request this reset, please ignore this email.</p>
            </body>
        </html>
        """
        if self._send_email(email, "VAPR-iDEX Account Password Reset", email_body):
            return True, "Password reset code sent to your email"
        return False, "Error sending reset email"

    def reset_password(self, token: str, new_password: str) -> Tuple[bool, str]:
        if token not in self.password_reset_tokens:
            logger.error(f"Invalid reset token: {token}")
            return False, "Invalid token"
        token_data = self.password_reset_tokens[token]
        expiry = datetime.fromisoformat(token_data["expiry"])
        if datetime.now() > expiry:
            del self.password_reset_tokens[token]
            self._save_tokens()
            return False, "Token expired"
        print(f"The new pass word is {new_password}")
        print(f'The username is {token_data["username"]}')
        username = token_data["username"]
        self.users[username]["password"] = hash_password(new_password)
        try:
            self._save_users()
            logger.info(f"Password reset for {username}")
        except Exception as e:
            logger.error(f"Failed to save users: {str(e)}")
            return False, "Error saving new password"
        del self.password_reset_tokens[token]
        self._save_tokens()
        return True, "Password reset successfully"

    def delete_user(self, username: str) -> Tuple[bool, str]:
        """Delete a user"""
        if username not in self.users:
            return False, "User not found"
        if self.users[username]["role"] == "admin":
            return False, "Cannot delete admin"
        del self.users[username]
        self._save_users()
        return True, "User deleted"

    def update_role(self, username: str, new_role: str) -> Tuple[bool, str]:
        """Update user role"""
        if username not in self.users:
            return False, "User not found"
        self.users[username]["role"] = new_role
        self._save_users()
        return True, "Role updated"

class LoginDialog(QDialog):
    """Enhanced login dialog with password reset option"""

    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.user_data = None
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Login")
        self.setModal(True)
        self.setMinimumWidth(350)

        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Username or Email")
        layout.addWidget(self.username_edit)

        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Password")
        self.password_edit.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_edit)

        login_button = QPushButton("Login")
        login_button.clicked.connect(self.try_login)
        layout.addWidget(login_button)

        forgot_password = QPushButton("Forgot Password?")
        forgot_password.setStyleSheet("border: none; text-decoration: underline;")
        forgot_password.clicked.connect(self.show_password_reset)
        layout.addWidget(forgot_password)

        self.error_label = QLabel("")
        self.error_label.setStyleSheet("color: red;")
        layout.addWidget(self.error_label)

        self.setLayout(layout)

    def try_login(self):
        username = self.username_edit.text()
        password = self.password_edit.text()
        logger.info(f"Attempting login with username: {username}, password length: {len(password)}")
        if not username or not password:
            self.error_label.setText("Please enter both username and password")
            return

        user_data = self.auth.authenticate(username, password)
        if isinstance(user_data, dict) and "error" in user_data:
            self.error_label.setText(user_data["error"])
        elif user_data:
            self.user_data = user_data
            self.accept()
        else:
            self.error_label.setText("Invalid credentials")
            self.password_edit.clear()

    def show_password_reset(self):
        dialog = PasswordResetDialog(self.auth, self)
        dialog.exec()


class PasswordResetDialog(QDialog):
    """Dialog for password reset request with email verification"""

    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.verification_request = False
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Reset Password")
        self.setModal(True)
        self.setMinimumWidth(300)

        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        email_layout = QVBoxLayout()
        self.email_label = QLabel("Enter your email address:")
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        email_layout.addWidget(self.email_label)
        email_layout.addWidget(self.email_edit)
        layout.addLayout(email_layout)

        self.request_button = QPushButton("Request Verification Code")
        self.request_button.clicked.connect(self.request_verification)
        self.request_button.setStyleSheet(
            """
            QPushButton{
                background-color: #446699;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover{
                background-color: #557799;
            }
            """
        )
        layout.addWidget(self.request_button)

        self.verification_widget = QWidget()
        verification_layout = QVBoxLayout(self.verification_widget)

        self.code_label = QLabel("Enter verification code:")
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("Enter 6-digit code from email")
        verification_layout.addWidget(self.code_label)
        verification_layout.addWidget(self.code_edit)

        self.new_password_label = QLabel("Enter new password:")
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        self.new_password_edit.setPlaceholderText("New password")
        verification_layout.addWidget(self.new_password_label)
        verification_layout.addWidget(self.new_password_edit)

        self.confirm_password_label = QLabel("Confirm new password:")
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("Confirm new password")
        verification_layout.addWidget(self.confirm_password_label)
        verification_layout.addWidget(self.confirm_password_edit)

        self.reset_button = QPushButton("Reset password")
        self.reset_button.clicked.connect(self.reset_password)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #449944;
                color: white;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover{
                background-color: #55aa55;
            }
        """)
        verification_layout.addWidget(self.reset_button)

        layout.addWidget(self.verification_widget)
        self.verification_widget.hide()

        self.status_label = QLabel()
        self.status_label.setWordWrap(True)
        self.status_label.setStyleSheet("padding: 10px;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def request_verification(self):
        email = self.email_edit.text().strip()
        if not email:
            self.show_error("Please enter your email address")
            return

        success, message = self.auth.initiate_password_reset(email)
        if success:
            self.show_success("Verification code sent! Check your email.")
            self.verification_widget.show()
            self.email_edit.setEnabled(False)
            self.request_button.setEnabled(False)
            self.verification_request = True
        else:
            self.show_error(message)

    def reset_password(self):
        if not self.verification_request:
            self.show_error("Please request a verification code first")
            return

        code = self.code_edit.text().strip()
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()

        if not code:
            self.show_error("Please enter the verification code")
            return

        if not new_password or not confirm_password:
            self.show_error("Please enter and confirm your new password")
            return

        if new_password != confirm_password:
            self.show_error("Passwords do not match")
            return

        if not re.match(r"^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$", new_password):
            self.show_error("Password must be at least 8 characters, with letters, numbers, and special characters")
            return

        success, message = self.auth.reset_password(code, new_password)
        if success:
            self.show_success("Password reset successfully!")
            QTimer.singleShot(1500, self.accept)
        else:
            self.show_error(message)

    def show_error(self, message):
        self.status_label.setStyleSheet("color: #ff4444; padding: 10px;")
        self.status_label.setText(message)

    def show_success(self, message):
        self.status_label.setStyleSheet("color: #44aa44; padding: 10px;")
        self.status_label.setText(message)


class UserManagementDialog(QDialog):
    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.setup_ui()
        self.verification_sent = False
        self.new_user_email = None

    def setup_ui(self):
        self.setWindowTitle("User Management")
        self.setModal(True)
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        user_group = QGroupBox("Add New User")
        user_layout = QGridLayout()

        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("Office Email Address")
        user_layout.addWidget(QLabel("Email:"), 0, 0)
        user_layout.addWidget(self.email_edit, 0, 1)

        self.role_edit = QLineEdit()
        self.role_edit.setPlaceholderText("Role (e.g., user, manager)")
        user_layout.addWidget(QLabel("Role:"), 1, 0)
        user_layout.addWidget(self.role_edit, 1, 1)

        user_group.setLayout(user_layout)
        layout.addWidget(user_group)

        self.add_button = QPushButton("Add User")
        self.add_button.clicked.connect(self.add_user)
        layout.addWidget(self.add_button)

        self.verification_group = QGroupBox("Email Verification")
        verification_layout = QGridLayout()

        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("Enter 6-digit verification code")
        verification_layout.addWidget(QLabel("Verification Code:"), 0, 0)
        verification_layout.addWidget(self.code_input, 0, 1)

        self.new_password = QLineEdit()
        self.new_password.setPlaceholderText("Enter new password")
        self.new_password.setEchoMode(QLineEdit.Password)
        verification_layout.addWidget(QLabel("New Password:"), 1, 0)
        verification_layout.addWidget(self.new_password, 1, 1)

        self.confirm_password = QLineEdit()
        self.confirm_password.setPlaceholderText("Confirm new password")
        self.confirm_password.setEchoMode(QLineEdit.Password)
        verification_layout.addWidget(QLabel("Confirm Password:"), 2, 0)
        verification_layout.addWidget(self.confirm_password, 2, 1)

        self.verify_button = QPushButton("Verify & Set Password")
        self.verify_button.clicked.connect(self.verify_and_set_password)
        verification_layout.addWidget(self.verify_button, 3, 0, 1, 2)

        self.verification_group.setLayout(verification_layout)
        self.verification_group.hide()
        layout.addWidget(self.verification_group)

        # User Management Section
        manage_group = QGroupBox("Manage Users")
        manage_layout = QGridLayout()

        self.delete_username = QLineEdit()
        self.delete_username.setPlaceholderText("Username to delete")
        manage_layout.addWidget(QLabel("Delete User:"), 0, 0)
        manage_layout.addWidget(self.delete_username, 0, 1)

        self.delete_button = QPushButton("Delete")
        self.delete_button.clicked.connect(self.delete_user)
        manage_layout.addWidget(self.delete_button, 0, 2)

        self.update_username = QLineEdit()
        self.update_username.setPlaceholderText("Username")
        manage_layout.addWidget(QLabel("Update Role:"), 1, 0)
        manage_layout.addWidget(self.update_username, 1, 1)

        self.new_role = QLineEdit()
        self.new_role.setPlaceholderText("New role")
        manage_layout.addWidget(self.new_role, 1, 2)

        self.update_button = QPushButton("Update")
        self.update_button.clicked.connect(self.update_role)
        manage_layout.addWidget(self.update_button, 1, 3)

        manage_group.setLayout(manage_layout)
        layout.addWidget(manage_group)

        self.status_label = QLabel("")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def add_user(self):
        email = self.email_edit.text()
        role = self.role_edit.text() or "user"

        if not email:
            self.show_status("Please enter an email address", error=True)
            return

        temp_password = secrets.token_urlsafe(self.auth.TEMP_PASSWORD_LENGTH)
        success, message = self.auth.add_user(email, temp_password, role)

        if success:
            self.verification_sent = True
            self.new_user_email = email
            self.show_status("Verification code sent! Check email.", error=False)
            self.verification_group.show()
            self.email_edit.setEnabled(False)
            self.role_edit.setEnabled(False)
            self.add_button.setEnabled(False)
        else:
            self.show_status(message, error=True)

    def verify_and_set_password(self):
        if not self.verification_sent:
            return

        code = self.code_input.text().strip()
        new_pass = self.new_password.text()
        confirm_pass = self.confirm_password.text()

        if not code:
            self.show_status("Please enter verification code", error=True)
            return

        if not new_pass or not confirm_pass:
            self.show_status("Please enter and confirm new password", error=True)
            return

        if new_pass != confirm_pass:
            self.show_status("Passwords do not match", error=True)
            return

        if not re.match(r"^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$", new_pass):
            self.show_status("Password must be at least 8 characters, with letters, numbers, and special characters",
                             error=True)
            return

        success, message = self.auth.verify_email(code)
        if success:
            username = self.auth._extract_username_from_email(self.new_user_email)
            user = self.auth.users.get(username)
            if user:
                user['password'] = self.auth._hash_password(new_pass)
                self.auth._save_users()
                self.show_status("User verified and password set successfully!", error=False)
                QTimer.singleShot(1500, self.accept)
            else:
                self.show_status("Error updating password", error=True)
        else:
            self.show_status(message, error=True)

    def delete_user(self):
        username = self.delete_username.text().strip()
        if not username:
            self.show_status("Please enter a username", error=True)
            return

        success, message = self.auth.delete_user(username)
        self.show_status(message, error=not success)

    def update_role(self):
        username = self.update_username.text().strip()
        new_role = self.new_role.text().strip()
        if not username or not new_role:
            self.show_status("Please enter username and new role", error=True)
            return

        success, message = self.auth.update_role(username, new_role)
        self.show_status(message, error=not success)

    def show_status(self, message, error=False):
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"color: {'red' if error else 'green'};")


class VerificationDialog(QDialog):
    def __init__(self, auth: UserAuth, parent=None):
        super().__init__(parent)
        self.auth = auth
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Verify Email")
        self.setModal(True)
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)

        instructions = QLabel("Enter the 6-digit verification code sent to your email:")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("6-digit Verification Code")
        layout.addWidget(self.code_input)

        verify_button = QPushButton("Verify")
        verify_button.clicked.connect(self.verify_code)
        verify_button.setStyleSheet("background-color: #446699; color: white; padding: 8px;")
        layout.addWidget(verify_button)

        self.status_label = QLabel()
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def verify_code(self):
        code = self.code_input.text().strip()
        if not code:
            self.status_label.setStyleSheet("color: red;")
            self.status_label.setText("Please enter the verification code")
            return

        success, message = self.auth.verify_email(code)
        if success:
            self.status_label.setStyleSheet("color: green;")
            self.status_label.setText("Email verified successfully!")
            QTimer.singleShot(1500, self.accept)
        else:
            self.status_label.setStyleSheet("color: red;")
            self.status_label.setText(message)


if __name__ == "__main__":
    logger.info("Testing Gmail Configuration...")
    auth = UserAuth()

    if UserAuth.test_email_configuration():
        logger.info("Configuration test passed! Sending test email...")
        test_subject = "VAPR-iDEX System Test Email"
        test_body = """
        <html>
            <body>
                <h2>Test Email</h2>
                <p>This is a test email from the VAPR-iDEX system.</p>
                <p>This email is to verify that the email configuration is working correctly.</p>
            </body>
        </html>
        """
        test_recipient = os.getenv("TEST_EMAIL", "<EMAIL>")
        if auth._send_email(test_recipient, test_subject, test_body):
            logger.info("Test email sent successfully")
        else:
            logger.error("Failed to send test email")
    else:
        logger.error("Email configuration test failed")